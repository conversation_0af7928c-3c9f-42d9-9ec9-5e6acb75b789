<template>
  <a-drawer width="500px" :visible="show" :closable="false" :title="task.name || t('campaign.canvas.addTitle')" @before-ok="handleSaveTask"
    @cancel="handleCancelTask">
    <a-form ref="formRef" class="task-create-form" :disabled="!editable" :model="task">
      <a-form-item :label="t('campaign.canvas.canvasId')" field="id">
        <a-input v-model="task.id" :disabled="!isCreate" :placeholder="t('campaign.reminder.input_canvas_id')" />
      </a-form-item>
      <a-form-item :label="t('campaign.canvas.canvasName')" field="name" :rules="[{ required: true, message: '画布名称不能为空' }]">
        <a-input v-model="task.name" :placeholder="t('campaign.reminder.input_canvas_name')" />
      </a-form-item>
      <a-form-item :label="t('campaign.canvas.campaignCode')" field="campaignCode" :rules="[{ required: true, message: '活动编码不能为空' }]">
        <a-input v-model="task.campaignCode" :disabled="owner?.setting?.customizationSetting?.manualCampaignCode != 'ENABLED'" :placeholder="t('campaign.reminder.input_activity_code')" />
      </a-form-item>

      <a-form-item :label="t('campaign.canvas.timePeriod')" field="rangeValue" :disabled="task.status == 'RUNNING'"
        :rules="[{ required: true, message: '时间周期不能为空' }]">
        <a-range-picker v-model="task.rangeValue" show-time
          :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }" format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" @ok="handleRangeTime" />
      </a-form-item>

      <a-form-item :label="t('campaign.canvas.category')" field="category" :rules="[{ required: true, message: '请选择所属分类' }]">
        <a-select v-model="task.category" :placeholder="t('campaign.reminder.selectCanvasCategory')">
          <a-option v-for="item in campaignCategories" :key="item.id" :label="item.name" :value="item.id" />
        </a-select>
      </a-form-item>
      <!-- <a-form-item label="预算年度" field="budgetAnnual">
        <a-input v-model="task.budgetAnnual" placeholder="请输入预算年度" />
      </a-form-item> -->

      <a-form-item :label="t('campaign.canvas.tag')">
        <a-select v-model="task.tags" :placeholder="t('campaign.reminder.campaignTags')" allow-create allow-clear multiple />
      </a-form-item>
      <a-form-item :label="t('campaign.canvas.remark')">
        <a-textarea v-model="task.summary" :placeholder="t('campaign.reminder.input_canvas_remark')" allow-clear />
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { getGroup } from "@/api/group";
import { findCategoryList } from "@/api/category";
import { useBussinessUnitStore } from "@/store";

const {
      proxy: { t }
    } = getCurrentInstance();

const owner = useBussinessUnitStore().marketingCenter;

const props = defineProps({
  editable: Boolean,
  saveTask: Function,
  groupId: String
});

const createFlow = ref(true);

const isCreate = ref(false);
const task = ref({
  setting: {}
});
const formRef = ref(null);
const show = ref(false);
const campaignCategories = ref([]);

const initTask = (type) => {
  task.value = {
    tags: [],
    name: "",
    type,
    groupId: props.groupId,
    category: null,
    endTime: new Date(),
    startTime: new Date(),
    status: "DRAFT",
    setting: {
      type,
      repeatAllow: true
    },
    rangeValue: []
  };
};

const handleSaveTask = async (done) => {
  if (!props.editable) {
    done(true);
    return;
  }
  const res = await formRef.value.validate();
  if (res) {
    done(false);
    return;
  }
  try {
    await props.saveTask(task.value);
    show.value = false;
    done(true);
  } catch (err) {
    done(false);
  }
};
const handleRangeTime = () => {
  task.value.startTime = task.value.rangeValue[0];
  task.value.endTime = task.value.rangeValue[1];
};

const loadCategories = async () => {
  campaignCategories.value = await findCategoryList();
};

const createTask = async (type, templateId) => {
  isCreate.value = true;
  const group = await getGroup(props.groupId);
  initTask(type);
  task.value.groupId = props.groupId;
  task.value.campaignCode = group.code;
  task.value.templateId = templateId;
  show.value = true;
  createFlow.value = true;
  formRef.value.clearValidate();
  await loadCategories();
};

const editTask = async (_task) => {
  isCreate.value = false;
  show.value = true;
  task.value = JSON.parse(JSON.stringify(_task));
  task.value.rangeValue = [task.value.startTime, task.value.endTime];
  createFlow.value = false;
  formRef.value.clearValidate();
  await loadCategories();
};

const handleCancelTask = () => {
  show.value = false;
  setTimeout(() => {
    task.value;
  }, 1000);
};

defineExpose({
  createTask,
  editTask
});
</script>

<style lang="less" scoped>
.task-create-form {
  margin-top: 10px;
  padding: 10px 0px;
  border-radius: 4px;
  background: #ffffff;

  .task-create-item {
    margin: 8px 4px;
  }

  .add-list {
    display: flex;
    align-items: center;
    width: 100%;

    .btn {
      margin-left: 10px;
    }
  }
}
</style>
