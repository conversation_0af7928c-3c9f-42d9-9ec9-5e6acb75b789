* {
    box-sizing: border-box;
}

html,
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-size: 14px;
    background-color: var(--color-bg-1);
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
}

.container {
    .filter-row {
        align-items: stretch;
    }

    .actions {
        margin-top: 20px;
        margin-left: 20px;
        margin-right: 20px;
        height: 60px;
        padding: 14px 20px 14px 0;
        background: var(--color-bg-2);
        text-align: right;
        display: flex;
        font-size: 16px;
        font-weight: bold;
        justify-content: space-between;
        position: relative;

        &:after {
            content: '';
            position: absolute;
            left: 20px;
            right: 20px;
            bottom: 0;
            height: 1px;
            background-color: var(--color-neutral-3);
            ;
        }
    }

    .title {
        font-weight: bold;
        margin: 10px 10px 10px 20px;
    }

    .main {
        height: calc(100vh - 160px);
        width: calc(100% - 40px);
        margin: 0 20px;
        overflow-y: auto;
        overflow-x: hidden;
        box-sizing: border-box;
        background-color: #ffffff;
    }

}

:deep(.arco-table-th) {
    &:last-child {
        .arco-table-th-item-title {
            margin-left: 16px;
        }
    }
}

.echarts-tooltip-diy {
    background: linear-gradient(304.17deg,
            rgba(253, 254, 255, 0.6) -6.04%,
            rgba(244, 247, 252, 0.6) 85.2%) !important;
    border: none !important;
    backdrop-filter: blur(10px) !important;
    /* Note: backdrop-filter has minimal browser support */

    border-radius: 6px !important;

    .content-panel {
        display: flex;
        justify-content: space-between;
        padding: 0 9px;
        background: rgba(255, 255, 255, 0.8);
        width: 164px;
        height: 32px;
        line-height: 32px;
        box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
        border-radius: 4px;
        margin-bottom: 4px;
    }

    .tooltip-title {
        margin: 0 0 10px 0;
    }

    p {
        margin: 0;
    }

    .tooltip-title,
    .tooltip-value {
        font-size: 13px;
        line-height: 15px;
        display: flex;
        align-items: center;
        text-align: right;
        color: #1d2129;
        font-weight: bold;
    }

    .tooltip-item-icon {
        display: inline-block;
        margin-right: 8px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
    }
}

.general-form {
    max-width: 120vh;
}

.general-card {
    border-radius: 4px;
    border: none;
    //padding-top: 20px;

    &>.arco-card-header {
        height: auto;
        padding: 20px;
        border: none;
    }

    &>.arco-card-body {
        padding: 20px;
    }
}

.split-line {
    border-color: rgb(var(--gray-2));
}

.arco-table-cell {
    .circle {
        display: inline-block;
        margin-right: 4px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: rgb(var(--blue-6));

        &.pass {
            background-color: rgb(var(--green-6));
        }
    }
}

.arco-select-option-content {
    width: 100%;
}

.arco-radio-group-button{
    margin-right: 10px;
}
.arco-radio-button-content {
    white-space: nowrap;
}

.arco-pagination {
    justify-content: flex-end;
    margin-top: 20px;
}

.arco-table-filters-bottom {
    display: flex;
}

/* 全局滚动条样式 */
.scrollable {-webkit-overflow-scrolling: touch;}
::-webkit-scrollbar {width: 5px;height: 5px;}
::-webkit-scrollbar-thumb {background-color: rgba(50, 50, 50, 0.3);}
::-webkit-scrollbar-thumb:hover {background-color: rgba(50, 50, 50, 0.6);}
::-webkit-scrollbar-track {background-color: rgba(50, 50, 50, 0.1);}
::-webkit-scrollbar-track:hover {background-color: rgba(50, 50, 50, 0.2);}


.menu-drawer .arco-drawer-header {
  display: none;
}

.arco-table .arco-table-cell{
    min-width: 90px;
}

.tooltip-content {
    white-space: pre-wrap;
}

.light-text {
    color: darkgrey;
}
