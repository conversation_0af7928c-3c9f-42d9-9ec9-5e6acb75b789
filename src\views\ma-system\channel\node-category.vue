<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <a-button v-permission="['ma_menu.system.flow-category.create']" type="primary" @click="addGroup">
          <template #icon>
            <icon-plus />
          </template>
          {{t('global.button.create')}}
        </a-button>
      </template>
      <template #context><span></span></template>
      <template #main>
        <a-table :bordered="false" :data="dataSource" :pagination="false" @page-change="onPageChange">
          <template #columns>
            <a-table-column :title="t('systemSetting.touchpointCategoryManagement.id')" data-index="id" />
            <!-- <a-table-column title="图标" data-index="icon">

          </a-table-column> -->
            <a-table-column :title="t('systemSetting.touchpointCategoryManagement.name')" data-index="name" />
            <!-- <a-table-column title="状态" data-index="status">
            <template #cell="{ record }">
              {{ $moment(record.createdTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
</a-table-column> -->
            <a-table-column :title="t('systemSetting.touchpointCategoryManagement.summary')" data-index="summary" />
            <a-table-column :title="t('systemSetting.touchpointCategoryManagement.createdTime')" data-index="createTime">
              <template #cell="{ record }">
                {{ $moment(record.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </a-table-column>
            <a-table-column :title="t('global.button.operation')" align="center">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.system.flow-category.update']" type="text" size="small"
                  @click="edit(record)">  {{t('global.button.update')}}</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <!-- <capabilitySelectorDlg ref="csDlgRef" :prefix-path="module.editPath" /> -->
      </template>
    </module>
    <!-- 新增分组 -->
    <AddNodeCategoryDlg ref="addNodeCategoryDlgRef" @change="bindData" />
  </div>
</template>

<script>
import { ref, provide, computed,getCurrentInstance } from "vue";

import { useRouter, useRoute } from "vue-router";
import { findNodeCategoryPage } from "@/api/node-category";
// import { Modal } from "@arco-design/web-vue";
// import { capabilityType } from "@/constant/capability";
import { filters } from "@/utils/filter";
import AddNodeCategoryDlg from "@/components/modal-dlg/add-node-category-dlg.vue";

export default {
  components: {
    AddNodeCategoryDlg
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();

    // const userStore = useUserStore();
    const route = useRoute();
    // 路由API
    const router = useRouter();
    // 模块设置
    const module = ref({
      entityIdField: "id",
      entityName: "渠道管理",
      breadcrumb: [
        {
          name: t('menu.system'),
          path: "/system/setting"
        },
        {
         name: t('systemSetting.touchpointCategoryManagement.touchpointCategoryManagement'),
        }
      ],
      editPath: "/system/node-category/edit",
      mainPath: "/system/setting"
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true
    });
    // 过滤设置
    const filter = ref([
      {
        field: "name",
        label: t('systemSetting.touchpointCategoryManagement.name'),
        component: "a-input",
        operate: "like",
        placeholder: t('systemSetting.touchpointCategoryManagement.enterName'),
        comment: true,
        value: ""
      }
    ]);
    // 数据设置
    const entity = ref({});
    const addNodeCategoryDlgRef = ref({});

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    // 查询API
    const bindData = async (expression) => {
      entity.value = await findNodeCategoryPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1
        },
        {
          expression
        }
      );
      pagination.value.total = entity.value.totalElements;
    };

    // 新增数据
    const addGroup = () => {
      addNodeCategoryDlgRef.value.createEdit();
    };

    // 新增数据
    const edit = (item) => {
      addNodeCategoryDlgRef.value.createEdit(item);
    };

    const quit = async () => {
      await router.push({ path: module.value.mainPath });
    };

    const onPageChange = () => {
      bindData();
    };

    const setup = {
      t,
      module,
      entity,
      addGroup,
      addNodeCategoryDlgRef,
      edit,
      // deleteData,
      // csDlgRef,
      // modelData,
      // create,
      filter,
      bindData,
      dataSource,
      pagination,
      quit,
      onPageChange,
      // capabilityType,
      filters
    };
    provide("main", setup);
    return setup;
  }
};
</script>
