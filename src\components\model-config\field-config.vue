<!-- eslint-disable vue/no-mutating-props -->
<template>
  <a-form
    ref="formRef"
    class="field-form"
    layout="vertical"
    :model="field"
    disabled
  >
    <a-form-item
      field="name"
      :label="t('systemSetting.behaviorModel.fieldName')"
      :rules="[
        { required: true, message: t('systemSetting.basicSettings.notAllowNull')  },
        {
          match: /^(?!_)(?!.*?_$)[a-zA-Z0-9_]+$/,
          message: t('systemSetting.behaviorModel.fieldNameTips')
        }
      ]"
      :validate-trigger="['change', 'blur']"
    >
      <a-input
        v-model="field.name"
        :placeholder="t('systemSetting.behaviorModel.enterFieldName')"
        :disabled="!editable"
        @blur="blurChange(field)"
        @change="changeName(field)"
      />
    </a-form-item>
    <a-form-item
      field="aliasName"
      :rules="[{ required: true, message: t('systemSetting.basicSettings.notAllowNull')  }]"
      :validate-trigger="['change', 'input']"
      :label="t('systemSetting.behaviorModel.showName') "
    >
      <a-input
        v-model="field.aliasName"
        :placeholder="t('systemSetting.behaviorModel.enterShowName')"
        :disabled="!editable"
      />
    </a-form-item>
    <a-form-item
      field="type"
      :rules="[{ required: true, message:t('systemSetting.basicSettings.notAllowNull') }]"
      :validate-trigger="['change', 'input']"
      :label="t('systemSetting.behaviorModel.fieldType')"
    >
      <a-select
        v-model="field.type"
        :placeholder="t('systemSetting.behaviorModel.selectFieldType')"
        :disabled="!editable"
      >
        <a-option value="string">{{t('systemSetting.basicSettings.string')}}</a-option>
        <a-option value="integer">{{t('systemSetting.basicSettings.integer')}}</a-option>
        <a-option value="long">{{t('systemSetting.basicSettings.long')}}</a-option>
        <a-option value="double">{{t('systemSetting.basicSettings.double')}}</a-option>
        <a-option value="date">{{t('systemSetting.basicSettings.date')}}</a-option>
        <a-option value="boolean">{{t('systemSetting.basicSettings.boolean')}}</a-option>
        <a-option value="struct">{{t('systemSetting.basicSettings.nested')}}</a-option>
      </a-select>
    </a-form-item>
    <a-form-item field="description" :label="t('systemSetting.behaviorModel.fieldDesc')">
      <a-textarea v-model="field.description" :placeholder="t('systemSetting.behaviorModel.enterFieldDesc')" />
    </a-form-item>
    <a-form-item
      v-if="optionsEnable(field?.type)"
      field="options"
      :label="t('systemSetting.behaviorModel.options')"
    >
      <key-value
        v-model:map="field.options"
        :key-type="field.type"
        :title="t('systemSetting.behaviorModel.options')"
        :columns="[t('systemSetting.behaviorModel.value'), t('systemSetting.behaviorModel.description')]"
      />
    </a-form-item>
    <a-form-item field="optional" :label="t('systemSetting.basicSettings.isNull')">
      <a-switch v-model="field.optional" type="round" :disabled="!editable">
        <template #checked> {{t('systemSetting.basicSettings.yes')}}</template>
        <template #unchecked>  {{t('systemSetting.basicSettings.no')}}</template>
      </a-switch>
    </a-form-item>
    <a-form-item field="array" :label="t('systemSetting.basicSettings.isArray')" :disabled="!editable">
      <a-switch v-model="field.array" type="round">
        <template #checked> {{t('systemSetting.basicSettings.yes')}}</template>
        <template #unchecked>  {{t('systemSetting.basicSettings.no')}}</template>
      </a-switch>
    </a-form-item>
    <a-form-item field="piiData" :label="t('systemSetting.behaviorModel.privacyData')" :disabled="!editable">
      <a-switch v-model="field.piiData" type="round">
        <template #checked> {{t('systemSetting.basicSettings.yes')}}</template>
        <template #unchecked>  {{t('systemSetting.basicSettings.no')}}</template>
      </a-switch>
    </a-form-item>
    <a-form-item field="defaultValue" :label="t('systemSetting.basicSettings.defaultValue')">
      <a-input v-model="field.defaultValue"  :placeholder="t('systemSetting.basicSettings.enterDefaultValue')" />
    </a-form-item>
    <a-form-item field="extras" :label="t('systemSetting.behaviorModel.extras')">
      <key-value
        v-model:map="field.extras"
        :title="t('systemSetting.behaviorModel.extras')"
        :columns="[t('systemSetting.behaviorModel.key'), t('systemSetting.behaviorModel.value')]"
      />
    </a-form-item>
    <slot name="extra" />
  </a-form>
</template>

<script setup>
import { ref ,getCurrentInstance} from "vue";
import { optionsEnable } from "@/utils/filter";
import keyValue from "./key-value.vue";


const {
      proxy: { t }
    } = getCurrentInstance()
defineProps({
  editable: Boolean,
  field: Object,
  blurChange: Function,
  changeName: Function
});

const formRef = ref(null);
</script>

<style scoped lang="less">
.field-form {
  flex: 1;
  padding-left: 30px;
  max-width: 450px;
}
</style>
