<template>
  <module edit>
    <template #main>
      <a-form ref="formRef" layout="vertical" class="general-form" :model="entity">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item :label="t('systemSetting.benefitChannel.channelName')" :rules="[{ required: true, message: t('systemSetting.benefitChannel.enterChannelName') }]" field="name">
                  <a-input v-model="entity.name" :placeholder=" t('systemSetting.benefitChannel.enterChannelName')">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item :label="t('systemSetting.benefitChannel.remark')" field="entity.summary">
                  <a-textarea v-model="entity.summary" :placeholder=" t('systemSetting.benefitChannel.enterSummary')" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-space>
      </a-form>
    </template>
  </module>
</template>

<script>
import { ref, provide,getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";
import { findItem, saveInfo } from "@/api/channel";
import { findCustomerModel } from "@/api/system";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const route = useRoute();
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      entityName:t('systemSetting.benefitChannel.couponChannel'),
      breadcrumb: [
        {
          name: t('menu.system'),
          path: "/system/setting",
        },
        {
          name: t('systemSetting.benefitChannel.benefitManagement'),
          path: "/system/benefit",
        },
        {
          name: t('systemSetting.benefitChannel.couponBenefitManagement'),
          path: "/system/benefit/coupon",
        },
      ],
      mainPath: "/system/benefit",
    });

    const queryValue = route.query.id;
    const isEdit = !!queryValue;

    const formRef = ref(null);

    const entity = ref({
      type: "coupon",
      category: "benefit",
      setting: {
        type: "coupon"
      }
    });

    const bindData = async () => {
      if (isEdit) {
        entity.value = await findItem(queryValue);
        entity.value = entity.value ? entity.value : {};
      }
      findCustomerModel().then((data) => {
        entity.value.customerField = data.pkName;
      });
    };

    const formData = ref({});

    const save = async () => {
      formRef.value.validate((err) => {
        if (!err) {
          saveInfo(entity.value).then(() => {
            router.push({ path: module.value.mainPath });
          });
        }
      });
    };



    const setup = {
      t,
      formRef,
      module,
      entity,
      formData,
      bindData,
      save,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>
