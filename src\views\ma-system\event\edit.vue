<template>
  <module edit>
    <template #main>
      <a-row class="grid-demo" :gutter="20">
        <a-col :span="8">
          <a-form ref="formRef" layout="vertical" :model="entity">
            <a-space direction="vertical" :size="16">
              <a-card class="general-card">
                <template #title> {{ module.entityName }}</template>
                <a-col :span="24">
                  <a-form-item :label="t('systemSetting.behaviorEvent.id')" field="id">
                    <a-input v-model="entity.id" :placeholder="t('systemSetting.behaviorEvent.id')" :disabled="module.isEdit"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :label="t('systemSetting.behaviorEvent.name')" field="name">
                    <a-input v-model="entity.name" :placeholder="t('systemSetting.behaviorEvent.name')">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :label="t('systemSetting.behaviorEvent.campaignId')" field="campaignId">
                    <a-select v-model="entity.modelId" :placeholder="t('systemSetting.behaviorEvent.campaignId')">
                      <a-option v-for="item in modelList" :key="item.id" :value="item.id"
                        :label="item.aliasName"></a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :label="t('systemSetting.behaviorEvent.status')" field="status">
                    <a-switch v-model="entity.status" type="round" class="form-switch" checked-value="ENABLED"
                      unchecked-value="DISABLED">

                      <template #checked> {{t('global.button.enable')}}</template>

                      <template #unchecked> {{t('global.button.disable')}}</template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :label="t('systemSetting.behaviorEvent.summary')" field="summary">
                    <a-textarea v-model="entity.summary" :placeholder="t('systemSetting.behaviorEvent.summary')" />
                  </a-form-item>
                </a-col>
              </a-card>
            </a-space>
          </a-form>
        </a-col>
      </a-row>
    </template>
  </module>
</template>

<script>
import { ref, provide,getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";
import { findBehaviorModelList, findBehaviorEvent, createBehaviorEvent, modifyBehaviorEvent } from "@/api/system";
import { Message } from "@arco-design/web-vue";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()

    const route = useRoute();
    const router = useRouter();
    const queryValue = route.query.id;

    const module = ref({
      entityIdField: "id",
      mainPath: "/system/behavior-event",
      breadcrumb: [{
        name:  t('menu.system'),
        path: '/system/setting'
      }, {
        name:  t('systemSetting.basic.behaviorEvent'),
        path: '/system/behavior-event'
      }, {
        name:  t('systemSetting.behaviorEvent.editbehaviorEvent'),
      }],
      isEdit: !!queryValue
    });

    const entity = ref({
      status: "ENABLED"
    });
    const modelList = ref([]);
    const bindData = async () => {
      if (module.value.isEdit) {
        entity.value = await findBehaviorEvent(queryValue)
      }
      modelList.value = await findBehaviorModelList()
    };
    const quit = () => {
      router.push({ path: module.value.mainPath });
    };

    const save = async () => {
      if (module.value.isEdit) {
        await modifyBehaviorEvent(entity.value);
      } else {
        await createBehaviorEvent(entity.value);
      }
      Message.success(t('global.tips.success.submit'));
      quit();
    };


    const setup = {
      t,
      save,
      route,
      router,
      module,
      entity,
      bindData,
      modelList
    };
    provide("edit", setup);
    return setup;
  },
};
</script>
