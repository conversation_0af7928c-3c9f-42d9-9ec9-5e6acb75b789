/** *by:<EMAIL> on 2022/8/4 0004 */
<template>
  <div
    v-if="dataList"
    id="BICHART"
    class="bi-chart"
    :class="{ 'screen-full': screenfullShow }"
  >
    <a-card v-if="isFilter" class="card-item" hoverable>
      <div class="title-name">
        {{ dataList.name }}
        <a-dropdown v-if="isShowBtn" @select="onSelectDropdown">
          <a-button type="outline" size="mini">···</a-button>
          <template #content>
            <a-doption value="full"
              >{{ screenfullShow ? "退出大屏" : "大屏显示" }}
            </a-doption>
            <a-doption value="pdf">下载PDF</a-doption>
            <a-doption value="edit">编辑仪表板</a-doption>
            <a-doption value="delete">删除仪表板</a-doption>
          </template>
        </a-dropdown>
      </div>
      <!--   搜索   -->
      <a-form
        v-if="filter.length > 0"
        class="filter-form"
        :model="formModel"
        auto-label-width
        label-align="right"
      >
        <a-row :gutter="16">
          <template v-for="(item, index) in filter" :key="index">
            <a-col :span="6">
              <a-form-item :field="item.field" :label="item.label">
                <component
                  :is="item.component"
                  v-model="formModel[item.field]"
                  allow-clear
                  :options="item.operate"
                  :placeholder="item.placeholder"
                ></component>
              </a-form-item>
            </a-col>
          </template>
        </a-row>
      </a-form>
    </a-card>
    <div class="grid-demo">
      <grid-layout
        v-if="dataList.charts.length > 0"
        v-model:layout="dataList.charts"
        :col-num="12"
        :row-height="height / 2"
        :is-draggable="false"
        :is-resizable="false"
        :vertical-compact="false"
        use-css-transforms
      >
        <grid-item
          v-for="item in dataList.charts"
          :key="item.i"
          :static="item.static"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
        >
          <ChartItem :height="(item.h * height) / 2 + 'px'" :data-item="item" />
        </grid-item>
      </grid-layout>
      <a-empty v-else> 暂无数据 </a-empty>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, watch } from "vue";
import {
  getDataList,
  getDataListChart,
  getChartListItem,
  getMaDataList,
  deleteItemChart,
} from "@/api/dashboard";
import useLoading from "@/hooks/loading";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "vue-router";
import html2canvas from "html2canvas";
import JSPDF from "jspdf";
import screenfull from "screenfull";
import debug from "@/utils/env";
import { getSortList } from "../option";
import ChartItem from "../components/chart-item.vue";

export default defineComponent({
  name: "BiChart",
  components: {
    ChartItem,
  },
  props: {
    id: {
      type: [String, Number],
      default: "",
    },
    isFilter: {
      // 是否显示搜索
      type: Boolean,
      default: true,
    },
    isShowBtn: {
      // 是否显示功能
      type: Boolean,
      default: true,
    },
    isGetData: {
      // 是否自动初始化
      type: Boolean,
      default: true,
    },
    height: {
      type: Number,
      default: 60,
    },
  },
  setup(props) {
    // 设置loading效果
    const { loading, setLoading } = useLoading(true);
    const dataList = ref({ charts: [] });
    const router = useRouter();

    // 获取数据
    const onGetDataList = (params) => {
      setLoading(true);
      getDataList(props.id, params)
        .then((res) => {
          // 坐标排序
          const sortData = getSortList(res.charts);
          dataList.value = {
            ...res,
            charts: sortData,
          };
          // 搜索处理
          filterForm(res.filterControls);
        })
        .then(() => {
          setLoading(false);
        });
    };

    // 搜索
    const filter = ref([]);
    const formModel = ref({});
    const filterForm = (res) => {
      res.forEach((item) => {
        let component = "a-input";
        let field = "";
        let placeholder = "";
        const operator = [];
        switch (item.type) {
          case "DATA_INPUT":
            component = "a-input";
            field = item.dataInputSetting.code;
            placeholder = `请输入${item.name}`;
            break;
          case "DATA_PICKER":
            component = item.dataPickerSetting.code
              ? "a-select"
              : "a-range-picker";
            field = item.dataPickerSetting.code || item.datePickerSetting.code;
            if (item.dataPickerSetting.code) {
              for (const key in item.dataPickerSetting.options.dict) {
                operator.push({
                  value: key,
                  label: item.dataPickerSetting.options.dict[key],
                });
              }
            }
            placeholder = `请选择${item.name}`;
            break;
          case "DATA_CASCADE":
            component = "a-tree-select";
            field = item.dataPickerSetting.code;
            placeholder = `请选择${item.name}`;
            break;
        }

        if (item.type === "HIDDEN") {
          return false;
        }

        filter.value.push({
          field,
          label: item.name,
          component,
          operate: operator,
          placeholder,
          value: "",
        });
      });
    };

    // 获取特定数据
    const getItemDataList = (newVal) => {
      getDataListChart(props.id, { values: newVal }).then((res) => {
        const sortData = getSortList(res);
        dataList.value.charts = sortData;
      });
    };

    // 获取报表分析
    const getItemReport = (h = 15) => {
      getChartListItem(props.id).then((res) => {
        dataList.value.charts = [
          {
            data: res,
            id: props.id,
            i: "Q8FNr9sQumWP9vurJfUfen",
            x: 0,
            y: 0,
            w: 12,
            h,
          },
        ];
      });
    };

    // ma获取数据
    const onGetMaDataList = (id) => {
      setLoading(true);
      getMaDataList(id)
        .then((res) => {
          // 坐标排序
          const sortData = getSortList(res.charts);
          dataList.value = {
            ...res,
            charts: sortData,
          };

          // 搜索处理
          filterForm(res.filterControls);
        })
        .then(() => {
          setLoading(false);
        });
    };

    watch(
      formModel.value,
      (newVal, oldVal) => {
        getItemDataList(newVal);
      },
      { deep: true }
    );

    // 功能
    const onSelectDropdown = (val) => {
      switch (val) {
        case "delete":
          onDeleteItemChart();
          break;
        case "edit":
          onEditItem();
          break;
        case "pdf":
          downPdf(dataList.value.name);
          break;
        case "full":
          onChangeScreenfull();
          break;
        default:
          Message.warning("功能构建中");
      }
    };

    // 全屏
    const screenfullShow = ref(false);
    const onChangeScreenfull = () => {
      screenfull.toggle();
      screenfullShow.value = !screenfullShow.value;
    };

    // 删除
    const onDeleteItemChart = () => {
      deleteItemChart(props.id).then((res) => {
        Message.success("删除成功");
      });
    };

    // 编辑
    const onEditItem = () => {
      router.push({ path: "/dashboard/edit", query: { id: props.id } });
    };

    // 下载PDF
    const downPdf = (htmlTitle) => {
      const element = document.getElementById("BICHART");
      html2canvas(element, {
        logging: true,
        useCORS: true,
      }).then(function (canvas) {
        const pdf = new JSPDF("p", "mm", "a4"); // A4纸，纵向
        const ctx = canvas.getContext("2d");
        const a4w = 170;
        const a4h = 257; // A4大小，210mm x 297mm，四边各保留20mm的边距，显示区域170x257
        const imgHeight = Math.floor((a4h * canvas.width) / a4w); // 按A4显示比例换算一页图像的像素高度
        let renderedHeight = 0;

        while (renderedHeight < canvas.height) {
          const page = document.createElement("canvas");
          page.width = canvas.width;
          page.height = Math.min(imgHeight, canvas.height - renderedHeight); // 可能内容不足一页

          // 用getImageData剪裁指定区域，并画到前面创建的canvas对象中
          page
            .getContext("2d")
            .putImageData(
              ctx.getImageData(
                0,
                renderedHeight,
                canvas.width,
                Math.min(imgHeight, canvas.height - renderedHeight)
              ),
              0,
              0
            );
          pdf.addImage(
            page.toDataURL("image/jpeg", 1.0),
            "JPEG",
            10,
            10,
            a4w,
            Math.min(a4h, (a4w * page.height) / page.width)
          ); // 添加图像到页面，保留10mm边距

          renderedHeight += imgHeight;
          if (renderedHeight < canvas.height) {
            pdf.addPage();
          } // 如果后面还有内容，添加一个空页
          // delete page;
        }
        pdf.save(htmlTitle);
      });
    };

    onMounted(() => {
      if (props.isGetData) {
        onGetDataList();
      }
    });

    return {
      dataList,
      filter,
      formModel,
      screenfullShow,
      filterForm,
      onChangeScreenfull,
      onSelectDropdown,
      getItemDataList,
      onGetMaDataList,
      getItemReport,
    };
  },
});
</script>

<style lang="less" scoped>
.bi-chart {
  padding: 16px 20px;

  .title-name {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .grid-demo {
    width: calc(100% + 20px);
    margin: -10px;
  }
  .vue-grid-item:not(.vue-grid-placeholder) {
    background-color: var(--color-bg-1);
    border: 1px solid #999999;
    padding: 16px 16px;
    border: 1px solid var(--color-neutral-3);
    border-radius: var(--border-radius-small);
  }

  .card-item {
    margin-bottom: 16px;
  }

  .filter-form {
    margin-top: 20px;
  }

  &.screen-full {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: var(--color-fill-2);
  }
}
</style>
