<template>
  <div v-if="type == 'config'">
    <p>分支节点，根据流程数据决定继续执行的流程分支</p>
  </div>
  <div v-if="type == 'monitor'">
    <p>接收值表示从上游流程接收到的数据总和；</p>
    <p>成功值表示能够继续执行后续流程的数据总和，</p>
    <p>当流程数据同时满足多个分支条件且分支策略为并行，则后续分支的接收数据总和会大于分支节点的成功值，</p>
    <P>当流程数据不满足任意分支条件且不支持默认分支，则后续分支节点的接收数据总和会小于分支节点的成功值；</P>
  </div>
</template>

<script setup>
const {type} = defineProps(['type'])
</script>

<style  lang="less" scoped>
</style>
