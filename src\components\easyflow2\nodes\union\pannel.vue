<template>
  <div class="easyflow-pannel-union">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">

    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
const props = defineProps(["node"]);
const node = props.node;
const loading = ref(false);
const pannelInject = inject("pannel");
const editEnable = pannelInject.editEnable;
const entity = ref({


});
const save = () => {
  return entity.value;
};


defineExpose({
  save,
});
onMounted(() => {
  Object.assign(entity.value, node.data);
});
</script>


<style lang="less" scoped></style>
