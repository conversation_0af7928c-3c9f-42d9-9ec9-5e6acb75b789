<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { findCommunicateItem } from "@/api/communicate";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (data.capabilityId) {
        const communicate = await findCommunicateItem(data.capabilityId);
        brief.push({ sort: 0, label: "沟通模板", value: communicate.name });
      }
      if (data.budgetSetting && data.budgetSetting.enabled) {
        brief.push({
          sort: 1,
          label: "单价",
          value: data.budgetSetting.budgetValue,
        });
      }
      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'sms' })
    const setup = {
      title: item.name || "短信",
      summary: item.name || "短信节点",
      iconClass: item.icon || "icon-auto-reply",
      nodeClass: "easyflow-node-sms",
      headerColor: item.themeColor|| "#85c0fa",
      headerBgColor: item.themeColor || "#4594f3",
      background: item.background || "#f5faff",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
