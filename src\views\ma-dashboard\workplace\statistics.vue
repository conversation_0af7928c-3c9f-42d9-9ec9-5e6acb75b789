<template>
  <div>
    <module main>
      <template #top>
        <div class="card-list">
          <a-card :title="t('menu.all')">
            <p>{{ dataSummary.TOTAL }}</p>
          </a-card>
          <a-card :title="t('menu.status.executing')">
            <p>{{ dataSummary.RUNNING }}</p>
          </a-card>
          <a-card :title="t('menu.status.paused')">
            <p>{{ dataSummary.PAUSED }}</p>
          </a-card>
          <a-card :title="t('menu.status.completed')">
            <p>{{ dataSummary.FINISHED }}</p>
          </a-card>
          <a-card :title="t('menu.status.pendingApproval')">
            <p>{{ dataSummary.COMMITTED }}</p>
          </a-card>
          <a-card :title="t('menu.status.draft')">
            <p>{{ dataSummary.DRAFT }}</p>
          </a-card>
        </div>
      </template>
      <template #filter></template>
      <template #search></template>
      <template #action><span></span></template>
      <template #main>
        <a-table :data="dataSource" :pagination="false">
          <template #columns>
            <a-table-column :title="t('searchTable.columns.activityNumber')" data-index="groupId" />
            <a-table-column :title="t('searchTable.columns.activityName')" data-index="groupName" />
            <a-table-column :title="t('searchTable.columns.canvasNumber')" data-index="id" />
            <a-table-column :title="t('searchTable.columns.activityCanvas')" data-index="name" />
            <a-table-column :title="t('searchTable.columns.status')" data-index="status" :width="100">
              <template #cell="{ record }">
                <span class="status" :class="record.status">
                  {{ statusFilter(record.status) }}</span>
              </template>
            </a-table-column>
            <a-table-column :title="t('searchTable.columns.activityCategory')" data-index="categoryName" :width="120">
              <template #cell="{ record }">
                {{ record.categoryName }}
              </template>
            </a-table-column>
            <a-table-column :title="t('searchTable.columns.startDate')" data-index="startTime" :width="120">
              <template #cell="{ record }">
                {{ $moment(record.startTime).format("YYYY-MM-DD") }}
              </template>
            </a-table-column>
            <a-table-column :title="t('searchTable.columns.endDate')" data-index="endTime" :width="120">
              <template #cell="{ record }">
                {{ $moment(record.endTime).format("YYYY-MM-DD") }}
              </template>
            </a-table-column>

            <a-table-column :title="t('searchTable.columns.action')" :align="'center'" :width="160">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.workplace.view']" type="text" size="small"
                  @click="viewItem(record)">{{ t('searchTable.operation.view') }}</a-button>
                <a-button v-permission="['ma_menu.workplace.report']" type="text" size="small"
                  @click="reportItem(record)">{{ t('searchTable.operation.report') }}</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
  </div>
</template>

<script>
import { provide, ref, getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Modal } from "@arco-design/web-vue";
import { getCampaignSummary } from "@/api/dashboard";
import { findGroupList } from "@/api/group";
import { getCampaignPage } from "@/api/campaign";
import { campaignStatus } from "@/constant/campaign";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          // name: t('menu.activity')
          name: t('menu.activityMonitoring')
        }
      ],
      mainPath: "/dashboard/workplace",
      viewPath: "/campaign/campaign/view-flow",
      reportPath: "/campaign/report"
    });
    // 过滤设置
    // 过滤设置
    const filter = ref([
      // {
      //   field: "groupId",
      //   label: "活动编号",
      //   component: "a-input",
      //   operate: "like",
      //   placeholder: "请输入活动编号",
      //   value: "",
      // },
      {
        field: "groupId",
        // label: "活动名称",
        label: t('searchTable.form.activityName'),
        component: "a-select",
        dataSource: [],
        allowClear: true,
        allowSearch: true,
        operate: "like",
        // placeholder: "请输入活动名称",
        placeholder: t('searchTable.reminder.input_activity_name'),
        comment: true,
        value: "",
      },
      {
        field: "id",
        // label: "画布编号",
        label: t('searchTable.form.canvasNumber'),
        component: "a-input",
        operate: "like",
        // placeholder: "请输入画布编号",
        placeholder: t('searchTable.reminder.input_canvas_number'),
        comment: true,
        value: "",
      },
      {
        field: "name",
        // label: "活动画布",
        label: t('searchTable.form.activityCanvas'),
        component: "a-input",
        operate: "like",
        // placeholder: "请输入活动画布",
        placeholder: t('searchTable.reminder.input_activity_canvas'),
        comment: true,
        value: "",
      },
      {
        field: "status",
        // label: "状态",
        label: t('searchTable.form.status'),
        component: "a-select",
        operate: "eq",
        dataSource: campaignStatus,
        // placeholder: "请选择状态",
        placeholder: t('searchTable.reminder.select_status'),
        value: "",
      },
      {
        field: "startTime",
        // label: "开始日期",
        label: t('searchTable.form.startDate'),
        component: "a-date-picker",
        operate: "ge",
        // placeholder: "请选择开始日期",
        placeholder: t('searchTable.reminder.select_start_date'),
        value: "",
      },
      {
        field: "endTime",
        // label: "结束日期",
        label: t('searchTable.form.endDate'),
        component: "a-date-picker",
        operate: "lt",
        // placeholder: "请选择结束日期",
        placeholder: t('searchTable.reminder.select_end_date'),
        value: "",
      }
    ]);

    const dataSummary = ref([]);
    const dataSource = ref([]);
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true
    });

    // 获取列表
    const bindData = async (expression) => {
      const pageData = await getCampaignPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
          expression
        }
      );
      dataSource.value = pageData.content;
      pagination.value.total = pageData.totalElements;

      getCampaignSummary().then((res) => {
        dataSummary.value = res.counts;
      });
      findGroupList().then((res) => {
        filter.value[0].dataSource = []
        res.forEach(item => {
          filter.value[0].dataSource.push(
            {
              value: item.id,
              label: item.name
            }
          )
        });
      });


    };

    const viewItem = async (item) => {
      localStorage.setItem(
        "workplaceItem",
        JSON.stringify({
          ...item,
          module: {
            mainPath: "/dashboard/workplace",
            name: t('menu.activityMonitoring'),
          }
        })
      );
      const query = { id: item.id, groupId: item.groupId };
      router.push({ path: module.value.viewPath, query });
    };

    const reportItem = async (item) => {
      localStorage.setItem(
        "workplaceItem",
        JSON.stringify({
          ...item,
          module: {
            mainPath: "/dashboard/workplace",
            // name: "活动监控"
            name: t('menu.activityMonitoring'),
          }
        })
      );
      const query = { id: item.id };
      router.push({ path: module.value.reportPath, query });
    };

    // 删除
    const deleteData = async (id) => {
      Modal.confirm({
        // title: "删除沟通模板",
        title: t("reach.title.deleteReachTemplate"),
        // content: "删除之后数据不可恢复，请确认是否删除?",
        content: t('global.tips.warning.delete'),
        onOk: async () => {
          await deleteContent(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1;
          }
          await bindData();
        }
      });
    };

    const statusFilter = (status) => {
      const item = campaignStatus.find((item) => {
        return item.value === status;
      });
      return item.label || "--";
    };

    const setup = {
      filter,
      module,
      dataSummary,
      dataSource,
      pagination,
      statusFilter,
      bindData,
      viewItem,
      reportItem,
      deleteData
    };
    provide("main", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped>
.card-list {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;
  margin-bottom: 20px;

  .arco-card {
    flex: 1;
    width: 100%;

    &:nth-child(1) {
      margin-right: 50px;
    }
  }

  p {
    font-size: 50px;
    font-weight: bold;
    margin: 0;
    text-align: center;
  }
}

.status {
  &.DRAFT {
    color: rgba(var(--green-6));
  }

  &.COMMITTED {
    color: rgba(var(--lime-6));
  }

  &.APPROVED {
    color: rgba(var(--cyan-6));
  }

  &.REJECTED {
    color: rgba(var(--gold-6));
  }

  &.RUNNING {
    color: rgba(var(--blue-6));
  }

  &.PAUSED {
    color: rgba(var(--orange-6));
  }

  &.FINISHED {
    color: rgba(var(--blue-6));
  }

  &.STOP {
    color: rgba(var(--gray-6));
  }
}
</style>
