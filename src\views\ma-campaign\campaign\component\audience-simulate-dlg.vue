<template>
  <a-modal v-model:visible="visible" width="1000px" cancel-text="关闭">
    <template #title>活动模拟</template>
    <template #footer>
      <a-button type="outline" @click="close">关闭</a-button>
      <a-button v-if="!debuging" type="primary" @click="startDebug">开始</a-button>
      <a-button v-if="debuging" type="primary" @click="stopDebug">结束</a-button>
    </template>
    <a-form ref="dataFormRef" :model="entity">
      <a-form-item label="启用定时器" field="scheduleEnable">
        <a-switch v-model="entity.scheduleEnable" :disabled="debuging" type="round">
          <template #checked>开</template>
          <template #unchecked>关</template>
        </a-switch>
      </a-form-item>
      <a-form-item label="发送沟通消息" field="sendMessage">
        <a-switch v-model="entity.sendMessage" :disabled="debuging" type="round">
          <template #checked>开</template>
          <template #unchecked>关</template>
        </a-switch>
      </a-form-item>
      <a-form-item label="测试人群" field="audienceId" :rules="[{ required: true, message: '测试人群不能为空' }]">
        <div class="audience-select">
          <a-select v-model="entity.audienceId" :disabled="debuging" :allow-clear="true" placeholder="请选择人群"
            allow-search>
            <a-option v-for="item of audiences" :key="item.id" :label="item.name" :value="item.id"></a-option>
          </a-select>
        </div>
      </a-form-item>
    </a-form>

    <SimulateReport ref="simulateReportRef" :flow-id="flowId" />
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import {Modal} from "@arco-design/web-vue";
import { modalCommit } from "@/utils/modal";
import { findAudienceList } from "@/api/audience";
import { simulateCampaign, flowStopDebug } from "@/api/campaign";
import SimulateReport from "./audience-simulate-report.vue"

const visible = ref(false);
const debuging = ref(false);
const entity = ref({
  budgetEnable: false,
  stepOver: false,
  scheduleEnable: false,
  sendMessage: false,
});
const simulateReportRef = ref(null);
const dataFormRef = ref(null);
const audiences = ref([]);
const callback = ref(null);
const flowId = ref(null);
const simAudienceNodes = ref([]);
const debugInstanceId = ref(null);

const simulate = async () => {
  const _audienceIds = {}
  simAudienceNodes.value.forEach(it => {
    _audienceIds[it.nodeId] = it.audienceId
  })
  const request = {
    campaignId: flowId.value,
    setting: {
      breakPoints: [],
      budgetEnable: entity.value.budgetEnable,
      engineType: "DEBUG",
      scheduleEnable: entity.value.scheduleEnable,
      sendMessage: entity.value.sendMessage,
      stepOver: entity.value.stepOver,
      audienceIds: { 'audience_receive_1': entity.value.audienceId }
    },
    triggerType: "DEBUG",
  };

  const inst = await simulateCampaign(request);
  debugInstanceId.value = inst.id;
  debuging.value = true;
  // callback.value(inst.id);
  simulateReportRef.value.toogleMonitor(true, debugInstanceId.value);
}

const startDebug = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    Modal.confirm({
      title: "开始模拟",
      content: "是否开始活动模拟?",
      onOk: async () => {
        await simulate();
      },
    });
  });
};

const stopDebug = async () => {
  Modal.confirm({
    title: "结束模拟",
    content: "是否结束活动模拟?",
    onOk: async () => {
      await flowStopDebug(debugInstanceId.value);
      debuging.value = false;
      simulateReportRef.value.toogleMonitor(false);
    },
  });
};


const show = async (id) => {
  visible.value = true;
  const params = { fields: "name", expression: "usageType eq TEST" };
  flowId.value = id;

  audiences.value = await findAudienceList(params);
  return new Promise((resolve, reject) => {
    callback.value = resolve;
  });
};

const close = () => {
  debuging.value = false;
  simulateReportRef.value.toogleMonitor(false);
  visible.value = false;
}

const toogleSimulate = async (enable, _flowId, instanceId) => {
  if(enable){
    show(_flowId);
  }
  debugInstanceId.value = instanceId
  debuging.value = enable;
  simulateReportRef.value.toogleMonitor(enable, instanceId);
}

defineExpose({
  show,
  toogleSimulate
});
</script>

<style lang="less" scoped>
.audience-list {
  display: block;

  .audience-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    .audience-select {
      width: 300px;
      padding-left: 10px;
    }
  }
}
</style>
