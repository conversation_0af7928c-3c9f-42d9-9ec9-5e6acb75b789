export function filters(
  list?: any,
  id?: any,
  field = "label",
  key = "value",
  defaultValue = ""
) {
  if (list?.length === 0) {
    return "";
  }
  const item = list.find((item: any) => {
    return item[key] === id;
  });
  return item ? item[field] : defaultValue;
}

export function convertFilter(list?: any, label = "label", value = "value") {
  if (list?.length === 0) {
    return [];
  }
  return list.map((it: any) => {
    return { label: it[label], value: it[value] };
  });
}

export function formatCurrency(num: number) {
  return num.toString().replace(/\d+/, function (n) {
    return n.replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
  });
}

export function formatNumber(num: number) {
  return num.toFixed(2).replace(/\d+/, function (n) {
    return n.replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
  });
}

export function optionsEnable(type: string) {
  return ["TEXT", "INTEGER", "LONG", "DOUBLE"].indexOf(type) >= 0;
}

export function isNumber(type: string) {
  return ["INTEGER", "LONG", "DOUBLE"].indexOf(type) >= 0;
}
