<template>
  <a-button type="text" size="small" @click="show()">引用</a-button>
  <a-modal v-model:visible="modelVisible" width="60%" :footer="false">
    <template #title>引用记录</template>
    <div class="reference">
      <a-table ref="table" :bordered="false" :data="dataList" :pagination="false">
        <template #columns>
          <a-table-column title="活动Id" data-index="groupId">
          </a-table-column>
          <a-table-column title="画布Id" data-index="campaignId">
          </a-table-column>
        </template>
      </a-table>

      <a-pagination :total="pagination.total" size="small" :current="pagination.page" :page-size="pagination.size"
        show-total show-jumper show-page-size @change="changePage" @page-size-change="changeSizePage" />
    </div>
  </a-modal>

</template>

<script setup>
import { ref } from "vue";
import { findDependentPage } from "@/api/campaign";

const { id,type } = defineProps(["id","type"]);

const dataList = ref([]);

const modelVisible = ref(false);
// 分页设置
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
  showPageSize: true
});


const getDataList = async () => {
  findDependentPage({
    ...pagination.value,
    page: pagination.value.page - 1,
    expression: `dependentId eq ${id} AND type eq ${type}`
  }).then((res) => {
    dataList.value = res.content;
    pagination.value.total = res.totalElements;
  });
};

// 切换页数
const changePage = (e) => {
  pagination.value.page = e;
  getDataList();
};
// 切换条数
const changeSizePage = (e) => {
  pagination.value.page = 1;
  pagination.value.size = e;
  getDataList();
};

const show = () => {
  modelVisible.value = true;
  // recordRef.value.activeId = item.id;
  // recordRef.value.dataList = [];
  getDataList();
};

defineExpose({
  show
});
</script>

<style lang="less" scoped></style>
