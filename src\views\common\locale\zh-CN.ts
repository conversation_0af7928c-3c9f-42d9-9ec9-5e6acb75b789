export default {
  "init.systemEnvironmentCheck": "系统环境检查",
  "init.shardedStorageConfiguration": "分库存储设置",
  "init.ossStorageConfiguration": "OSS存储设置",
  "init.dataWarehouseConfiguration": "数据仓库设置",
  "init.scheduledTaskConfiguration": "定时任务设置",
  "init.analysisReportConfiguration": "分析报表设置",
  "init.flowConfiguration": "Flow设置",
  "init.deployment": "上线",

  init: {
    button: {
      start: "开始初始化",
      previous: "上一步",
      next: "下一步",
    },
    storageConfiguration: {
      source: "数据源",
      subDatabase: "分库",
      connectionString: "连接字符串"
    },
    ossConfiguration: {
      type: "OSS类型",
      address: "服务地址",
      accessKey: "访问键",
      accessSecret: "访问密钥",
      storageBucket: "存储桶",
      baseDir: "基础目录",
    },
    warehouseConfiguration: {
      type: "数据仓库类型",
      dataModel: "自建数据模型",
      customerModel: "客户模型",
    },
    reminder: {
      start: "当前业务单元尚未初始化，点击下方按钮开始初始化",
      envCheck: "您已完成所有前置系统初始化。",
      storageConfig: "如您需要分库，可选择配置独立存储信息，本产品运行时产生的定时任务数据将为您单独存储。",
      mongoConn: "请输入MongoDB连接字符串",
      esConn: "请输入ElasticSearch连接字符串",
      warehouseConfig: "数据仓库配置",
      ossType: "请输入OSS类型",
      ossAddress: "请输入服务地址",
      accessKey: "请输入访问键",
      accessSecret: "请输入访问密钥",
      storageBucket: "请输入存储桶",
      baseDir: "请输入基础目录",
      datawarehouseType: "请选择数据仓库类型",
      dataModel: "请选择自建数据模型",
      customerModel: "请选择客户模型",
    }
  }
};
