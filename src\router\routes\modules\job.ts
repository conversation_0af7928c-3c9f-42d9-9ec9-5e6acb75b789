export default {
  path: "task",
  name: "task",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.task",
    requiresAuth: true,
    iconFont: "icon-daibandengdaishenhe",
    order: 88,
    parentMenu:true,
  },
  children: [
    {
      path: "list",
      name: "list",
      component: () => import("@/views/ma-job/scheduler/index.vue"),
      meta: {
        type:'menu',
        locale: "menu.task.list",
        requiresAuth: true,
        roles: ["ma_menu.job"],
      },
    },
    {
      path: "view",
      name: "view",
      component: () => import("@/views/ma-job/scheduler/view.vue"),
      meta: {
        locale: "menu.task.view",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
  ],
};
