/**
 * 分组管理接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function createGroup(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/campaign-group`;
  return axios.post(uri, info);
}

export function saveGroup(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/campaign-group`;
  return !info.id ? axios.post(uri, info) : axios.put(uri, info);
}

export function deleteGroup(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/campaign-group/${id}`
  );
}

export function saveGroupList(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/campaign-group/order`;
  return axios.post(uri, info);
}

export function getGroup(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign-group/${id}`);
}

export function findGroupList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign-group/list`, {
    params,
  });
}

export function findGroupPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign-group`, {
    params: {
      ...params,
      ...query,
    },
  });
}
