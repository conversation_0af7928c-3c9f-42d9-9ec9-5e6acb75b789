<template>
  <VCharts
    v-if="renderChart"
    class="chart-card"
    :option="options"
    :autoresize="autoresize"
    :style="{ width, height }"
  />
</template>

<script lang="ts" setup>
import { ref, nextTick } from "vue";
import VCharts from "vue-echarts";

defineProps({
  options: {
    type: Object,
    default() {
      return {};
    },
  },
  autoresize: {
    type: Boolean,
    default: true,
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "100%",
  },
});

const renderChart = ref(false);
// wait container expand
nextTick(() => {
  renderChart.value = true;
});
</script>

<style scoped lang="less">
.chart-card{
  // border: 1px solid;
  padding: 5px;
  border-radius: 5px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}
</style>
