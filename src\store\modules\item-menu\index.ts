import type { RouteLocationNormalized } from "vue-router";
import { defineStore } from "pinia";
import { MenuItemState } from "./types";

const useAppStore = defineStore("menuItem", {
  state: (): MenuItemState => ({
    path: "",
    name: "",
    meta: {},
    localStorageKey: "app-menu-item",
  }),
  actions: {
    loadFromLocalStorage() {
      const item = localStorage.getItem(this.localStorageKey);
      if (item) {
        this.$patch(JSON.parse(item));
      }
    },
    setMenuItem(item: any) {
      localStorage.setItem(this.localStorageKey, JSON.stringify(item));
      this.$patch({
        ...item
      });
    },
  },
});

export default useAppStore;
