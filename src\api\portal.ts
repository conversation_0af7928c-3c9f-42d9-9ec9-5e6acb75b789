/**
 * portal事件接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";
const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function sendSimulateEvent(data: any) {
  return axios.post(`/api/ma-portal/${tenantId}/${buCode}/event/inbound`, data);
}
