import axios from 'axios';
import { tokenManager } from './auth';
import { HttpResponse } from './interceptor';

// 请求参数接口
export interface DraftInfoDTO {
  wechatOfficialAccountCode: string; // 微信公众号编码: funLoopLand - 环游， esports - 华立电竞， card - 卡片嘉年华
  offset?: number; // 偏移量，默认从 0 开始
  count?: number; // 获取数量，默认 10 条数据
}

// 图文内容接口
export interface NewsItem {
  article_type: string; // 文章类型
  title: string; // 标题
  author: string; // 作者
  digest: string; // 说明
  url: string; // 临时地址
}

export interface DraftContent {
  news_item: NewsItem[];
}

// 草稿项接口
export interface DraftItem {
  media_id: string; // 文章唯一标识
  title: string; // 文章标题
  content: DraftContent;
}

// 响应数据接口
export interface DraftInfoData {
  total_count: number; // 总条数
  item_count: number; // 当前页数据条数
  item: DraftItem[];
}

export interface DraftInfoResponse {
  msg: string;
  code: number;
  data: DraftInfoData;
}

// 获取草稿信息（图文）
export async function getDraftInfo(params: DraftInfoDTO) {
  try {
    const token = await tokenManager.getValidToken();
    // console.log('获取到token:', token);

    const response = await axios.post('/api/wechat/official/accounts/getDraftInfo', params, {
      headers: {
        'token': token,
        'Content-Type': 'application/json',
      },
    });

    //console.log('获取草稿信息响应:', response);

    // 检查响应是否存在（注意：axios拦截器已经返回了response.data）
    if (!response) {
      throw new Error('获取草稿信息失败: 服务器响应格式错误');
    }

    return response;
  } catch (error: any) {
    console.error('获取草稿信息失败:', error);

    // 如果是token相关错误，清除token缓存
    if (error?.message && error.message.includes('token')) {
      tokenManager.clearToken();
    }

    // 如果是axios错误，提供更详细的错误信息
    if (error?.response) {
      console.error('服务器响应错误:', error.response.status, error.response.data);
      throw new Error(`获取草稿信息失败: 服务器错误 ${error.response.status}`);
    } else if (error?.request) {
      console.error('网络请求失败:', error.request);
      throw new Error('获取草稿信息失败: 网络连接错误');
    } else {
      throw error;
    }
  }
}