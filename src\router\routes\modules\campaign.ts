export default {
  path: "campaign",
  name: "campaign",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.campaign",
    requiresAuth: true,
    iconFont: "icon-app-campaign",
    order: 30,
    parentMenu:true,
  },
  children: [
    {
      path: "group",
      name: "CampaignGroup",
      component: () => import("@/views/ma-campaign/campaign/group.vue"),
      meta: {
        type:'menu',
        locale: "menu.campaign.campaign",
        requiresAuth: true,
        roles: ["ma_menu.campaign_group"],
      },
    },
    {
      path: "campaign",
      name: "Campaign",
      component: () => import("@/views/ma-campaign/campaign/campaign.vue"),
      meta: {
        // type:'menu',
        locale: "menu.campaign.flow",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["ma_menu.campaign"],
      },
    },
    {
      path: "campaign/edit-audience",
      name: "AudienceCampaignEdit",
      component: () => import("@/views/ma-campaign/campaign/audience-campaign-edit.vue"),
      meta: {
        locale: "menu.campaign.edit",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
    // {
    //   path: "campaign/edit-event",
    //   name: "EventCampaignEdit",
    //   component: () => import("@/views/ma-campaign/campaign/event-campaign-edit.vue"),
    //   meta: {
    //     locale: "menu.campaign.edit",
    //     requiresAuth: true,
    //     hideInMenu: true,
    //     roles: ["*"],
    //   },
    // },
    {
      path: "campaign/edit-flow",
      name: "CampaignEdit",
      component: () => import("@/views/ma-campaign/campaign/flow-campaign-edit.vue"),
      meta: {
        locale: "menu.campaign.edit",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
    {
      path: "campaign/edit-wecom",
      name: "WeComCampaignEdit",
      component: () => import("@/views/ma-campaign/campaign/wecom-campaign-edit.vue"),
      meta: {
        locale: "menu.campaign.edit",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
    {
      path: "campaign/view-flow",
      name: "CampaignView",
      component: () => import("@/views/ma-campaign/campaign/view-flow.vue"),
      meta: {
        locale: "menu.campaign.view",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
    {
      path: "campaign/budget",
      name: "CampaignBudget",
      component: () => import("@/views/ma-campaign/campaign/budget.vue"),
      meta: {
        // type:'menu',
        locale: "menu.campaign.budget",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
    {
      path: "report",
      name: "CampaignReport",
      component: () => import("@/views/ma-campaign/campaign/report.vue"),
      meta: {
        locale: "menu.dashboard.edit",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },

  ],
};