<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      brief.push({ sort: 0, label: "去重字段", value: data.uniqueField });

      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'unique' })
    const setup = {
      title: item.name || "去重",
      summary: item.name || "去重节点",
      iconClass: item.icon || "icon-quzhong",
      nodeClass: "easyflow-node-end",
      headerColor: item.themeColor|| "#fba980",
      headerBgColor: item.themeColor || "#fba980",
      background: item.background || "#fff9ef",
      getBrief
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
