export default {
  path: "report",
  name: "communicateReport",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.report",
    requiresAuth: true,
    icon: "icon-dashboard",
    order: 47,
  },
  children: [
    {
      path: "list",
      name: "reportList",
      component: () => import("@/views/ma-report/report/index.vue"),
      meta: {
        locale: "menu.report.report",
        requiresAuth: true,
        roles: ["ma_menu.report.reach"],
      },
    }
  ],
};
