export default {
  path: "calendar",
  name: "calendar",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.calendar",
    requiresAuth: true,
    iconFont: "icon-riqi",
    order: 20,
    parentMenu:true,
  },
  children: [
    {
      path: "calendar",
      name: "Calendar",
      component: () => import("@/views/ma-calendar/calendar/main.vue"),
      meta: {
        type:'menu',
        locale: "menu.calendar",
        requiresAuth: true,
        roles: ["ma_menu.calendar"],
      },
    },
  ],
};
