<template>
  <div class="easyflow-toolbar">
    <div class="ex-toolbar">
      <div class="debug-toolbar" v-if="isDebug">
        <!-- <div class="btn-line">
          <a-button type="text" class="notactive" @click="handleExport">
          <template #icon>
          <span class="button-icon iconfont icon-last" title="执行到结束"></span>
          </template>
</a-button>
<a-button type="text" class="notactive" @click="debugStepOver">
  <template #icon>
              <span class="button-icon iconfont icon-debug-step-over"></span>
            </template>
</a-button>
<a-button type="text" class="notactive" title="停止" @click="debugStop">
  <template #icon>
              <span class="button-icon iconfont icon-flow-stop"></span>
            </template>
</a-button>
</div> -->
        <a-button type="text" class="notactive" @click="simulateEvent">事件</a-button>
        <div class="progress"></div>
      </div>
    </div>
    <div class="main-toolbar">
      <!-- <div v-if="editEnable" class="toolbar-button">
      <a-button type="text" class="notactive" @click="handleExport">
      <template #icon>
      <span class="button-icon iconfont icon-daochu"></span>
      </template>
      <template #default>导出</template>
      </a-button>
      </div> -->
      <div v-if="simulateBtnShow" class="toolbar-button">
        <a-button type="text" :class="isDebug ? 'active' : 'notactive'" @click="handleSimulate">
          <template #icon>
            <span class="button-icon iconfont icon-node"></span>
          </template>
          <template #default>模拟</template>
        </a-button>
      </div>
      <div v-if="editEnable" class="toolbar-button">
        <a-button type="text" class="notactive" @click="handleValidate">
          <template #icon>
            <span class="button-icon iconfont icon-order"></span>
          </template>
          <template #default>校验</template>
        </a-button>
        <!-- <a-button type="text" class="notactive" @click="handleImport">
          <template #icon>
            <span class="button-icon iconfont icon-order"></span>
          </template>
          <template #default>导入</template>
        </a-button> -->
      </div>
      <div v-if="monitorBtnShow" class="toolbar-button">
        <a-button type="text" :class="isMonitor ? 'active' : 'notactive'"
          @click="handleMonitor(flow.flowId, 'STANDARD')">
          <template #icon>
            <span class="button-icon iconfont icon-node"></span>
          </template>
          <template #default>监控</template>
        </a-button>
      </div>
      <!-- <div class="toolbar-button">
      <span class="button-icon iconfont icon-refresh"></span>
      <span>刷新</span>
    </div> -->
      <!-- <div class="toolbar-button">
      <span class="button-icon iconfont icon-rubbish"></span>
      <span>删除</span>
    </div> -->
    </div>
  </div>
</template>

<script setup>
import { ref, inject } from "vue";
import {
  Modal,
  Popover,
  Dropdown,
  Button,
  Switch,
  Doption,
  Popconfirm,
} from "@arco-design/web-vue";
import EventBus from "@/utils/eventbus";
import { simulateEnabled } from "@/constant/campaign";

const flow = inject("flow");
const editEnable = flow.editEnable;

const isDebug = ref(flow.isDebug);
const isMonitor = ref(false);
const simulateBtnShow = simulateEnabled(flow.entity.status) && editEnable.value;
const monitorBtnShow = ref(flow.started ? flow.started : false);

const handleExport = () => {
  const json = flow.getData();
  console.log("导出数据：", JSON.stringify(json));
};


const debugStepOver = async () => { };

const debugStop = async () => {
  Modal.confirm({
    title: "结束模拟",
    content: "是否结束营销流程模拟?",
    onOk: async () => {
      EventBus.eventbus.emit("debugStop", {});
      isMonitor.value = false;
      monitorBtnShow.value = flow.started ? flow.started : false;
    },
  });
};

const handleMonitor = async (instanceId, engineType) => {
  isMonitor.value = !isMonitor.value;
  await flow.toogleMonitor(isMonitor.value, instanceId, engineType);
};

/**
 * 切换模拟状态
 */
const handleSimulate = async () => {
  if (isDebug.value) {
    await debugStop();
    // handleMonitor();
  } else {
    if (!await flow.validate(true)) {
      return;
    }
    const instanceId = await flow.simulate();
    if (instanceId) {
      monitorBtnShow.value = false;
      handleMonitor(instanceId, "DEBUG");
    }
  }
};

/**
 * 模拟事件
 */
const simulateEvent = async () => {
  await flow.simulateEvent();
};

const handleValidate = async () => {
  await flow.validate();
};
</script>

<style lang="less" scoped>
.easyflow-toolbar {
  z-index: 1;
  position: absolute;
  bottom: 24px;
  right: 24px;
  border-radius: 6px;
  background: #ffffff;
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.09);

  .main-toolbar {
    height: 42px;
    display: flex;
    font-size: 14px;
    font-weight: 400;
    overflow: hidden;
    color: #333333;
    align-items: center;
    font-family: PingFang SC;

    .toolbar-button {
      padding: 0 4px;
      cursor: pointer;
      box-sizing: border-box;
      color: black;

      .button-icon {
        &::before {
          font-size: 14px;
          color: #999999;
        }
      }

      &:not(:last-child) {
        border-right: 1px solid #eeeeee;
      }
    }
  }

  .ex-toolbar {
    .debug-toolbar {
      height: 42px;
      display: flex;
      border-bottom: 1px solid;
      font-size: 14px;
      font-weight: 400;
      overflow: hidden;
      color: #333333;
      align-items: center;
      font-family: PingFang SC;
      background-color: #eee;

      .btn-line {
        width: 100%;
        padding: 0 10px;
      }

      .progress {
        background-color: red;

        &::after {
          position: absolute;
          top: 0;
          right: -1px;
          display: block;
          width: 1px;
          height: 100%;
          content: "";
        }
      }
    }
  }

  .active {
    color: white;
    border-radius: 6px;
    background-color: rgb(var(--primary-6));

    .button-icon::before {
      color: white;
    }
  }

  .notactive {
    color: black;
  }
}
</style>
