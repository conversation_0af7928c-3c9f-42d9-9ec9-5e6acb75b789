<template>
  <div class="easyflow-pannel-event">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item key="behaviorId" label="选择事件" :rules="[{ match: /one/, message: 'must select one' }]">
        <a-select v-model="entity.behaviorId" placeholder="请选择事件" allow-search :loading="loading" :filter-option="false"
          :show-extra-options="false" @search="handleSearchBehavior" @change="handelChangeBehavior">
          <a-option v-for="item of behaviors" :key="item.id" :value="item.id">{{
            item.name
          }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="时间范围" :rules="[{ required: true, message: '不能为空' }]">
        <a-range-picker v-model="eventTimeRange" show-time :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
          format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" @ok="handleEventRangeTime" />
      </a-form-item>
      <a-form-item label="事件超时时间" :content-flex="false">
        <a-input-number v-model="delay.day" class="delay-input" placeholder="请输入延时天数" :min="0">
          <template #append> 天 </template>
        </a-input-number>
        <a-input-number v-model="delay.hour" class="delay-input" placeholder="请输入延时小时数" :min="0" :max="24">
          <template #append> 小时 </template>
        </a-input-number>
        <a-input-number v-model="delay.minute" class="delay-input" placeholder="请输入延时分钟数" :min="0" :max="59">
          <template #append> 分钟 </template>
        </a-input-number>
        <a-input-number v-model="delay.second" class="delay-input" placeholder="请输入延时秒数" :min="0" :max="59">
          <template #append> 秒 </template>
        </a-input-number>
      </a-form-item>
      <a-form-item label="事件路径" :content-flex="false">
        <template v-for="(item, index) in entity.branches" :key="index">
          <a-row class="branch-item" :gutter="24">
            <a-col :span="14" class="item-label">
              {{ item.name }}
            </a-col>
            <a-col :span="10">
              <a-radio-group v-model="item.type" type="button">
                <a-radio border value="EVENT">事件</a-radio>
                <a-radio border value="TIMEOUT">超时</a-radio>
              </a-radio-group>
            </a-col>
          </a-row>
        </template>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { findBehaviorEventList } from "@/api/behavior";
import { getDurationString, getDelay } from "@/utils/date";

const props = defineProps(["node", "connections", "easyflow"]);
const { node } = props;
const { connections } = props;
const { easyflow } = props;
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;
const entity = ref({
  branches: [],
  schedule: {},
});
const delay = ref({
  day: 0,
  hour: 0,
  minute: 0,
  second: 0,
});
const eventTimeRange = ref(null);
const behaviors = ref(null); // 行为模型列表
const loading = ref(false);

const handleSearchBehavior = async (value) => {
  const params = { fields: "name,modelId" };
  if (value) {
    params.expression = `name like ${value}`;
  }
  behaviors.value = await findBehaviorEventList(params);
};
const handelChangeBehavior = (id) => {
  const behavior = behaviors.value.find((it) => it.id == id);
  if (behavior) {
    entity.value.behaviorModelId = behavior.modelId;
  }
};

const handleEventRangeTime = () => {
  entity.value.eventStartTime = eventTimeRange.value[0];
  entity.value.eventEndTime = eventTimeRange.value[1];
};

const refreshBranchs = () => {
  // 去除无效分支
  if (connections.outgoings) {
    entity.value.branches = connections.outgoings.map((it) => {
      const found = entity.value.branches.find((item, index) => {
        return item.outgoing == it.id;
      });
      if (found) {
        found.name = it.name;
        return found;
      }
      return {
        name: it.name,
        outgoing: it.id,
        type: "EVENT",
      };

    });
  } else {
    entity.value.branches = [];
  }
};
const save = () => {
  entity.value.schedule.type = "DELAY";
  entity.value.schedule.duration = getDurationString(delay.value);
  return entity.value;
};

defineExpose({
  save,
});

onMounted(() => {
  Object.assign(entity.value, node.data);
  handleSearchBehavior();
  refreshBranchs();
  eventTimeRange.value = [
    entity.value.eventStartTime,
    entity.value.eventEndTime,
  ];
  if (entity.value.schedule?.duration) {
    delay.value = getDelay(entity.value.schedule.duration);
  }
});
</script>


<style lang="less" scoped></style>
