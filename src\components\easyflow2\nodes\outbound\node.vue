<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode: BasicNode,
  },
  inject: {
    node: {
      default: null,
    },
  },
  mixins: [mixin],
  data() {
    return {};
  },
  setup() {
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'outbound' })
    const setup = {
      title: item.name || "输出",
      summary: item.name || "输出节点",
      iconClass: item.icon || "icon-output",
      nodeClass: "easyflow-node-end",
      headerColor: item.themeColor|| "#00b42a",
      headerBgColor: item.themeColor || "#00b42a",
      background: item.background || "#e8ffea",
    };
    provide("node", setup);
    return setup;
  },
};
</script>
