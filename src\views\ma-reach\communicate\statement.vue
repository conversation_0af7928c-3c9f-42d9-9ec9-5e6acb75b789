/**
*by:<EMAIL> on 2022/8/12 0004
*/
<template>
  <module view>
    <template #main>
      <div class="tab-content">
        <div class="item-text w100"><span>模板名称:</span>{{ dataInfo.name }}</div>
        <div class="item-text">
          <span>沟通渠道:</span>{{ channelFilter(dataInfo.channelId) }}
        </div>
        <div class="item-text"><span>模板类型:</span>{{ typeFilter(dataInfo.type) }}
        </div>
        <div class="item-text w100"><span>模板:</span>{{ dataInfo.content }}</div>

        <!-- 状态 -->
        <div class="item-status">
          <a-tag v-if="dataInfo.status" color="blue">{{ statusFilter(dataInfo.status) }}</a-tag>
          <a-tag v-else color="orangered">{{ statusFilter(dataInfo.status) }}</a-tag>
        </div>
      </div>
      <MaBi :id="id" ref="maBiRef" :height="90" :is-filter="false" :is-get-data="false" />
    </template>
  </module>
</template>

<script>
import { ref, onMounted, provide } from 'vue';
import MaBi from '@/components/bi/chart/index.vue'
import { useRoute } from "vue-router";
import { useBussinessUnitStore } from "@/store";
import {
  findCommunicateItem,
  findChannelList
} from "@/api/communicate";
import { filters } from "@/utils/filter"
import { capabilityType } from "@/constant/capability"

export default {
  name: 'TemplateStatement',
  components: {
    MaBi
  },
  setup() {
    const module = ref({
      entityIdField: "id",
      entityName: `设计活动流程图`,
      breadcrumb: [
        {
          name: "模板管理",
          path: "/reach/communicate/template",
        },
        {
          name: "报表",
        },
      ],
      mainPath: "/reach/communicate/template"
    })
    const route = useRoute();
    const maBiRef = ref(null)
    const userTc = useBussinessUnitStore()
    const buCode = userTc.currentBussinessUnit?.code;
    const tenantId = userTc.currentBussinessUnit?.tenantId;
    const id = ref(`${tenantId}_${buCode}`)
    const dataInfo = ref({})
    const channelList = ref([])
    const typeFilter = (v) => {
      return filters(capabilityType, v, 'title', 'type')
    }
    const statusFilter = (v) => {
      switch (v) {
        case true:
          return "启用";
        case false:
          return "禁用";
        default:
          return "禁用";
      }
    }
    const channelFilter = (_id) => {
      const item = channelList.value.find(it => {
        return it.id === _id
      })
      return item?.name || ''
    }
    onMounted(async () => {
      channelList.value = await findChannelList()
      maBiRef.value.getItemDataList({ capabilityId: route.query.id })
      dataInfo.value = await findCommunicateItem(route.query.id)
    })

    const setup = {
      id,
      maBiRef,
      dataInfo,
      channelList,
      channelFilter,
      typeFilter,
      statusFilter,
      module
    }

    provide("view", setup);
    return setup;
  },
}
</script>

<style lang="less" scoped>
.tab-content {
  margin: 20px 20px 10px;
  background-color: rgb(247 248 250);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  color: #999999;
  padding: 20px;
  font-size: 14px;
  position: relative;

  .item-status {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .item-text {
    width: 50%;
    line-height: 35px;
  }

  .w100 {
    width: 100%;
  }

  span {
    color: #333333;
    margin-right: 10px;
  }
}
</style>
