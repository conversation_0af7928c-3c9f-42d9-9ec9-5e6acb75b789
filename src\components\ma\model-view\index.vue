<template>
  <!-- 选择用户 -->
  <a-modal v-model:visible="modelVisible" title="选择用户" @cancel="handleCancel" @ok="handleOk">
    <a-select v-model="customerId" allow-search placeholder="选择模拟用户">
      <a-option v-for="item in customerList" :key="item.id" :value="item.id"
        :label="item.payload.wx_nickName"></a-option>
    </a-select>
  </a-modal>
  <!-- 显示 -->
  <!-- 微信客服消息 -->
  <template v-if="viewType === 'WECHAT_TEMPLATE_MSG' || viewType === 'WECHAT_OFFICIAL_ACCOUNT'">
    <div class="right-body">
      <div class="right-phone border-phone">
        <div v-if="entity" class="right-tabs-item">
          <div class="title-top" v-html="viewText"></div>
          <div class="footer-body">
            <span>查看详情</span>
            <i class="iconfont icon-arrow-right-bold"></i>
          </div>
        </div>
      </div>
    </div>
  </template>

  <!-- 微信客服消息 -->
  <template v-else-if="viewType === 'WECHAT_CUSTOMER_MSG'">
    <div class="right-body">
      <div class="right-phone border-phone">
        <div v-if="entity" class="right-tabs-customer">
          <template v-if="entity.materialType === 'image'">
            <img class="top-img" :src="materialTypeItem.serverUrl" alt="" />
          </template>
          <template v-if="entity.materialType === 'link' && materialTypeItem">
            <a :href="materialTypeItem.url" class="a-item">
              <img class="top-img" :src="materialTypeItem.picUrl" alt="" />
              <div class="title-top">{{ materialTypeItem.title }}</div>
              <div class="title-desc">{{ materialTypeItem.description }}</div>
            </a>
          </template>
          <template v-if="entity.materialType === 'text'">
            <div class="title-top" v-html="viewText"></div>
          </template>
          <template v-if="entity.materialType === 'voice' && materialTypeItem">
            <audio :src="materialTypeItem.serverUrl" style="width: 100%" controls autoplay></audio>
          </template>
          <template v-if="entity.materialType === 'video' && materialTypeItem">
            <video :src="materialTypeItem.serverUrl" style="width: 100%; height: 150px" controls autoplay></video>
          </template>

          <!-- 图文 -->
          <template v-if="entity.materialType === 'news'">
            <template v-for="(item, index) in materialTypeItem" :key="item.thumb_media_id">
              <a v-if="index === 0" :href="materialTypeItem.url" class="a-item-news">
                <img class="top-img" :src="item.thumb_url" alt="" />
                <div class="title-top">{{ item.title }}</div>
              </a>
              <a v-else :href="materialTypeItem.url" class="a-item-news-bottom">
                <div class="title-top">{{ item.title }}</div>
                <img class="top-img" :src="item.thumb_url" alt="" />
              </a>
            </template>
          </template>
          <!-- 小程序 -->
          <template v-if="entity.materialType === 'miniprogram_page'">
            <div class="miniprogram-page">
              <i class="iconfont icon-mini"></i>
              点击我打开{{ materialTypeItem.name }}小程序
            </div>
          </template>
        </div>
      </div>
    </div>
  </template>

  <!-- 短信模板显示 -->
  <template v-else-if="viewType === 'SMS' || viewType === 'MMS'">
    <div class="right-body">
      <div class="right-phone-sms border-phone">
        <div v-if="entity" class="right-tabs-item" v-html="viewText"></div>
      </div>
    </div>
  </template>

  <!-- 邮件模板显示 -->
  <template v-else-if="viewType === 'EMAIL'">
    <div class="right-body">
      <div class="right-phone-email border-phone">
        <div v-if="entity" class="right-tabs-item-email">
          <div class="email-img">{{ entity.subject.slice(0, 1) }}</div>
          <div class="right-body-email">
            <div class="title-name">{{ entity.subject }}</div>
            <div class="title-desc" v-html="viewText"></div>
          </div>
        </div>
      </div>
    </div>
  </template>
</template>

<script>
import { defineComponent, watch, ref, onMounted, nextTick } from "vue";
import { findCustomerPage } from "@/api/audience";
import { getCommunicatePreview } from "@/api/communicate";
import { findCustomerModel, findBehaviorModelList } from "@/api/system";
import moment from "moment";

export default defineComponent({
  name: "",
  props: {
    /**
     *  WECHAT_TEMPLATE_MSG 微信模板消息
     *  WECHAT_CUSTOMER_MSG 微信客服消息
     *  SMS 短信
     *  MMS 彩信
     *  EMAIL 邮件
     */
    viewType: {
      type: String,
      default: "WECHAT_TEMPLATE_MSG",
    },
    entity: {
      type: [Object, Array],
      default: null,
    },
    wechatTemplates: {
      type: [Object, Array],
      default: null,
    },
  },
  setup(props, context) {
    const modelVisible = ref(false);
    const showPhoneView = ref(false);
    const customerId = ref("");

    // 获取用户列表
    const customerList = ref([]);

    // 关闭显示
    const handleCancel = () => {
      modelVisible.value = !modelVisible.value;
    };

    const getUserList = async () => {
      const list = await findCustomerPage();
      customerList.value = list.content;
      handleCancel();
    };

    // 确定显示
    const handleOk = () => {
      getCommunicatePreview({
        commId: props.entity.templateId,
        customer: customerId.value,
      }).then((res) => {
        showPhoneView.value = true;
      });
    };

    // 获取数据源数据
    const initFields = (data, parent) => {
      if (!Array.isArray(data)) return;
      data.forEach((i) => {
        if (parent) {
          i.path = `${parent.path}.${i.name}`;
        } else {
          i.path = i.name;
        }
        i.name = `${i.name}      「${i.aliasName}」`;

        if (i.fields && i.fields.length > 0) {
          return initFields(i.fields, i);
        }
      });
    };

    // 匹配符合条件的数据
    const isBehaviorFindArr = (arr, item) => {
      if (!arr) return null;
      for (const obj of arr) {
        if (obj.path === item) {
          return obj.defaultValue;
        }
        if (obj?.fields?.length > 0) {
          const ret = isBehaviorFindArr(obj.fields, item);
          if (ret) return ret;
        }
      }
      return null;
    };

    // 处理显示
    const viewText = ref("");
    const materialTypeItem = ref("");
    watch(
      () => props.entity,
      (count, prevCount) => {
        setViewHtml(count);
      },
      { deep: true }
    );
    onMounted(() => {
      nextTick(() => {
        setViewHtml(props.entity);
      })
    })
    const setViewHtml = (count = props.entity) => {
      const viewContent = count?.viewContent || count?.setting?.template
      viewText.value = viewContent?.replace(/(\${(.*?)})|({{(.*?)\.DATA}})/g, (match, p1, p2, p3, p4) => {

        let key = ''
        // 如果是 ${name} 形式的匹配
        if (p1 !== undefined && p2 !== undefined) {
          key = p2
        }
        // 如果是 {{first.DATA}} 形式的匹配
        if (p3 !== undefined && p4 !== undefined) {
          key = p4
        }
        const mapping = count?.mapping || count?.setting?.templateMappings
        const item = mapping.find(x => { return x.source === key })
        if (!item) {
          return match;
        }

        if (['CUSTOMER', 'BEHAVIOR'].includes(item.type)) {
          return item.defaultValue || '--'
        }
        if (['CONSTANT'].includes(item.type)) {
          return item.value || '--'
        }
        if (['UDF'].includes(item.type)) {
          return item.value || '--'
        }
        if (['FLOW'].includes(item.type)) {
          return match || '--'
        }
        if (['SYSTEM'].includes(item.type)) {
          if (item.value == 'CURRENT_TIME') {
            return moment().format('YYYY-MM-DD HH:mm:ss')
          }
          if (item.value == 'CURRENT_DATE') {
            return moment().format('YYYY-MM-DD')
          }
          return item.value || '--'
        }

        return key || '--'
      });

      // 处理数据中的 /n
      viewText.value = viewText.value?.replace(/\\n/g, '<br>')

    };

    return {
      modelVisible,
      showPhoneView,
      handleCancel,
      customerId,
      handleOk,
      getUserList,
      customerList,
      materialTypeItem,
      viewText,
      setViewHtml,
    };
  },
});
</script>

<style lang="less" scoped>
.right-body {
  width: 280px;
  max-width: 280px;
  min-width: 280px;
  height: 520px;
  background: url("@/assets/images/iphone.png") no-repeat center;
  background-size: 100% 100%;
  padding: 12px 20px 20px;
  margin: 20px;
}

.border-phone {
  border-radius: 30px;
  overflow: hidden;
  padding: 60px 5px;
}

.right-phone {
  width: 100%;
  height: 100%;
  background: url("@/assets/images/phone-bg.png") no-repeat top;
  background-size: 100% 100%;
}

.right-tabs-item {
  background-color: #ffffff;
  border-radius: 3px;
  padding: 10px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);

  .title-top {
    font-size: 14px;
    color: #333333;
    margin-bottom: 5px;
    white-space: pre-line;
  }

  .title-desc {
    font-size: 12px;
    color: #999999;
    margin-bottom: 15px;
  }

  .p-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    color: #999999;

    .left-title {
      width: 65px;
    }

    .right-desc {
      color: #3370ff;
    }
  }

  .footer-body {
    border-top: 1px solid #efefef;
    margin: 0 -10px;
    padding: 10px 10px 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    i {
      color: #999999;
    }
  }
}

.right-phone-sms {
  width: 100%;
  height: 100%;
  background: url("@/assets/images/phone-sms.jpg") no-repeat top;
  background-size: 100% 100%;
  padding: 100px 15px 70px;

  .right-tabs-item {
    width: 100%;
    background-color: #ffffff;
    border-radius: 3px;
    padding: 10px;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
    font-size: 12px;
    line-height: 18px;
    max-height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    white-space: pre-line;
  }
}

.right-tabs-customer {
  background-color: #ffffff;
  border-radius: 3px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .a-item {
    text-decoration: none;
    margin-bottom: 20px;
    display: block;
  }

  .top-img {
    width: 100%;
    height: 150px;
    object-fit: cover;
  }

  .title-top {
    font-size: 14px;
    color: #333333;
    margin-bottom: 5px;
    padding: 5px 5px 0;
  }

  .title-desc {
    font-size: 12px;
    color: #999999;
    padding: 5px 5px 10px;
  }
}

.right-phone-email {
  width: 100%;
  height: 100%;
  background: url("@/assets/images/phone-email.png") no-repeat top;
  background-size: cover;
  padding-top: 100px;

  .right-body-email {
    width: calc(100% - 50px);
    white-space: normal;

    :deep(p) {
      margin: 0;
      white-space: normal;
      width: 100%;
      word-wrap: break-word;
    }
  }
}

.right-tabs-item-email {
  display: flex;
  align-items: flex-start;

  .email-img {
    background-color: #efefef;
    width: 40px;
    height: 40px;
    min-width: 40px;
    border-radius: 40px;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    color: #666666;
  }

  .title-name {
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .title-desc {
    font-size: 14px;
    color: #999999;
    word-break: break-all;
  }
}

.miniprogram-page {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 3px;
  background-color: #ffffff;
  color: rgb(4, 74, 252);
}

.a-item-news {
  position: relative;

  .top-img {
    display: block;
  }

  .title-top {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.4);
    color: #ffffff;
    margin: 0;
    padding: 5px;
  }
}

.a-item-news-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #efefef;

  .title-top {
    color: #333333;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .top-img {
    width: 50px;
    height: 50px;
  }
}
</style>
