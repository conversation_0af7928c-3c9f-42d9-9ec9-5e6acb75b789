<template>
  <a-grid
    :cols="24"
    :row-gap="16"
    class="panel"
    style="box-sizing: border-box; padding-bottom: 10px"
  >
    <a-grid-item
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/288b89194e657603ff40db39e8072640.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="总活动"
          :value="dataList.totalCampaignCount"
          :value-from="0"
          animation
          show-group-separator
        >
          <template #suffix>
            <span class="unit">{{ $t("workplace.pecs") }}</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/fdc66b07224cdf18843c6076c2587eb5.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="进行中"
          :value="dataList.runningCampaignCount"
          :value-from="0"
          animation
          show-group-separator
        >
          <template #suffix>
            <span class="unit">{{ $t("workplace.pecs") }}</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/77d74c9a245adeae1ec7fb5d4539738d.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="待执行"
          :value="dataList.readyCampaignCount"
          :value-from="0"
          animation
          show-group-separator
        >
          <template #suffix>
            <span class="unit">{{ $t("workplace.pecs") }}</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }"
      style="border-right: none"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/c8b36e26d2b9bb5dbf9b74dd6d7345af.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="已完成"
          :value="dataList.finishCampaignCount"
          :value-from="0"
          animation
        >
          <template #suffix>
            <span class="unit">{{ $t("workplace.pecs") }}</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
  </a-grid>
  <!-- <a-grid :cols="20">
    <a-grid-item
      class="panel-center"
      :span="{ xs: 8, sm: 8, md: 8, lg: 8, xl: 8, xxl: 4 }"
      style="border-right: none"
    >
      <a-space class="tab-bg">
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/c8b36e26d2b9bb5dbf9b74dd6d7345af.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="预算总额"
          :value="
            dataList.totalBudget > 10000
              ? dataList.totalBudget / 10000
              : dataList.totalBudget
          "
          :value-from="0"
          animation
        >
          <template #suffix>
            <span class="unit"
              >{{ dataList.totalBudget > 10000 ? "万" : "" }}元</span
            >
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-center"
      :span="{ xs: 8, sm: 8, md: 8, lg: 8, xl: 8, xxl: 4 }"
      style="border-right: none"
    >
      <a-space class="tab-bg">
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/c8b36e26d2b9bb5dbf9b74dd6d7345af.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="计划预算"
          :value="
            dataList.planBudget > 10000
              ? dataList.planBudget / 10000
              : dataList.planBudget
          "
          :value-from="0"
          animation
        >
          <template #suffix>
            <span class="unit"
              >{{ dataList.planBudget > 10000 ? "万" : "" }}元</span
            >
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-center"
      :span="{ xs: 8, sm: 8, md: 8, lg: 8, xl: 8, xxl: 4 }"
      style="border-right: none"
    >
      <a-space class="tab-bg">
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/c8b36e26d2b9bb5dbf9b74dd6d7345af.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="使用预算"
          :value="
            dataList.spendBudget > 10000
              ? dataList.spendBudget / 10000
              : dataList.spendBudget
          "
          :value-from="0"
          animation
        >
          <template #suffix>
            <span class="unit"
              >{{ dataList.spendBudget > 10000 ? "万" : "" }}元</span
            >
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-center"
      :span="{ xs: 8, sm: 8, md: 8, lg: 8, xl: 8, xxl: 4 }"
      style="border-right: none"
    >
      <a-space class="tab-bg">
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/c8b36e26d2b9bb5dbf9b74dd6d7345af.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="确定预算"
          :value="
            dataList.confirmBudget > 10000
              ? dataList.confirmBudget / 10000
              : dataList.confirmBudget
          "
          :value-from="0"
          animation
        >
          <template #suffix>
            <span class="unit"
              >{{ dataList.confirmBudget > 10000 ? "万" : "" }}元</span
            >
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
      class="panel-center"
      :span="{ xs: 8, sm: 8, md: 8, lg: 8, xl: 8, xxl: 4 }"
      style="border-right: none"
    >
      <a-space class="tab-bg">
        <a-avatar :size="54" class="col-avatar">
          <img
            alt="avatar"
            src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/c8b36e26d2b9bb5dbf9b74dd6d7345af.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
          title="预算余额"
          :value="
            dataList.reserveBudget > 10000
              ? dataList.reserveBudget / 10000
              : dataList.reserveBudget
          "
          :value-from="0"
          animation
        >
          <template #suffix>
            <span class="unit"
              >{{ dataList.reserveBudget > 10000 ? "万" : "" }}元</span
            >
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
  </a-grid> -->
</template>

<script>
export default {
  props: {
    dataList: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    }
  },
  setup() {
    return {};
  }
};
</script>

<style lang="less" scoped>
.arco-grid.panel {
  margin-bottom: 0;
  padding: 16px 20px 0 20px;
}
.panel-col {
  padding-left: 43px;
  border-right: 1px solid rgb(var(--gray-2));
}
.col-avatar {
  margin-right: 12px;
  background-color: var(--color-fill-2);
}
.up-icon {
  color: rgb(var(--red-6));
}
.unit {
  margin-left: 8px;
  color: rgb(var(--gray-8));
  font-size: 12px;
}
:deep(.panel-border) {
  margin: 4px 0 0 0;
}
.tab-bg {
  padding: 10px 50px 10px 20px;
  background-color: #f7f8fa;
  border-radius: 100px;
  ::v-deep .arco-avatar-image {
    background-color: #ffffff;
  }
}
.panel-center {
  margin: 20px auto;
}
::v-deep .arco-statistic-title {
  margin-bottom: -4px;
}
</style>
