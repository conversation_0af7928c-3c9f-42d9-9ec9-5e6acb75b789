import { createRouter, createWebHashHistory } from "vue-router";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css";

import DefaultLayout from "@/layout/default-layout.vue";
import appRoutes from "./routes";
import createRouteGuard from "./guard";

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "/",
      redirect: import.meta.env.PROD ? "/load" : "login",
    },
    {
      path: "/login",
      name: "login",
      component: () => import("@/views/common/login/index.vue"),
      meta: {
        requiresAuth: false,
      },
    },
    {
      name: "load",
      path: "/load",
      component: () => import("@/views/common/load/index.vue"),
    },
    {
      name: "init",
      path: "/init",
      component: () => import("@/views/common/init/index.vue"),
    },
    {
      name: "root",
      path: "/",
      component: DefaultLayout,
      children: appRoutes,
    },
    {
      path: "/:pathMatch(.*)*",
      name: "notFound",
      component: () => import("@/views/common/not-found/index.vue"),
    },
  ],
  scrollBehavior() {
    return { top: 0 };
  },
});

createRouteGuard(router);
export default router;
