<template>
  <a-modal v-model:visible="modelVisible" title="监控数据详情" modal-class="monitor-detail-model" width="50%" cancel-text="关闭"
    @cancel="handleCancel">
    <!-- <a-modal v-model:visible="modelVisible" title="监控数据详情" modal-class="monitor-detail-model" width="50%" ok-text="重试"
    :ok-button-props="{ disabled: dataForm.status != 'ERROR' }" cancel-text="关闭" @ok="handleOk" @cancel="handleCancel"> -->
    <a-form v-if="modelVisible" :model="dataForm">
      <a-form-item label="客户id">
        {{ dataForm.payloadId }}
      </a-form-item>
      <a-form-item label="客户姓名">
        {{ customer.payload?.wx_nickName }}
      </a-form-item>
      <a-form-item label="执行时间">
        {{ dataForm.timestamp }}
      </a-form-item>
      <a-form-item label="执行结果">
        {{ dataForm.statusText }}
      </a-form-item>
      <a-form-item label="消息">
        {{ dataForm.message }}
      </a-form-item>
    </a-form>

    <a-table ref="tableRef" row-key="id" class="monitor-table" :bordered="false" :data="traceData.content"
        :pagination="false">
        <template #columns>
          <a-table-column title="活动节点" data-index="taskId">
            <template #cell="{ record }">
              {{record.taskId}}
            </template>
          </a-table-column>
          <a-table-column title="执行时间" data-index="timestamp">
            <template #cell="{ record }">
              {{record.timestamp}}
            </template>
          </a-table-column>
          <a-table-column title="执行状态" data-index="status">
            <template #cell="{ record }">
              {{record.status}}
            </template>
          </a-table-column>
          <a-table-column title="消息" data-index="message">
            <template #cell="{ record }">
              {{record.message}}
            </template>
          </a-table-column>
        </template>
      </a-table>

  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { retryTaskCustomer } from "@/api/campaign";
import { getCustomerItem } from "@/api/audience";
import { getMonitorPage } from "@/api/monitor";

const modelVisible = ref(false);
const dataForm = ref({});
const customer = ref({});
const traceData = ref([]);

const findTraceData = async (factId) => {
  const expression = `factId eq ${factId}`;
  traceData.value = await getMonitorPage(
    {
      sort: `timestamp,ASC`
    },
    { expression }
  );
};

const show = async (data) => {
  dataForm.value = data;
  // FIXME 这里要用动态模型的字段
  customer.value = await getCustomerItem(data.payloadId);
  await findTraceData(data.factId);
  modelVisible.value = true;
};
const handleOk = async () => {
  await retryTaskCustomer(dataForm.value.factId);
  modelVisible.value = false;
};

const handleCancel = () => {
  modelVisible.value = false;
};

defineExpose({ show });
</script>

<style lang="less" scoped>
.monitor-detail-model {}
</style>
