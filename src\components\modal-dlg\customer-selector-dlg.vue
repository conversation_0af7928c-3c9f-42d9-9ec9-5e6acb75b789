<template>
  <a-modal v-model:visible="visible" title="模拟发送" @cancel="handleCancel" @before-ok="handleOk">
    <a-form :model="form">
      <div class="tag-name">用户查询</div>
      <a-tree-select v-model="form.customerField" style="margin-bottom: 10px" :data="customerModel.fields" allow-clear
        :field-names="treeFieldStruct" placeholder="请选择用户字段">
      </a-tree-select>
      <a-input-search v-model="form.value" style="margin-bottom: 10px" placeholder="请输入用户字段值"
        :loading="loadingData" search-button @press-enter="searchCustomer" @search="searchCustomer" />
      <div v-if="customerList?.length > 0" class="user-list">
        <div class="user-title">查询结果</div>
        <div>
          <a-table v-if="customerList.length > 0" ref="table" v-model:selectedKeys="selectedKeys" row-key="_id" :columns="columnsTable"
            :pagination="pagination" :row-selection="rowSelection" :bordered="false" :column-resizable="true"
            :data="customerList" />
          <a-result v-else status="500" subtitle="客户模型没有可显示字段"></a-result>
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { treeFieldStruct } from "@/constant/common"
import { findCustomerPage } from "@/api/audience";
import { Message } from "@arco-design/web-vue";

const visible = ref(false);

const selectedKeys = ref([]);
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const pagination = { pageSize: 5 }
const form = reactive({
  value: '',
  customerField: ''
});

const customerModel = ref({});
const columnsTable = ref([]);
let callback;

const show = async (_customerModel) => {
  customerModel.value = _customerModel;
  customerModel.value.fields.forEach(item => {
    columnsTable.value.push({
      title: item.aliasName,
      dataIndex: `${item.name}`,
      cellClass: "customer-column",
      cellStyle: { "white-space": "nowrap" },
      width: 100,
    })
  });
  visible.value = true;
  return new Promise((resolve, reject) => {
    callback = resolve;
  });
};

const handleOk = async () => {
  if (selectedKeys.value.length <= 0) {
    Message.warning("请选择用户");
    return false
  }
  visible.value = false;
  callback(selectedKeys.value);
};

const handleCancel = () => {
  visible.value = false;
  callback();
}

// 搜索用户
const customerList = ref(null)
const loadingData = ref(false)

const searchCustomer = async () => {
  customerList.value = null
  if (!form.value) {
    Message.warning("请输入字段值");
    return false
  }
  if (!form.customerField) {
    Message.warning("请选择用户字段");
    return false
  }
  loadingData.value = true
  try {
    const list = await findCustomerPage({
      expression: `${form.customerField} eq ${form.value}`
    })
    customerList.value = list.content.map(it => {return { ...it.payload, _id: it.id}});
  } catch (err) {
    console.error(err)
  }
  loadingData.value = false
}

defineExpose({ show });
</script>

<style lang="less" scoped>
.user-list {
  .user-title {
    font-size: 12px;
    color: #666666;
    margin-bottom: 10px;
  }
}

.tag-name {
  color: #333333;
  font-size: 14px;
  padding: 0 5px;
  line-height: 20px;
  border-left: 3px solid rgb(var(--primary-6));
  margin-bottom: 10px;
}

:deep(.arco-radio) {
  margin-bottom: 10px;
}
</style>
