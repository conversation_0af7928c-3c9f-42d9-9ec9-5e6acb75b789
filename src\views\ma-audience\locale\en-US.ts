export default {
  "menu.audience": "Audience",
  "menu.audience.audience": "Campaign Audience",
  "menu.audience.prop": "Props",

  audience: {
    title: "Campaign Audience",
    id: "Audience ID",
    name: "Audience Name",

    popup: {
      create_title: "Create Audience",
      condtion_create_title: "Condition Audience Creation",
      select_title: "Select Customer Tag",
      compare_audience_title: "Compare Audience Differences",
      leftAudience: "Left Audience",
      rightAudience: "Right Audience",
      type: "Type",
      audience: "Audience",
      snapshot: "Snapshot",
      audienceSnapshot: "Audience Snapshot",
      optionalLabel: "Optional Label",
      selectedLabel: "Selected Label",
    },

    button: {
      search: "Search",
      reset: "Reset",
      create: "Create",
      data_warehouse_sync: "Data Warehouse Sync",
      audience_comparison: "Audience Comparison",
    },

    detail: {
      title: "Audience Detail",
      detail: "Audience Detail",
      snapshot: "Audience Snapshot",
    },

    edit: {
      title: "Edit Condition Audience",
      name: "Audience Name",
      testSwitch: "Test Audience Switch",
      realTrigger: "Real Trigger",
      testAudience: "Test Audience",
      estimatedAudience: "Estimated Audience",
      description: "Description Information",
      fieldFiltering: "Field Filtering",
      tagFiltering: "Tag Filtering",
      requiredTag: "Required Tags",
      optionalTag: "Optional Tags",
      excludeTag: "Excluded Tags",
    },

    analysis: {
      title: "Campaign Audience - Analysis",
      designCampaignFlowchart: "Design Campaign Flowchart",
      createCompareAudience: "Add Comparison Audience",
    },

    operation: {
      view: "View",
      edit: "Edit",
      delete: "Delete"
    },

    column: {
      id: "Audience ID",
      name: "Audience Name",
      selection_type: "Selection Type",
      audience_usage: "Audience Usage",
      modifiable: "Modifiable",
      description_info: "Description Info",
      action: "Actions",
    },
    reminder: {
      input_audience_id: "Enter the Audience ID",
      input_audience_name: "Enter the Audience Name",
      input_description: "Enter the Description Info",
      dataIrretrievableWarning: "Data cannot be recovered after deletion. Are you sure you want to proceed?",
      delete_audience: "Delete Audience",
      sysn_audience: "Sync Audience Complete",
      generate_new_audience_by_filtering: "Generate New Audience by Filtering Conditions",
      notEmpty: "Cannot be empty",

      field: "Select field",
      operator: "Operator",
      value: "Enter value",
      allTags: "Customer must hit all of the following tags",
      atLeastOneTag: "Customer needs to hit at least one of the following tags",
      noTags: "Customer cannot hit any of the following tags",
      selectAudienceType: "Select the audience type",
      selectAudience: "Select an audience",
      selectAudienceSnapshot: "Select an audience snapshot",
      selectComparisonAudience: "Select comparison audience",

      submitSuccess: "Submitted successfully",
      createAudienceSuccess: "Successfully created audience comparison task. You can view it in task management",
    },
    bool: {
      yes: "YES",
      no: "NO",
      and: "AND",
      or: "OR",
    }

  },

  'searchTable.form.number': 'Set Number',
  'searchTable.form.number.placeholder': 'Please enter Set Number',
  'searchTable.form.name': 'Set Name',
  'searchTable.form.name.placeholder': 'Please enter Set Name',
  'searchTable.form.contentType': 'Content Type',
  'searchTable.form.contentType.img': 'image-text',
  'searchTable.form.contentType.horizontalVideo': 'Horizontal short video',
  'searchTable.form.contentType.verticalVideo': 'Vertical short video',
  'searchTable.form.filterType': 'Filter Type',
  'searchTable.form.filterType.artificial': 'artificial',
  'searchTable.form.filterType.rules': 'Rules',
  'searchTable.form.createdTime': 'Create Date',
  'searchTable.form.status': 'Status',
  'searchTable.form.status.online': 'Online',
  'searchTable.form.status.offline': 'Offline',
  'searchTable.form.search': 'Search',
  'searchTable.form.reset': 'Reset',
  'searchTable.form.selectDefault': 'All',
  'searchTable.operation.create': 'Create',
  'searchTable.operation.import': 'Import',
  'searchTable.operation.download': 'Download',
  // columns
  'searchTable.columns.number': 'Set Number',
  'searchTable.columns.name': 'Set Name',
  'searchTable.columns.contentType': 'Content Type',
  'searchTable.columns.filterType': 'Filter Type',
  'searchTable.columns.count': 'Count',
  'searchTable.columns.createdTime': 'CreatedTime',
  'searchTable.columns.status': 'Status',
  'searchTable.columns.operations': 'Operations',
  'searchTable.columns.operations.view': 'View',
};
