/**
*by:<EMAIL> on 2022/7/18 0018
*/
<template>
  <div class="BudgetMain">
    <module main>
      <template #top>
        <BudgetSummaryPanel :budget-summary="budgetSummary"/>
      </template>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <a-button type="primary" @click="addBudgetItemRef.create()">
          <template #icon>
            <icon-plus/>
          </template>
          {{ t(global.button.create) }}
        </a-button>
      </template>
      <template #main>
        <a-table
            ref="table"
            :bordered="false"
            :data="dataSource"
            :pagination="false"
        >
          <template #columns>
            <a-table-column :title="t('campaign.budget.entryName')" data-index="name"/>
            <a-table-column :title="t('campaign.budget.plan')" data-index="planned" :width="150">
              <template #cell="{ record }">
                {{ formatNumber(record.planned) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('campaign.budget.used')" data-index="spent" :width="150">
              <template #cell="{ record }">
                {{ formatNumber(record.spent) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('campaign.budget.remaining')" data-index="balance" :width="150">
              <template #cell="{ record }">
                {{ formatNumber(record.balance) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('campaign.budget.warningValue')" data-index="alarmLimit" :width="150">
              <template #cell="{ record }">
                {{ formatNumber(record.alarmLimit) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('campaign.budget.action')" align="center" :width="350">
              <template #cell="{ record }">
                <a-button
                    type="text"
                    size="small"
                    @click="addBudget(record, 'PLAN')"
                >{{t(campaign.button.increaseBudget)}}
                </a-button
                >
                <a-button
                    type="text"
                    size="small"
                    @click="addBudget(record, 'CONFIRM')"
                >{{t(global.button.confirm)}}
                </a-button
                >
                <!--      :disabled="!(status === 'DRAFT' && status === 'REJECTED')"          -->
                <a-button
                    :disabled="!(status === 'DRAFT' && record.spent === 0)"
                    type="text"
                    size="small"
                    @click="addBudgetItemRef.edit(record)"
                >{{t(global.button.edit)}}
                </a-button
                >
                <a-button
                    :disabled="!(status === 'DRAFT' && record.spent === 0)"
                    type="text"
                    size="small"
                    @click="deleteData(record.id)"
                >{{t(global.button.delete)}}
                </a-button
                >
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
    <!--  追加预算  -->
    <a-modal
        v-model:visible="addBudgetVisible"
        width="300px"
        @before-ok="addBudgetOk"
    >
      <template #title>{{ dataFormBudget.type === 'PLAN' ? t('campaign.budget.addBudget') : t('global.button.confirm') }}</template>
      <a-form ref="appendBudgetFormRef" :model="dataFormBudget">
        <a-form-item
            field="amount"
            :hide-label="true"
            :rules="[{ required: true, message: t('campaign.reminder.inputAmount') }]"
        >
          <a-input-number
              v-model="dataFormBudget.amount"
              :placeholder="t('campaign.reminder.inputAmount')"
          >
            <template #prefix>￥</template>
          </a-input-number>
        </a-form-item>
      </a-form>
    </a-modal>
    <AddBudgetItemDlg
        ref="addBudgetItemRef"
        :budget-id="budgetId"
        :bind-data="bindData"
    />
  </div>
</template>

<script>
import {provide, ref, onMounted, getCurrentInstance} from "vue";
import {useRoute, useRouter} from "vue-router";
import {formatNumber} from "@/utils/filter";
import {Modal} from "@arco-design/web-vue";
import {modalCommit} from "@/utils/modal";
import {
  findBudgetItemList,
  getBudgetSummary,
  changeBudgetItem,
  deleteBudgetItem,
} from "@/api/budget";
import AddBudgetItemDlg from "@/components/modal-dlg/add-budget-item-dlg.vue";
import BudgetSummaryPanel from "./component/budget-summary-panel.vue";

export default {
  name: "BudgetMain",
  components: {
    BudgetSummaryPanel,
    AddBudgetItemDlg,
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const budgetId = route.query.id;
    const status = ref(route.query.status);
    const isGroup = !!route.query.isGroup;
    const module = ref({
      entityIdField: "id",
      // entityName: "预算管理",
      entityName: t('campaign.budget.budgetManagement'),
      breadcrumb: [
        {
          // name: isGroup ? '预算管理' : '营销活动',
          name: isGroup ? t('campaign.budget.budgetManagement') : t('campaign.title'),

          path: isGroup ? '/budget/budget' : '/campaign/campaign',
        },
        {
          // name: "预算设置",
          name: t('campaign.budget.budgetSettings'),
        },
      ],
      showCard: false,
    });
    const filter = ref([
      {
        field: "name",
        label: "名称",
        component: "a-input",
        operate: "like",
        placeholder: "请输入预算名称",
        comment: true,
        value: "",
      },
    ]);
    const dataSource = ref([]);
    const addBudgetItemRef = ref(null);

    // 获取列表
    const bindData = async (expression) => {
      getBudgetSummary(route.query.id).then((res) => {
        budgetSummary.value = res;
      });
      const pageData = await findBudgetItemList({
        expression: `${expression ? `${expression} AND ` : ""}budgetId like ${
            route.query.id
        }`,
      });
      dataSource.value = pageData;
    };

    const budgetSummary = ref({});

    // 删除
    const deleteData = async (id) => {
      Modal.confirm({
        title: "删除条目",
        content: "删除之后数据不可恢复，请确认是否删除?",
        onOk: async () => {
          await deleteBudgetItem(id);
          await bindData();
        },
      });
    };

    const appendBudgetFormRef = ref(null);

    // 追加预算
    const addBudgetVisible = ref(false);
    const dataFormBudget = ref({
      amount: 0,
      budgetId: "",
      itemId: "",
      remarks: "",
      type: "PLAN", // PLAN 追加 RESERVE 储备 CONFIRM 确认  SPEND 消费
    });
    const addBudget = (item, type) => {
      dataFormBudget.value.amount = "";
      dataFormBudget.value.budgetId = item.budgetId;
      dataFormBudget.value.type = type;
      dataFormBudget.value.itemId = item.id;
      addBudgetVisible.value = true;
    };
    const addBudgetOk = async (done) => {
      modalCommit(appendBudgetFormRef, done, async () => {
        await changeBudgetItem(dataFormBudget.value);
        await bindData();
      });
    };

    const setup = {
      module,
      filter,
      dataSource,
      addBudgetVisible,
      dataFormBudget,
      appendBudgetFormRef,
      deleteData,
      bindData,
      addBudget,
      addBudgetOk,
      status,
      budgetSummary,
      formatNumber,
      budgetId,
      addBudgetItemRef,
    };
    provide("main", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.BudgetMain {
}
</style>
