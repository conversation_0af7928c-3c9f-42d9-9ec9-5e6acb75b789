<template>
  <a-list v-if="!loading">
    <step-list-item title="创建ES索引模板" :status="itemStatus.EsTemplate" :on-click="configEsTemplate" />
    <step-list-item title="创建BI工作区" :status="itemStatus.BI" :on-click="configBI" />
    <step-list-item title="配置DWH" :status="itemStatus.DWH" :on-click="configDWH" />
    <step-list-item title="配置触达模型" :status="itemStatus.Model" :on-click="configModel" />
    <step-list-item title="创建节点分组" :status="itemStatus.NodeGroups" :on-click="configNodeGroups" />
    <step-list-item title="创建系统节点" :status="itemStatus.SystemNodes" :on-click="configSystemNodes" />
  </a-list>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { checkConfig, initEsTemplate, initBI, initDwh, initNodeGroups, initSystemNodeConfigs, initFactModel } from "@/api/marketing_center";
import StepListItem from "./step-list-item.vue";

const props = defineProps(["saveEntity", "configPass"]);

const loading = ref(true);

const itemStatus = ref({
  EsTemplate: "FAILED",
  BI: "FAILED",
  DWH: "FAILED",
  Model: "FAILED",
  NodeGroups: "FAILED",
  SystemNodes: "FAILED",
});

const configEsTemplate = async () => {
  let result = false;
  await initEsTemplate().then(() => {
    result = true;
  }).catch(() => {
    return false;
  });
  return result;
}

const configBI = async () => {
  let result = false;
  await initBI().then(() => {
    result = true;
  }).catch(() => {
    return false;
  });
  return result;
}

const configDWH = async () => {
  let result = false;
  await initDwh().then(() => {
    result = true;
  }).catch(() => {
    return false;
  });
  return result;
}

const configModel = async () => {
  let result = false;
  await initFactModel().then(() => {
    result = true;
  }).catch(() => {
    return false;
  });
  return result;
}

const configNodeGroups = async () => {
  let result = false;
  await initNodeGroups().then(() => {
    result = true;
  }).catch(() => {
    return false;
  });
  return result;
}

const configSystemNodes = async () => {
  let result = false;
  await initSystemNodeConfigs().then(() => {
    result = true;
  }).catch(() => {
    return false;
  });
  return result;
}

const configFinish = () => {
  if (itemStatus.value.BI
    && itemStatus.value.DWH) {
      props.configPass();
  }
}

onMounted(async () => {
  await props.saveEntity();
  const checkResult = await checkConfig();
  itemStatus.value.BI = checkResult.BI ? "SUCCESS" : "FAILED";
  itemStatus.value.DWH = checkResult.DWH ? "SUCCESS" : "ERROR";
  loading.value = false;
  configFinish();
});

defineExpose({
  loading
});

</script>
