import { Graph } from "@antv/x6";
import { getPorts } from "../../components/node";
import OutboundNode from "./node.vue";
import OutboundPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "outbound",
  node: {
    inherit: "vue-shape",
    x: 0,
    y: 0,
    width: 232,
    height: 104,
    component: {
      template: `<OutboundNode />`,
      components: {
        OutboundNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("OutboundNode", nodeData.node, true);
};

const Outbound = {
  type: "outbound",
  name: "输出",
  shape: "OutboundNode",
  iconClass: "icon-output",
  color: "#00b42a",
  themebg: "#e8ffea",
  registerNode: registerNode,
  pannel: OutboundPannel,
  help: Help,
  skippable: true,
};

export default Outbound;
