import { Graph } from "@antv/x6";
import { getPorts } from "../../components/node";
import UnionNode from "./node.vue";
import UnionPannel from "./pannel.vue";

const nodeData = {
  type: "union",
  node: {
    inherit: "vue-shape",
    x: 0,
    y: 0,
    width: 232,
    height: 104,
    component: {
      template: `<UnionNode />`,
      components: {
        UnionNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("UnionNode", nodeData.node, true);
};

const Union = {
  type: "union",
  name: "并集",
  shape: "UnionNode",
  iconClass: "icon-merge",
  registerNode,
  pannel: UnionPannel,
  skippable: true,
};

export default Union;
