import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import FlowNode from "./node.vue";
import FlowPannel from "./pannel.vue";

const nodeData = {
  type: "flow",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<FlowNode />`,
      components: {
        FlowNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("FlowNode", nodeData.node, true);
};

const getModelId = (data: any) => {
  let result = new Set();
  if (data.payloadEnable) {
    result.add("Flow:" + data.flowId + ":" + data.payloadFieldName);
  }
  return result;
};

const getBindName = (data: any) => {
  return data.payloadEnable ? data.payloadFieldName : null;
};

const Flow = {
  type: "flow",
  name: "Flow",
  shape: "FlowNode",
  iconClass: "icon-flow",
  color: "#00b42a",
  themebg: "#e8ffea",
  registerNode: registerNode,
  pannel: FlowPannel,
  skippable: true,
  getModelId: getModelId,
  getBindName: getBindName,
};

export default Flow;
