<template>
  <div class="easyflow-pannel-start">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="触达内容">
        <ReachItem v-model:entity="entity.reachItems" />
      </a-form-item>
      <a-form-item key="limit" :label="t('campaign.pannel.frequencyLimit')">
        <a-switch v-model="entity.limit" type="round">
          <template #checked>开</template>
          <template #unchecked>关</template>
        </a-switch>

        <template #label>
          {{ t('campaign.pannel.frequencyLimit') }}
          <a-tooltip>
            <template #content>
              <p>会员超出渠道频次限制的内容不再发送，但不影响后续流程执行</p>
            </template>
            <icon-info-circle-fill />
          </a-tooltip>
        </template>
      </a-form-item>
      <a-form-item :label="t('campaign.pannel.deduplication')">
        <a-space>
          <a-switch v-model="entity.unique" type="round" />
          <a-tree-select v-if="entity.unique" v-model="entity.uniqueFields" :allow-search="true" :data="flowModel"
            allow-clear :field-names="treeFieldStruct" placeholder="请选择去重字段" />
        </a-space>
      </a-form-item>
      <a-form-item label="勿扰策略">
        <SilenceSelect v-model:silenceRuleId="entity.silenceRuleId" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch, getCurrentInstance } from "vue";
import { treeFieldStruct } from "@/constant/common"
import EventBus from "@/utils/eventbus";
import { findContentList } from "@/api/flow-content";
import { getFlowModelsByIds } from "@/api/campaign";
import { findSilenceRuleList } from "@/api/silence";
import { getNode } from "@/api/node";
import { getFieldByPath, formatFields } from "@/utils/field"
import SilenceSelect from "@/components/ma/silence-select/index.vue"
import ReachItem from "./reach-item.vue"

const {
  proxy: { t }
} = getCurrentInstance();


const { node, easyflow } = defineProps(["node", "easyflow"]);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const entity = ref({
  flowContentId: null, // 事件Id
  limit: false,
  configId: node.data.configId,
});
const contentText = ref("");
const contentList = ref(null);
const loading = ref(false);
const flowModel = ref(null);

const silenceRules = ref([]);

const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = formatFields(modelFields.fields, "");
};


const handleSearchContent = async (value) => {
  const params = { fields: "name,flowNodeId,flowTemplateId,status" };
  params.expression = `enabled in true`
  if (node?.data?.configId) {
    params.expression = `flowNodeId in ${node?.data?.configId} AND ${params.expression}`
  }

  if (value) {
    params.expression = `( id like ${value} OR name like ${value} ) AND ${params.expression}`;
  }
  contentList.value = await findContentList(params);
  contentList.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
};

const showContent = async (contentId) => {
  if (contentId) {
    const params = { fields: "content", expression: `id eq ${contentId}` };
    const contents = await findContentList(params);
    contentText.value = contents[0].content;
  }
};
const loadSilenceRules = async () => {
  silenceRules.value = await findSilenceRuleList();
};

const onChangeContent = (contentId) => {
  const item = contentList.value.find((x) => {
    return x.id === contentId;
  });
  if (!item) {
    entity.value.flowContentId = "";
    entity.value.flowContentName = "";
    return false;
  }
  entity.value.flowContentName = item.name;
  showContent(contentId);
};

const save = () => {
  entity.value.reachItems = [{
"conditionJson":{"name": "Customer.phone",
				"type": "string",
				"operator": "empty",
				"value": "true",
				"conditions": [],
				"array": false,
				"fieldType": "string",
				"aliasName": "手机号"},
"reachType":"flow_content",
"flowContentId":"7qCvfi4gUL1vbT1dtj7fYx",
"channelId":"FLOW2",
"reachField":"Customer.phone",
"priority":2
},{
"conditionJson":{},
"reachType":"flow_content",
"flowContentId":"P72n5PWzwGZ9PbHYfKMYtV",
"channelId":"FLOW2",
"reachField":"Customer.phone",
"priority":1
}]
  return entity.value;
};

defineExpose({
  save,
});

onMounted(async () => {
  Object.assign(entity.value, node.data);
  showContent(entity.value.flowContentId);
  loadSilenceRules();
  await handelFlowModel();
});

watch(entity.value, async () => {
  await handleSearchContent();
});
</script>

<style lang="less" scoped>
.easyflow-pannel-start {
  .easyradio-group {
    width: 100%;

    .easyradio {
      width: 50%;
    }
  }

  .repeat-div {
    margin-top: 10px;
    justify-content: space-between;
    display: flex;
  }

  .form-date {
    width: 200px;
    margin: 0 10px;
  }

  .repeat-select {
    margin-right: 20px;
    width: 120px;
  }

  .flow-content {
    color: #b3b3b3;
  }

  .form-file {
    width: 100%;
    height: 90px;
    display: flex;
    cursor: pointer;
    font-size: 12px;
    color: #999999;
    align-items: center;
    justify-content: center;
    border: 1px dashed #dddddd;

    &:hover {
      opacity: 0.8;
      border: 1px dashed #999999;
    }
  }

  .between {
    display: flex;
    justify-content: space-between;
  }
}

.between-option {
  display: flex;
  justify-content: space-between;
}

.usage-type {
  color: lightseagreen;
}

.form-item-select {
  position: relative;

  .estimate-number {
    position: absolute;
    top: 14px;
    right: 0;
    zoom: 0.8;
    padding: 0 5px;
    background-color: #3370ff;
    color: #ffffff;
  }
}
</style>
