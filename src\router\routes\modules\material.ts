export default {
    path: "material",
    name: "material",
    component: () => import("../base.vue"),
    meta: {
        locale: "menu.material",
        requiresAuth: true,
        iconFont: "icon-folder",
        order: 40,
        parentMenu: true,
    },
    children: [
        {
            path: "material",
            name: "MaterialManagement",
            component: () => import("@/views/ma-material/material/main.vue"),
            meta: {
                type: 'menu',
                locale: "menu.material.management",
                requiresAuth: true,
                roles: ["*"],
            },
        },
        {
            path: "upload",
            name: "MaterialUpload",
            component: () => import("@/views/ma-material/material/upload.vue"),
            meta: {
                type: 'page',
                requiresAuth: true,
                roles: ["*"],
                hideInMenu: true,
            },
        },
        {
            path: "edit/:id",
            name: "MaterialEdit",
            component: () => import("@/views/ma-material/material/edit.vue"),
            meta: {
                type: 'page',
                requiresAuth: true,
                roles: ["*"],
                hideInMenu: true,
            },
        },
    ],
};