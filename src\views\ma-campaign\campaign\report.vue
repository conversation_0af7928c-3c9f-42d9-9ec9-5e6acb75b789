<template>
  <module view>
    <template #main>
      <a-spin style="display: block" :loading="loading">
        <div class="top-input-tag">
          <a-range-picker v-model="dateList" style="width: 300px" format="YYYY-MM-DD" :allow-clear="false"
            @change="changeTimeRange" />
          <div class="tag-list">
            <a-tag :color="workplaceItem.color">{{
              statusFilter(workplaceItem.status)
            }}</a-tag>
            <a-tag>{{ workplaceItem.name }}</a-tag>
          </div>
        </div>
        <div class="card-list">
          <div class="card-item">
            <div class="number">{{ countNumber }}</div>
            <div class="desc">{{ t('campaign.report.cumulativeReachCount') }}</div>
          </div>
          <div class="card-item">
            <div class="number">{{ peopleNumber }}</div>
            <div class="desc">{{ t('campaign.report.cumulativeReachPeople') }}</div>
          </div>
          <div v-for="item in dataList" class="card-item">
            <div class="number">{{ item.counts.TOTAL_SEND }}</div>
            <div class="desc">{{ item.name }}{{ t('campaign.report.reachCount') }}</div>
          </div>
        </div>
        <div class="table-list">
          <a-table ref="table" :pagination="false" :data="dataList">
            <template #columns>
              <a-table-column :title="t('campaign.report.reachChannel')" data-index="name" />
              <a-table-column :title="t('campaign.report.todayReachCount')" data-index="counts.TODAY_SEND" />
              <a-table-column :title="t('campaign.report.todayReachSuccessRate')" data-index="counts.TODAY_SUCC">
                <template #cell="{ record }">
                  {{
                    record.counts.TODAY_SEND
                      ? `${(
                        record.counts.TODAY_SUCC / record.counts.TODAY_SEND
                      ).toFixed(2) * 100
                      }%`
                      : "--"
                  }}
                </template>
              </a-table-column>
              <a-table-column :title="t('campaign.report.cumulativeReachSuccessCount')"
                data-index="counts.TOTAL_SEND" />
              <a-table-column :title="t('campaign.report.cumulativeReachSuccessRate')" data-index="code">
                <template #cell="{ record }">
                  {{
                    record.counts.TOTAL_SEND
                      ? `${(
                        record.counts.TOTAL_SUCC / record.counts.TOTAL_SEND
                      ).toFixed(2) * 100
                      }%`
                      : "--"
                  }}
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>

        <div class="table-list">
          <!--
          <a-form class="filter-form" :model="formModel" auto-label-width label-align="right">
            <a-row>
              <template v-for="(filterItem, filterIndex) in filter" :key="filterIndex">
                <a-col v-if="filterIndex < openFilterNumber" :span="4">
                  <a-form-item :field="filterItem.field" :label="filterItem.label">
                    <component :is="filterItem.component" v-model="formModel[filterItem.field]"
                      :options="filterItem.dataSource" :allow-clear="filterItem.allowClear"
                      :allow-search="filterItem.allowSearch" :placeholder="filterItem.placeholder"
                      :field-names="filterItem.fieldNames">
                    </component>
                  </a-form-item>
                </a-col>
              </template>
            </a-row>
          </a-form>
           -->
          <a-form ref="formRef" layout="inline" :model="filterList">
            <div class="input-list-btn">
              <div class="input-item">
                <a-form-item :label="t('campaign.report.channelName')">
                  <a-select v-model="filterList.nodeConfigId" :placeholder="t('campaign.reminder.channelName')"
                    style="width: 150px">
                    <a-option v-for="item of nodelist" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                  </a-select>
                </a-form-item>
                <a-form-item :label="t('campaign.report.reachField')">
                  <a-input v-model="filterList.reachField" :placeholder="t('campaign.reminder.reachField')" />
                </a-form-item>
                <a-form-item :label="t('campaign.report.reachFieldValue')">
                  <a-input v-model="filterList.reachFieldValue" :placeholder="t('campaign.reminder.reachFieldValue')" />
                </a-form-item>
                <a-form-item :label="t('campaign.report.reachStatus')">
                  <a-select v-model="filterList.reachStatus" :placeholder="t('campaign.reminder.reachStatus')"
                    style="width: 150px">
                    <a-option value="SENDING">{{ t("global.reach.status.sending") }}</a-option>
                    <a-option value="SENT">{{ t("global.reach.status.sendSuccess") }}</a-option>
                    <a-option value="LIMITED">{{ t("global.reach.status.restricted") }}</a-option>
                    <a-option value="ERROR">{{ t("global.reach.status.sendError") }}</a-option>
                    <!-- <a-option value="DELAY">推迟发送</a-option>
                    <a-option value="SKIP">忽略发送</a-option> -->
                    <a-option value="RECEIVED">{{ t("global.reach.status.received") }}</a-option>
                    <a-option value="ACCEPTED">{{ t("global.reach.status.accepted") }}</a-option>
                    <a-option value="REFUSED">{{ t("global.reach.status.rejected") }}</a-option>
                    <a-option value="REFUSE_CHANNEL">{{ t("global.reach.status.rejectChannelMessages") }}</a-option>
                  </a-select>
                </a-form-item>
                <a-form-item :label="t('campaign.report.canvasNodeId')">
                  <a-input v-model="filterList.taskId" :placeholder="t('campaign.reminder.canvasNodeId')" />
                </a-form-item>
              </div>
              <div class="right">
                <a-button style="margin-right: 10px" type="primary" @click="search(1)">{{ t('global.button.query')
                  }}</a-button>
                <a-button v-permission="['ma_menu.campaign.report-export']" style="margin-right: 10px" type="primary"
                  @click="exportFile">{{ t('global.button.export') }}</a-button>
                <a-button @click="reset">{{ t('global.button.reset') }}</a-button>
                <!-- <a-button v-if="filter.length > 3" type="text" @click="onOpenFilter">
                 {{ openFilterNumber === 3 ? "展开" : "收起" }}
                </a-button> -->

              </div>
            </div>
          </a-form>
          <a-table ref="recordTable" :pagination="false" :bordered="false" :data="dataSource">
            <template #columns>
              <a-table-column :title="t('campaign.report.channel')" data-index="nodeConfigId" :width="80">
                <template #cell="{ record }">
                  {{ nodeConfigNameFilter(record.nodeConfigId) }}
                </template>
              </a-table-column>
              <a-table-column :title="t('campaign.report.operationTime')" data-index="timestamp" :width="120">
                <template #cell="{ record }">
                  {{ $moment(record.timestamp).format("YYYY-MM-DD HH:mm:ss") }}
                </template>
              </a-table-column>
              <a-table-column :title="t('campaign.report.campaignId')" data-index="groupId" :width="120" />
              <a-table-column :title="t('campaign.report.campaignCategory')" data-index="category" :width="120" />
              <!-- <a-table-column title="活动编号" data-index="campaignCode" /> -->
              <a-table-column :title="t('campaign.report.canvasId')" data-index="flowId" :width="120" :ellipsis="true"
                :tooltip="true" />
              <a-table-column :title="t('campaign.report.executionStatus')" data-index="status" :width="80">
                <template #cell="{ record }">
                  {{ nodeStatusFilter(record.status) }}
                </template>
              </a-table-column>
              <a-table-column :title="t('campaign.report.customerId')" data-index="payloadId" :width="120" />
              <a-table-column :title="t('campaign.report.reachField')" data-index="reachField" :width="100" />
              <a-table-column :title="t('campaign.report.reachFieldValue')" data-index="reachFieldValue" :width="150" />
              <a-table-column :title="t('campaign.report.reachContent')" data-index="reachContent" :width="200"
                :ellipsis="true" :tooltip="{ class: 'tooltip-content' }" />
              <a-table-column :title="t('campaign.report.reachData')" data-index="reachMapping" :width="200"
                :ellipsis="true" :tooltip="{ class: 'tooltip-content' }" />
              <a-table-column :title="t('campaign.report.reachStatus')" data-index="reachStatus" :width="100">
                <template #cell="{ record }">
                  {{ reachStatusFilter(record.reachStatus) }}
                </template>
              </a-table-column>
              <a-table-column :title="t('campaign.report.processInstanceId')" data-index="instanceId" :width="120"
                :ellipsis="true" :tooltip="true" />
              <a-table-column :title="t('campaign.report.canvasNodeId')" data-index="taskId" :width="120" />
              <a-table-column :title="t('campaign.report.reachTemplateId')" data-index="reachTemplate" :width="120" />
              <a-table-column :title="t('campaign.report.reachTemplateName')" data-index="reachTemplateName"
                :width="120" />
              <a-table-column :title="t('campaign.report.reachContentId')" data-index="reachContentId" :width="120" />
              <a-table-column :title="t('campaign.report.reachContentName')" data-index="reachContentName"
                :width="120" />
              <a-table-column :title="t('campaign.report.acceptanceStatus')" data-index="income" :width="80" />
              <a-table-column :title="t('campaign.report.completionStatus')" data-index="outgoing" :width="80" />
              <a-table-column :title="t('campaign.report.errorStatus')" data-index="error" :width="80" />
              <a-table-column :title="t('campaign.report.nodeType')" data-index="taskType" :width="120" />
              <a-table-column :title="t('campaign.report.isTemplateFrequencyLimit')" data-index="limitedTemplate"
                :width="160" :ellipsis="true" :tooltip="true" />
              <a-table-column :title="t('campaign.report.isChannelFrequencyLimit')" data-index="limitedChannel"
                :width="160" :ellipsis="true" :tooltip="true" />
              <a-table-column :title="t('campaign.report.isGlobalFrequencyLimit')" data-index="limitedGlobal"
                :width="160" :ellipsis="true" :tooltip="true" />
              <a-table-column :title="t('campaign.report.message')" data-index="message" :width="150" :ellipsis="true"
                :tooltip="{ class: 'tooltip-content' }" />

              <a-table-column title="扩展字段1" data-index="extended1" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段2" data-index="extended2" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段3" data-index="extended3" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段4" data-index="extended4" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段5" data-index="extended5" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段6" data-index="extended6" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段7" data-index="extended7" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段8" data-index="extended8" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段9" data-index="extended9" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段10" data-index="extended10" :width="110" :ellipsis="true" :tooltip="true" />
            </template>
          </a-table>
          <div class="page-bottom">
            <!-- <span class="total">共{{ pagination.totalElements }}条</span> -->
            <span class="total">{{ t('global.total') }} : {{ pagination.totalElements }}</span>
            <a-pagination size="small" :total="pagination.total" :current="pagination.page" :page-size="pagination.size"
              show-page-size @change="search" @page-size-change="changeSizePage" />
          </div>
        </div>
      </a-spin>
    </template>
  </module>
</template>

<script>
import { computed, ref, onMounted, provide, getCurrentInstance } from "vue";
import moment from "moment";
import { useRoute } from "vue-router";
import { Message } from "@arco-design/web-vue";
import { getReachSummary, getMonitorPage } from "@/api/monitor";
import { findNodeLists } from "@/api/node";
import { saveTask2, startTask } from "@/api/task";
import { useUserStore } from "@/store";
import { filters } from "@/utils/filter";
import { flowNodeStatus } from "@/constant/flow";
import { campaignStatus } from "@/constant/campaign";
import { reachStatus } from "@/constant/reach";

import { uuid } from "@/utils/uuid";

export default {
  name: "TemplateStatement",
  components: {},
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const userStore = useUserStore();
    const userInfo = computed(() => {
      return userStore.userAuthInfo;
    });
    const workplaceItem = ref(
      JSON.parse(localStorage.getItem("workplaceItem"))
    );
    const colorList = {
      DRAFT: "rgba(0,180,42)",
      COMMITTED: "rgba(159,219,29)",
      APPROVED: "rgba(20,201,201)",
      REJECTED: "rgba(247,186,30)",
      RUNNING: "rgba(52,145,250)",
      PAUSED: "rgba(255,125,0)",
      FINISHED: "rgba(22,93,255)",
      STOP: "rgba(29,33,41)",
    };
    workplaceItem.value.color = colorList[workplaceItem.value.status];
    const route = useRoute();
    const { id } = route.query;
    const { groupId } = route.query;
    const module = ref({
      entityIdField: "id",
      entityName: `活动报表`,
      entityName: t('campaign.report.title'),
      breadcrumb: [
        {
          name: workplaceItem.value.module.name,
          path: workplaceItem.value.module.mainPath,
          query: { id: groupId },
        },
        {
          // name: "活动报表",
          name: t('campaign.report.title'),
        },
      ],
      mainPath: workplaceItem.value.module.mainPath,
    });
    if (workplaceItem.value.module.mainQuery) {
      module.value.mainQuery = workplaceItem.value.module.mainQuery;
    }
    // 过滤条件
    const filterList = ref({});
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      totalElements: 0,
      showPageSize: true,
    });
    // 数据设置
    const loading = ref(true);
    const entity = ref({});
    const searchValue = ref("");
    const countNumber = ref(0);
    const peopleNumber = ref(0);
    const nodelist = ref([]);
    const dataSource = ref([]);
    const dataList = ref([]);
    const dateList = ref([]);

    const filter = ref([
      {
        field: "nodeConfigId",
        label: "渠道名称",
        component: "a-select",
        operate: "eq",
        placeholder: "请选择渠道名称",
        dataSource: nodelist.value,
        value: "",
      },
      {
        field: "reachField",
        label: "触达字段",
        component: "a-input",
        operate: "eq",
        placeholder: "请输入触达字段",
        value: "",
      },
      {
        field: "reachFieldValue",
        label: "触达字段值",
        component: "a-input",
        operate: "eq",
        placeholder: "请输入触达字段值",
        dataSource: nodelist.value,
        value: "",
      },
      {
        field: "reachStatus",
        label: "触达状态",
        component: "a-select",
        operate: "eq",
        placeholder: "请选择触达状态",
        dataSource: nodelist.value,
        value: "",
      },
      {
        field: "taskId",
        label: "画布节点ID",
        component: "a-input",
        operate: "eq",
        placeholder: "请输入画布节点ID",
        dataSource: nodelist.value,
        value: "",
      },
      {
        field: "limitedTemplate",
        label: "是否模版频次限制",
        component: "a-input",
        operate: "eq",
        placeholder: "请输入频次限制模板",
        value: "",
      },
      {
        field: "limitedChannel",
        label: "是否渠道频次限制",
        component: "a-input",
        operate: "eq",
        placeholder: "请输入频次限制渠道",
        value: "",
      },

      {
        field: "limitedGlobal",
        label: "是否全局频次限制",
        component: "a-input",
        operate: "eq",
        placeholder: "请输入全局频次限制",
        value: "",
      },
    ]);
    const openFilterNumber = ref(4);
    const onOpenFilter = () => {
      openFilterNumber.value =
        openFilterNumber.value === 3 ? filter.value.length : 3;
    };

    const generateFormModel = () => {
      const tFilter = {};
      filter?.value?.forEach((f) => (tFilter[f.field] = f.value));
      return tFilter;
    };

    const formModel = ref(generateFormModel());

    const getSummary = () => {
      getReachSummary(id, {
        startDate: dateList.value[0],
        endDate: dateList.value[1],
      }).then((res) => {
        peopleNumber.value = res.reachPeople;
        dataList.value = res.channels;
        dataList.value.forEach((item) => {
          countNumber.value = item.counts.TOTAL_SEND;
        });
      });
    };

    const statusFilter = (status) => {
      // eslint-disable-next-line no-shadow
      const item = campaignStatus.find((item) => {
        return item.value === status;
      });
      return item?.label || "--";
    };

    const reachStatusFilter = (val) => {
      // console.log(val);

      const item = reachStatus.find((it) => {
        return it.value === val;
      })
      return item?.label || "--";
    };

    const nodeStatusFilter = (val) => {
      return filters(flowNodeStatus, val, "text", "value");
    };

    const nodeConfigNameFilter = (val) => {
      const res = nodelist.value.find((item) => {
        return item.id === val;
      });
      return res ? res.name : "";
    };
    const getNodelist = async () => {
      nodelist.value = await findNodeLists({
        expression: "type in flow_content,sms,wechat,email,coupon,points",
        fields: "name",
      });
    };

    const buildExp = () => {
      const exp = [];
      for (const item in filterList.value) {
        if (filterList.value[item]) {
          exp.push(`${item} eq ${filterList.value[item]}`);
        }
      }
      exp.push(`timestamp ge ${`${dateList.value[0]}T00:00:00.000+08:00`}`);
      exp.push(`timestamp le ${`${dateList.value[1]}T23:59:59.999+08:00`}`);
      return exp.join(" AND ");
    };

    const getQueryExp = (exp) => {
      let expression = `instanceId eq ${id} AND reachStatus not NULL`;
      if (exp) {
        expression = `${expression} AND ${exp}`;
      }
      // if (dateList?.value?.length > 0) {
      //   expression = `${expression} AND timestamp gt ${dateList.value[0]} AND timestamp lt ${dateList.value[1]}`;
      // }
      return expression;
    };

    // 查询API
    const bindData = async (exp) => {
      const expression = getQueryExp(exp);
      entity.value = await getMonitorPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        { expression }
      );
      pagination.value.total =
        entity.value.totalElements > 10000 ? 10000 : entity.value.totalElements;
      pagination.value.totalElements = entity.value.totalElements;

      dataSource.value = entity.value.content || [];
      dataSource.value.map((it) => {
        it.ext = JSON.stringify(it.ext);
        return it;
      });
      loading.value = false;
    };

    const search = (page = 1) => {
      loading.value = true;
      pagination.value.page = page;
      bindData(buildExp());

      loading.value = false;
    };

    const reset = async () => {
      filterList.value = {};
      await bindData();
    };

    const changeTimeRange = async () => {
      getSummary();
      search();
    };

    const exportFile = async () => {
      const expression = getQueryExp(buildExp());
      const job = {
        id: `${id}_${uuid(6)}`,
        // name: `${workplaceItem.value.name}_报表数据导出`,
        name: `${workplaceItem.value.name}${t('global.tips.success.reportDataExport')}`,
        type: "export_campaign_reach_report",
        timestamp: new Date(),
        status: "SUBMITTED",
        userId: userInfo.value.username,
        payload: {
          query: expression,
        },
      };
      const res = await saveTask2(job);
      await startTask(res.id);
      // Message.success("创建活动监控人群任务成功,可在任务管理中查看");
      Message.success(t('global.tips.success.creacreateActivityMonitoringAudienceTaskSuccess'));
    };

    // 切换条数
    const changeSizePage = async (e) => {
      pagination.value.page = 1;
      pagination.value.size = e;
      await search();
    };

    onMounted(() => {
      // eslint-disable-next-line no-use-before-define
      const today = moment().format("YYYY-MM-DD");
      const tenDaysAgo = moment().subtract(10, "days").format("YYYY-MM-DD");
      dateList.value = [tenDaysAgo, today];
      changeTimeRange();
      getNodelist();
    });

    const setup = {
      t,
      id,
      filterList,
      pagination,
      searchValue,
      dataSource,
      dataList,
      dateList,
      module,
      nodelist,
      workplaceItem,
      countNumber,
      peopleNumber,
      reachStatusFilter,
      statusFilter,
      nodeConfigNameFilter,
      moment,
      changeSizePage,
      search,
      reset,
      changeTimeRange,
      exportFile,
      loading,
      nodeStatusFilter,
      filter,
      formModel,
      openFilterNumber,
    };

    provide("view", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.top-input-tag {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tag-list {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.card-list {
  display: flex;
  gap: 20px;
  padding: 0 20px;
  flex-wrap: wrap;

  .card-item {
    flex: 1;
    min-width: 300px;
    text-align: center;
    color: #333;
    border: 1px solid #efefef;
    padding: 30px;

    .number {
      font-size: 40px;
      font-weight: bold;
      line-height: 60px;
    }

    .desc {
      font-size: 16px;
      color: #999;
    }
  }
}

.table-list {
  padding: 20px;

  .input-list-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
  }

  .input-item {
    display: flex;
    align-items: center;
  }
}

.chart-body {
  padding: 20px;

  .top-name {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-name {
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.page-bottom {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  align-items: center;

  .total {
    margin-top: 20px;
  }
}
</style>
