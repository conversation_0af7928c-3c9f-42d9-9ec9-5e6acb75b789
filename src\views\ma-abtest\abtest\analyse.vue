/**
*by:<EMAIL> on 2022/11/2 0015
*/
<template>
  <module view>
    <template #main>
      <div v-if="dataList.length > 1"
           class="AudienceAnalyse">
        <div v-for="(item, index) in dataList"
             :key="item.id"
             class="chart-item">
          <div class="top-name">
            <div class="left-name">
              <div class="name-number">{{ item.name }}
                <!--                <span>{{ item.number }}</span>人-->
              </div>
              <div class="tags">
                <template v-for="c in item.list"
                          :key="item.name">
                  <a-tag checkable
                         :default-checked="c.is"
                         class="tag"
                         color="red"
                         @click="c.is = !c.is">{{ c.name }}</a-tag>
                </template>
              </div>
            </div>
          </div>
          <div class="grid-demo">
            <a-row :gutter="10">
              <template v-for="c in item.list"
                        :key="c.i">
                <a-col v-if="c.is"
                       class="grid-col"
                       :lg="24"
                       :xl="dataList.length > 1 ? 24 : 12">
                  <ChartItem class="grid-item"
                             :height="c.h * 32 + 'px'"
                             :data-item="c" />
                </a-col>
              </template>
            </a-row>
          </div>
        </div>
      </div>
      <div v-else
           class="loading-chart">
        <a-spin dot
                tip="努力分析中" />
      </div>
    </template>
  </module>
</template>

<script>
import { ref, reactive, onMounted, provide, getCurrentInstance } from 'vue';
import {
  getAbtest,
  getChangeList,
  getAnalysisAudience
} from '@/api/analysis'
import { uuid } from '@/utils/uuid'
import { useRoute } from 'vue-router'
import ChartItem from "@/components/bi/components/chart-item.vue";

export default {
  name: 'ABAnalyse',
  components: {
    ChartItem
  },
  setup() {
    const route = useRoute()
    const dataList = ref([])
    const rqList = ref([])
    const {
      proxy: { t }
    } = getCurrentInstance();
    const module = ref({
      entityIdField: "id",
      // entityName: `A/B测试管理`,
      entityName: t('ab.abTestManagement'),
      breadcrumb: [
        {
          // name: "A/B测试管理",
          name: t('ab.abTestManagement'),
          path: "/ab/abtest",
        },
        {
          name: "A/B测试管理-分析",
        },
      ],
      mainPath: "/ab/abtest"
    })

    // 获取显示数据
    const chartType = reactive([
      { name: '性别', value: 'gender', is: true, h: 8, w: 6, x: 0, y: 0, static: false },
      { name: '出生日期', value: 'birthday', is: true, h: 8, w: 6, x: 0, y: 6, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 6, y: 0, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 6, y: 6, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 12, y: 0, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 12, y: 6, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 18, y: 0, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 18, y: 6, static: false }
    ])


    const getChartList = (id, taskId, x) => {
      if (!x.id) {
        return
      }
      return getChangeList({ expression: "useType eq ab" }).then(gs => {
        const list = []
        gs.forEach((item, index) => {
          chartType[index].name = item.name
          chartType[index].value = item.type
          list.push(getAnalysisAudience(chartType[index].value, 'ab', `${x.id}`))
        })
        Promise.all(list).then(res => {
          const analysisList = {
            number: 0,
            name: '',
            id,
            list: []
          }
          const curIndex = rqList.value.findIndex(y => {
            return y.id === id
          })
          analysisList.number = x.number
          analysisList.name = x.name
          res.forEach((c, index) => {
            // if (!c.dataSet) { return false }
            analysisList.list.push({
              data: c,
              i: uuid(22),
              id: uuid(null),
              ...chartType[index]
            })
          })
          dataList.value.push(analysisList)
        })
      })
    }


    onMounted(() => {
      getAbtest(route.query.id).then(res => {
        for (const key in res.groups) {
          getChartList(key, res.taskId, res.groups[key])
        }

      })
    })

    const setup = {
      module,
      dataList,
      getChartList,
      chartType,
    };
    provide("view", setup);
    return setup;
  },
}
</script>

<style lang="less" scoped>
.AudienceAnalyse {
  margin: 16px 20px;
  background-color: #ffffff;
  display: flex;
  .grid-demo {
    .grid-col {
      margin-top: 10px;
    }
    .grid-item {
      text-align: center;
      background-color: var(--color-bg-1);
      padding: 16px 16px;
      border: 1px solid var(--color-neutral-3);
      border-radius: var(--border-radius-small);
    }
  }

  .vue-grid-item:not(.vue-grid-placeholder) {
    background-color: var(--color-bg-1);
    border: 1px solid #999999;
    padding: 16px 16px;
    border: 1px solid var(--color-neutral-3);
    border-radius: var(--border-radius-small);
  }

  .chart-item {
    border-right: 1px solid var(--color-neutral-3);
    padding-right: 10px;
    margin-right: 10px;
    margin-bottom: 10px;
    flex: 1;
    &:last-child {
      border: 0;
      padding-right: 0;
      margin-right: 0;
    }
  }

  .card-item {
    margin-bottom: 16px;
  }

  .filter-form {
    margin-top: 20px;
  }

  &.screen-full {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: var(--color-fill-2);
  }

  .top-name {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .name-number {
      font-size: 16px;
      margin-bottom: 10px;

      span {
        font-size: 24px;
        margin-left: 20px;
      }
    }

    .right-btn {
      padding: 16px 25px;
      border: 1px dashed #999999;
      cursor: pointer;
    }
  }

  .tags {
    .tag {
      margin-right: 5px;
    }
  }

}
.loading-chart{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
