import { mergeConfig } from "vite";
import eslint from "vite-plugin-eslint";
import baseConig from "./vite.config.base";

export default mergeConfig(
  {
    mode: "development",
    server: {
      open: true,
      fs: {
        strict: true,
      },
      proxy: {
        "/api/platform-tenant/": {
          target: "http://localhost",
          changeOrigin: true,
        },

        "/api/ma-manage/": {
          target: "http://localhost",
          changeOrigin: true,
        },
        "/api/ma-flow/": {
          target: "http://localhost",
          changeOrigin: true,
        },
        "/api/ma-portal/": {
          target: "http://localhost",
          changeOrigin: true,
        },
        "/api/bi-manage/": {
          target: "https://demo.lianwei.com.cn",
          changeOrigin: true,
        },
      },
    },
    plugins: [
      // eslint({
      //   cache: false,
      //   include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
      //   exclude: ['node_modules'],
      // }),
    ],
    resolve: {
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
  },
  baseConig
);
