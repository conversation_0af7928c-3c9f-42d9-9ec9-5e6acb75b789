import type { Router, LocationQueryRaw } from "vue-router";
import NProgress from "nprogress"; // progress bar

import usePermission from "@/hooks/permission";
import { useUserStore, useBussinessUnitStore } from "@/store";
import { isLogin } from "@/utils/auth";
import appRoutes from "../routes";

export default function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    NProgress.start();
    const userStore = useUserStore();
    const userBussinessUnitStore = useBussinessUnitStore();
    async function crossroads() {
      const Permission = usePermission();
      if (Permission.accessRouter(to)) next();
      else {
        const destination = Permission.findFirstPermissionRoute(appRoutes) || {
          name: "notFound",
        };
        next(destination);
      }
      NProgress.done();
    }

    // 判断是否为生产环境

    if (import.meta.env.PROD) {
      // 生产环境
      if (userStore.authorized) {
        if (userBussinessUnitStore.bussinessUnit.length === 0) {
          await userBussinessUnitStore.getBussinessUnit();
        }
        crossroads();
      } else {
        await userStore.info();
        if (userStore.authorized) {
          await userBussinessUnitStore.getBussinessUnit();
          crossroads();
        } else {
          window.location.href = userStore.loginPage;
        }
      }
    } else if (isLogin()) {
      // dev模式，且已经登录
      if (userStore.authorized) {
        // 登录成功
        crossroads();
      } else {
        // 登录失败
        try {
          await userStore.info();
          if (userStore.authorized) {
            await userBussinessUnitStore.getBussinessUnit();
            crossroads();
          } else if (to.name === "login") {
            next();
            NProgress.done();
            return;
          }
        } catch (error) {
          next({
            name: "login",
            query: {
              redirect: to.name,
              ...to.query,
            } as LocationQueryRaw,
          });
          NProgress.done();
        }
      }
    } else {
      if (to.name === "login") {
        next();
        NProgress.done();
        return;
      }
      next({
        name: "login",
        query: {
          redirect: to.name,
          ...to.query,
        } as LocationQueryRaw,
      });
      NProgress.done();
    }
  });
}
