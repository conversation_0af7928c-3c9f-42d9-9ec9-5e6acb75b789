export default {
  path: "audience",
  name: "audience",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.audience",
    requiresAuth: true,
    icon: "icon-user-group",
    order: 40,
    parentMenu:true,
  },
  children: [
    {
      path: "main",
      name: "Audience",
      component: () => import("@/views/ma-audience/audience/main.vue"),
      meta: {
        type:'menu',
        locale: "menu.audience.audience",
        requiresAuth: true,
        roles: ["ma_menu.audience"],
      },
    },
    {
      path: "edit-condition",
      name: "AudienceConditionEdit",
      component: () => import("@/views/ma-audience/audience/edit-condition.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["ma_menu.audience.create"],
      },
    },
    {
      path: "edit-manual",
      name: "AudienceManualEdit",
      component: () => import("@/views/ma-audience/audience/edit-manual.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "audience/view",
      name: "AudienceView",
      component: () => import("@/views/ma-audience/audience/view.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "audience/analyse",
      name: "AudienceAnalyse",
      component: () => import("@/views/ma-audience/audience/analyse.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
  ],
};
