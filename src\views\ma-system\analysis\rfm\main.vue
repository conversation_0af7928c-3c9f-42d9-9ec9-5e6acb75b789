<template>
  <module main>
    <template v-slot:filter></template>
    <template v-slot:search></template>
    <template v-slot:action>
      <a-button type="primary" @click="rfmHandleOk('custom')">
        <template #icon>
          <icon-plus />
        </template>
        新建
      </a-button>
    </template>
    <template v-slot:context><span></span></template>
    <template v-slot:main>
      <a-table
          ref="table"
          row-key="id"
          :bordered="false"
          :data="dataSource"
          :pagination="false"
      >
        <template #columns>
          <a-table-column title="模型名称" data-index="name"/>
          <a-table-column title="计算间隔" data-index="period"/>
          <a-table-column title="是否被使用" data-index="hasused">
            <template #cell="{ record }">
              {{ record.hasused ? '是' : '否' }}
            </template>
          </a-table-column>
          <a-table-column title="创建时间" data-index="createTime">
            <template #cell="{ record }">
              {{ $moment(record.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </a-table-column>
          <a-table-column title="操作" align="center">
            <template #cell="{ record }">
              <a-button @click="detail(record)" type="text" size="small"
              >编辑
              </a-button
              >
              <a-button type="text" size="small" @click="deleteData(record.id)">删除</a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>

      <!--  弹窗  -->
      <a-modal v-model:visible="addShowRfm" :footer="false">
        <template #title>新建RFM分析</template>
        <div class="btn-list">
          <a-button type="outline"
                    :disabled="(isBasisCustom.basis >= 1 || (isBasisCustom.basis + isBasisCustom.custom) >= 5)"
                    class="btn-item"
                    @click="rfmHandleOk('basis')">
            <div class="title">基础RFM分析</div>
            <span class="desc">统计进180天内的用户最后一次购买间隔天数，购买此时和购买总金额</span>
            <div v-if="(isBasisCustom.basis >= 1 || (isBasisCustom.basis + isBasisCustom.custom) >= 5)"
                 class="grayscale">已有基础模型，无法再创建基础模型</div>
          </a-button>
          <a-button type="outline"
                    :disabled="(isBasisCustom.basis + isBasisCustom.custom) >= 5"
                    class="btn-item"
                    @click="rfmHandleOk('custom')">
            <div class="title">自定义RFM分析</div>
            <span class="desc">自定义设置RFM的统计窗口期和统计指标</span>
            <div v-if="(isBasisCustom.basis + isBasisCustom.custom) >= 5"
                 class="grayscale">模型数量超过最大限制，请释放无用模型后创建</div>
          </a-button>
        </div>
      </a-modal>
    </template>


  </module>
</template>

<script>
import {
  ref,
  watch,
  provide,
    reactive,
  computed,
  onMounted,
  onUpdated,
  onUnmounted,
  onBeforeMount,
  onBeforeUpdate,
  onBeforeUnmount,
} from "vue";

import {useRouter} from "vue-router";
import {
  findAnalysisRFMModelList,
  getRfmPageList,
  deleteTargetSource
} from "@/api/system";
import {Modal} from "@arco-design/web-vue";

export default {
  components: {

  },
  setup() {
    // 路由API
    const router = useRouter();

    // 模块设置
    const module = ref({
      entityIdField: "id",
      entityName: "分析模型-RFM",
      breadcrumb: [
        {
          name: "系统设置",
          path: "/system/setting",
        },
        {
          name: "分析模型-RFM",
        },
      ],
      editPath: "/system/rfm/edit",
      createPath: "/system/rfm/edit",
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true,
    });

    // 过滤设置
    const filter = ref([
      {
        field: "id",
        label: "模型id",
        component: "a-input",
        operate: "eq",
        placeholder: "请输入模型id",
        value: "",
      },
    ]);

    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    // 定义新增类型类型
    const isBasisCustom = reactive({
      basis: 0,
      custom: 0
    })

    // 查询API
    const bindData = async (expression) => {
      entity.value = await findAnalysisRFMModelList(
          {
            ...pagination.value,
            page: pagination.value.page - 1,
          },
          {expression: expression}
      );
      pagination.value.total = entity.value.totalElements;

      isBasisCustom.basis = 0
      isBasisCustom.custom = 0
      entity.value.content.forEach(item => {
        if (item.basicModel) {
          isBasisCustom.basis++
        } else {
          isBasisCustom.custom++
        }
      })
    };

    // 详情跳转
    const detail = (row) => {
      const query = {};
      query[module.value.entityIdField] = row[module.value.entityIdField];
      router.push({path: module.value.editPath, query: query});
    };

    // 新建跳转
    const create = () => router.push({path: module.value.createPath});

    // 选择类型
    const addShowRfm = ref(false)
    const addRfmType = () => {
      addShowRfm.value = !addShowRfm.value
    }
    // 跳转类型
    const rfmHandleOk = (type) => {
      router.push({path: module.value.createPath, query: { type: type }});
    }

    // 删除
    const deleteData = async (id) => {
      Modal.confirm({
        title: "删除RFM模型",
        content: "删除之后数据不可恢复，请确认是否删除?",
        onOk: async () => {
          await deleteTargetSource(id)
          if (dataSource.value.length === 1 &&
              pagination.value.page > 1) {
            pagination.value.page--
          }
          await bindData()
        },
      });
    };

    const setup = {
      module,
      filter,
      entity,
      deleteData,
      detail,
      create,
      bindData,
      dataSource,
      pagination,
      isBasisCustom,
      addShowRfm,
      addRfmType,
      rfmHandleOk
    };
    provide("main", setup);
    return setup;
  },
};
</script>
<style scoped lang="less">
.btn-list {
  width: 457px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .btn-item{
    height: auto;
    width: 40%;
    margin: 0 auto;
    text-align: left;
    padding: 10px 15px;
    white-space: break-spaces;
    min-height: 95px;
    line-height: normal;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    position: relative;
    &:hover{
      .grayscale{
        opacity: 1;
      }
    }
    .title{
      font-size: 14px;
    }
    .desc{
      font-size: 12px;
      opacity: 0.6;
      word-break: break-word;
      width: 100%;
      line-height: 18px;
    }
  }
  .grayscale{
    background-color: rgba(0,0,0,0.6);
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    padding: 10px 15px;
    box-sizing: border-box;
    opacity: 0;
  }
}
</style>
