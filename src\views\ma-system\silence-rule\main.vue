/** 勿扰规则 */
<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <a-button v-permission="['ma_menu.system.silence-rule.create']" type="primary" @click="editItem()">
          <template #icon>
            <icon-plus />
          </template>
          {{ $t('systemSetting.silenceManagement.xinJian') }}
        </a-button>
      </template>
      <template #main>
        <a-table :bordered="false" :data="dataSource" :pagination="false">
          <template #columns>
            <a-table-column :title="$t('systemSetting.silenceManagement.bianMa')" data-index="id" :width="60" />
            <a-table-column :title="$t('systemSetting.silenceManagement.mingCheng')" data-index="name" :width="60" />
            <a-table-column :title="$t('systemSetting.silenceManagement.beiZhu')" data-index="summary" :width="60" :ellipsis="true" :tooltip="{class:'tooltip-content'}" />
            <a-table-column :title="$t('systemSetting.silenceManagement.caoZuo')" :align="'center'" :width="60">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.system.silence-rule.modify']" type="text" size="small" @click="editItem(record)">编辑</a-button>
                <!-- <a-button v-permission="['ma_menu.system.silence-rule.delete']" type="text" size="small" @click="deleteData(record.id)">删除</a-button> -->
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
    <EditCategoryDlg ref="editCategoryRef" />
  </div>
</template>

<script>
import { provide, ref, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { Modal } from "@arco-design/web-vue";
import { findSilenceRulePage } from "@/api/silence";
import EditCategoryDlg from "./component/edit-silence-rule-dlg.vue";

export default {
  components: {
    EditCategoryDlg,
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: t('systemSetting.silenceManagement.xiTongSheZhi'),
          path: "/system/setting",
        },
        {
          name: t('systemSetting.silenceManagement.huoDongFenLei'),
        },
      ],
      showCard: false,
    });

    const editCategoryRef = ref(null);
    const dataSource = ref([]);
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true,
    });
    const filter = ref([
      {
        field: "name",
        label: t('systemSetting.silenceManagement.name'),
        component: "a-input",
        operate: "like",
        placeholder: t('systemSetting.silenceManagement.name'),
        comment: true,
        value: ""
      }
    ]);
    // 获取列表
    const bindData = async (expression) => {
      const pageData = await findSilenceRulePage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        {
          expression,
        }
      );
      dataSource.value = pageData.content;
      pagination.value.total = pageData.totalElements;
    };

    const editItem = async (item) => {
      if (item) {
        await editCategoryRef.value.showEdit(item);
      } else {
        await editCategoryRef.value.showCreate();
      }
      await bindData();
    };

    // 删除
    // const deleteData = async (id) => {
    //   Modal.confirm({
    //     title: "删除活动分类",
    //     content: "删除之后数据不可恢复，请确认是否删除?",
    //     onOk: async () => {
    //       await deleteCategory(id);
    //       if (dataSource.value.length === 1 && pagination.value.page > 1) {
    //         pagination.value.page--;
    //       }
    //       await bindData();
    //     },
    //   });
    // };

    const setup = {
      module,
      editCategoryRef,
      dataSource,
      pagination,
      filter,
      bindData,
      editItem,
      // deleteData,
    };
    provide("main", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped></style>
