<template>
  <a-alert type="warning">
   分析报表设置
  </a-alert>
  <a-form ref="formRef" :model="formData">
    <a-form-item field="host" label="连接地址" :rules="[{ required: true, message: '请输入连接地址' }]">
      <a-input v-model="formData.field" placeholder="请输入连接地址" />
    </a-form-item>
    <a-form-item field="username" label="用户名" :rules="[{ required: true, message: '请输入用户名' }]">
      <a-input v-model="formData.username" placeholder="请输入用户名" />
    </a-form-item>
    <a-form-item field="password" label="密码" :rules="[{ required: true, message: '请输入密码' }]">
      <a-input v-model="formData.password" placeholder="请输入密码" />
    </a-form-item>

  </a-form>
</template>

<script setup>
import { ref, computed } from "vue";

const form = ref({});
const commit = (entity) => {

}
defineExpose({
  commit
})
</script>

<style lang="less" scoped>

.arco-alert {
    margin: 10px;
  }

</style>
