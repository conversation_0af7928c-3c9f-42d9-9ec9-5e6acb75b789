<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'end' })
    const setup = {
      title: item.name || "结束",
      summary: item.name || "结束节点",
      iconClass: item.icon || "icon-flow-stop",
      nodeClass: "easyflow-node-end",
      headerColor: item.themeColor || "#ff6d69",
      headerBgColor: item.themeColor || "#ff6d69",
      background: item.background || "#fff8f8",
      ignoreOutgoingMonitor: true,
      notTirgger: true
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
