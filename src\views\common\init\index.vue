<template>
  <a-layout class="layout">
    <div v-if="navbar" class="layout-navbar">
      <NavBar />
    </div>
    <a-layout class="layout-content">
      <a-layout-content class="initialize-data">
        <!--  初始化弹窗  -->
        <Setup v-if="buStatus === 'NONE'" :init-bu="initBu" />
        <init-vue v-if="buStatus != 'NONE' && buStatus != 'ONLINE'" />
      </a-layout-content>
      <Footer v-if="footer" />
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useAppStore, useBussinessUnitStore } from "@/store";
import NavBar from "@/components/navbar/index.vue";
import Footer from "@/components/footer/index.vue";
import Setup from "./components/setup.vue";
import InitVue from "./components/init.vue";

const appStore = useAppStore();
const useBUStore = useBussinessUnitStore();
const router = useRouter();
const navbar = computed(() => appStore.navbar);
const footer = computed(() => appStore.footer);

const buStatus = ref(useBUStore.marketingCenter?.status);

if (buStatus.value == null) {
  buStatus.value = "NONE";
}

if (useBUStore.marketingCenter?.status === "ONLINE") {
  router.push({
    path: "/dashboard/workplace"
  });
}

const initBu = () => {
  buStatus.value = "INIT";
};
</script>

<style scoped lang="less">
@nav-size-height: 60px;
@layout-max-width: 1100px;

.layout {
  width: 100%;
  height: 100%;
}

.layout-navbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: @nav-size-height;
}

.layout-content {
  padding-top: 60px;
  min-height: 100vh;
  overflow-y: hidden;
  background-color: var(--color-fill-2);
  transition: padding 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}

.initialize-data {
  position: fixed;
  left: 0;
  right: 0;
  top: 60px;
  bottom: 0;
  background-color: #ffffff;
  z-index: 999;
  display: flex;
  flex-direction: column;
  padding: 50px;

  i {
    font-size: 90px;
    color: rgb(var(--primary-6));
  }
}
</style>
