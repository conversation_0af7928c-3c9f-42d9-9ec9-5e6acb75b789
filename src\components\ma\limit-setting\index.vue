<template>
  <a-row :gutter="20">
    <a-col :span="24">
      <a-form-item>
        <template #label>
          {{ t("reach.edit.frequencyLimit") }}
          <a-tooltip :content="t('reach.reminder.frequencyLimit')">
            <icon-info-circle />
          </a-tooltip>
        </template>
        <a-switch v-model="limitSetting.type" type="round" class="form-switch" checked-value="DATE"
          unchecked-value="DISABLED">
          <!-- <template #checked>{{t("global.button.yes")}}</template>
          <template #unchecked>{{t("global.button.no")}}</template> -->
          <template #checked>{{ t("reach.status.yes") }}</template>
          <template #unchecked>{{ t("reach.status.no") }}</template>
        </a-switch>
      </a-form-item>
    </a-col>
    <a-col :span="4">
      <a-form-item>
        <template #label>
          {{ t("reach.edit.communicationLimitModel") }}
          <a-tooltip :content="t('reach.edit.limitFrequencyModel')">
            <icon-info-circle />
          </a-tooltip>
        </template>
        <a-select v-model="limitSetting.modelName" :disabled="limitSetting.type == 'DISABLED'"
          :placeholder="t('reach.reminder.selectModel')" @change="changeModel">
          <a-option v-for="item of limitModels" :key="item.name" :value="item.name">{{ item.aliasName }}</a-option>
        </a-select>
      </a-form-item>
    </a-col>
    <a-col :span="8">
      <a-form-item>
        <template #label>
          {{ t("reach.edit.communicationLimitIdentificationField") }}
          <a-tooltip :content="t('reach.reminder.communicationLimitIdentificationField')">
            <icon-info-circle />
          </a-tooltip>
        </template>
        <a-tree-select v-model="limitSetting.identifyField" :disabled="limitSetting.type == 'DISABLED'"
          :data="modelFields" allow-clear allow-search :field-names="treeFieldStruct"
          :placeholder="t('reach.reminder.field')" />
      </a-form-item>
    </a-col>
    <a-col :span="4">
      <a-form-item>
        <template #label>
          {{ t("reach.edit.annualCount") }}
          <a-tooltip :content="t('reach.reminder.annualCount')">
            <icon-info-circle />
          </a-tooltip>
        </template>
        <a-input-number v-model="limitSetting.dateLimit.year" :disabled="limitSetting.type == 'DISABLED'" />
      </a-form-item>
    </a-col>
    <a-col :span="4">
      <a-form-item>
        <template #label>
          {{ t("reach.edit.monthlyCount") }}
          <a-tooltip :content="t('reach.reminder.monthlyCount')">
            <icon-info-circle />
          </a-tooltip>
        </template>
        <a-input-number v-model="limitSetting.dateLimit.month" :disabled="limitSetting.type == 'DISABLED'" />
      </a-form-item>
    </a-col>
    <a-col :span="4">
      <a-form-item>
        <template #label>
          {{ t("reach.edit.dailyCount") }}
          <a-tooltip :content="t('reach.reminder.dailyCount')">
            <icon-info-circle />
          </a-tooltip>
        </template>
        <a-input-number v-model="limitSetting.dateLimit.day" :disabled="limitSetting.type == 'DISABLED'" />
      </a-form-item>
    </a-col>
  </a-row>

</template>



<script setup>
import { defineEmits, ref, watch, onMounted, getCurrentInstance } from "vue";
import { treeFieldStruct } from "@/constant/common"
import { formatFields } from "@/utils/field";
import { findBehaviorModelList, findBehaviorModelItem } from "@/api/behavior";

const {
  proxy: { t }
} = getCurrentInstance();


const emit = defineEmits(["update:limitSetting"]);

const props = defineProps(['limitSetting', 'modelFields']);

const limitSetting = ref(props.limitSetting);
const limitModels = ref([]);
const modelFields = ref([]);

const getModelFields = async () => {
  if (limitSetting.value.modelName === "Customer") {
    modelFields.value = props.modelFields;
  } else {
    const bModelId = limitModels.value.find((it) => it.name === limitSetting.value.modelName)?.id;
    const bModel = await findBehaviorModelItem(bModelId);
    modelFields.value = formatFields(bModel.fields);
  }
};

const changeModel = async () => {
  await getModelFields();
  limitSetting.value.identifyField = null
};

watch(limitSetting.value, (newData, oldData) => {
  emit("update:limitSetting", limitSetting.value);
},
  { immediate: true, deep: true }
);

onMounted(async () => {
  limitModels.value = await findBehaviorModelList({
    fields: "id,name,aliasName"
  });
  limitModels.value.unshift({ id: "Customer", name: "Customer", aliasName: "客户模型" });

  if (limitSetting.value.modelName) {
    await getModelFields();
  }
});
</script>

<style lang="less" scoped></style>
