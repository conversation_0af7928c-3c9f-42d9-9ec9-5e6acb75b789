<template>
  <module edit>
    <template #main>
      <a-form ref="formRef" layout="vertical" class="general-form" :model="entity">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-col :span="24">
              <a-form-item label="预算名称" field="name">
                <a-input v-model="entity.name" placeholder="请输入预算名称" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="关联活动" field="campaignId">
                <a-select v-model="entity.campaignId" placeholder="请选择关联活动">
                  <a-option v-for="item in campaignList" :key="item.id" :value="item.id" :label="item.name"></a-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <a-form-item label="预估金额" field="estimatedAmount">
                <a-input v-model="entity.estimatedAmount" placeholder="请输入预估金额" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="实际金额" field="realAmount">
                <a-input v-model="entity.realAmount" placeholder="请输入实际金额" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="开始时间" field="startTime">
                <a-date-picker v-model="entity.startTime" show-time style="width: 100%;" value-format="timestamp"
                  placeholder="请选择开始时间" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="结束时间" field="endTime">
                <a-date-picker v-model="entity.endTime" show-time style="width: 100%;" placeholder="请输入结束时间" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="描述信息" field="summary">
                <a-input v-model="entity.summary" placeholder="请输入描述信息" />
              </a-form-item>
            </a-col>
          </a-card>
        </a-space>
      </a-form>
    </template>
  </module>
</template>

<script>
import { ref, provide } from "vue";
import { useRoute } from "vue-router";
import { findItem, saveInfo } from "@/api/budget";
import { findCampaignList } from "@/api/campaign";
import { Message } from "@arco-design/web-vue";

export default {
  components: {},
  setup() {
    const route = useRoute();
    const module = ref({
      entityIdField: "id",
      entityName: `编辑预算`,
      mainPath: "/budget/budget",
      breadcrumb: [{
        name: "预算管理",
        path: '/budget/budget'
      }, {
        name: '新增预算'
      }],
    });
    let queryValue = route.query.id;
    let isEdit = !!queryValue;

    module.value.entityName = isEdit ? "编辑预算" : "新建预算";
    module.value.breadcrumb[1].name = isEdit ? "编辑预算" : "新建预算";
    const entity = ref({});
    const campaignList = ref([]);
    const bindData = async () => {
      if (isEdit) {
        entity.value = await findItem(queryValue)
      }
      campaignList.value = await findCampaignList()
    };
    const save = async () => {
      entity.value = await saveInfo({
        ...entity.value,
        startTime: new Date(entity.value.startTime),
        endTime: new Date(entity.value.endTime)
      });
      queryValue = entity.value.id
      isEdit = !!queryValue;
      module.value.entityName = isEdit ? "编辑预算" : "新建预算";
      Message.success('提交成功！');
      await bindData()
    };
    const setup = {
      module,
      entity,
      bindData,
      campaignList,
      save
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style  lang="less" scoped></style>
