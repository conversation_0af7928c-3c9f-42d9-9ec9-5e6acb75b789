<template>
  <div :key="campaign.id" class="task" :class="{
    once: campaign.type === 'SINGLE',
    event: campaign.type === 'EVNET',
    repetition: campaign.type === 'PERIOD',
  }">
    <div class="task-title" :class="campaign.status">
      <div class="task-name" :title="campaign.name">
        <div class="name">
          <span>{{ campaign.name }}</span>
        </div>
        <span class="task-status" :class="campaign.status">{{
          filters(campaignStatus, campaign.status)
        }}</span>
      </div>
      <span>
        <a-dropdown @select="handleSelect">
          <a-button size="mini" type="text">操作</a-button>
          <template #content>
            <a-doption @click="handleModifyTask($event, campaign)">
              编辑
            </a-doption>
            <a-doption @click="main.showBudget(campaign)">预算</a-doption>

            <template v-if="campaign.status === 'DRAFT'">
              <a-doption @click="main.modifyFlow(campaign)">设计</a-doption>
              <a-doption @click="updateStatus('commit')">提交</a-doption>
              <a-doption @click="deleteCampaign">删除</a-doption>
            </template>

            <template v-if="campaign.status === 'COMMITTED'">
              <a-doption @click="main.modifyFlow(campaign)">设计</a-doption>
              <a-doption @click="updateStatus('approve')">审批</a-doption>
              <a-doption @click="deleteCampaign">删除</a-doption>
            </template>

            <template v-if="campaign.status === 'APPROVED'">
              <a-doption @click="main.modifyFlow(campaign)">设计</a-doption>
              <a-doption @click="startTask">启动</a-doption>
              <a-doption @click="deleteCampaign">删除</a-doption>
            </template>

            <template v-if="campaign.status === 'REJECTED'">
              <a-doption @click="main.modifyFlow(campaign)">设计</a-doption>
              <a-doption @click="updateStatus('commit')">提交</a-doption>
              <a-doption @click="deleteCampaign">删除</a-doption>
            </template>

            <template v-if="campaign.status === 'RUNNING'">
              <a-doption @click="main.viewFlow(campaign)">查看</a-doption>
              <a-doption>追加</a-doption>
              <a-doption @click="pauseTask">暂停</a-doption>
              <a-doption @click="stopTask">停止</a-doption>
              <a-doption>报表</a-doption>
            </template>

            <template v-if="campaign.status === 'PAUSED'">
              <a-doption @click="main.modifyFlow(campaign)">设计</a-doption>
              <a-doption @click="resumeTask">继续</a-doption>
              <a-doption @click="stopTask">停止</a-doption>
              <a-doption>报表</a-doption>
            </template>

            <template v-if="campaign.status === 'FINISHED'">
              <a-doption @click="main.viewFlow(campaign)">查看</a-doption>
              <a-doption>报表</a-doption>
            </template>
          </template>
        </a-dropdown>
      </span>
    </div>
    <div class="task-tags">
      <a-tag color="blue" class="arco-space-item" size="small">
        {{ filters(campaignType, campaign.type) }}
      </a-tag>
      <a-tag v-for="tag in campaign.tags" :key="tag" color="gray" class="arco-space-item" size="small">
        {{ tag }}
      </a-tag>
    </div>
    <div class="task-body">
      <div class="task-time">
        <span class="task-label">活动周期</span>
        <span class="task-value">{{ dateFilter(campaign.startTime) }} 至
          {{ dateFilter(campaign.endTime) }}</span>
      </div>
      <div class="task-time">
        <span class="task-label">创建时间</span>
        <span class="task-value">{{ dateFilter(campaign.createdTime) }} </span>
      </div>
      <!--      <div class="task-desc">活动描述</div>-->
    </div>
    <a-modal v-model:visible="commitModel" @ok="commitTask">
      <template #title> 提交审批 </template>
      <task-commit ref="taskCommitRef" />
    </a-modal>
    <a-modal v-model:visible="approveModel" @ok="approveTask">
      <template #title> 审批活动 </template>
      <task-approve ref="taskApproveRef" />
    </a-modal>
  </div>
</template>

<script>
import { ref, inject } from "vue";
import { Modal } from "@arco-design/web-vue";
import { filters } from "@/utils/filter";
import { campaignType, campaignStatus } from "@/constant/campaign";
import {
  commitCampaign,
  approveCampaign,
  getCampaign,
  startCampaign,
  stopCampaign,
  pauseCampaign,
  resumeCampaign,
} from "@/api/campaign";
import TaskCommit from "./task-commit.vue";
import TaskApprove from "./task-approve-dlg.vue";

export default {
  components: {
    TaskCommit,
    TaskApprove,
  },
  props: {
    task: {
      type: Object,
      default: () => {
        return {
          tags: [],
          name: null,
          flowData: "",
          status: null,
          groupId: null,
          type: "INBOUND",
          endTime: null,
          startTime: null,
          createdBy: null,
          updatedBy: null,
          updatedName: null,
          updatedTime: null,
          createdName: null,
          createdTime: null,
          campaignCode: null,
          setting: {
            repeatAllow: true,
          },
        };
      },
    },
    campaignStatus: {
      type: [Array, Object],
      default: () => { },
    },
    handleModifyTask: Function,
  },
  setup(props) {
    const main = inject("main");
    const campaign = ref(props.task);
    const commitModel = ref(false);
    const approveModel = ref(false);
    const taskCommitRef = ref(null);
    const taskApproveRef = ref(null);

    const handleSelect = (v) => {
      console.log(v);
    };
    /**
     * 时间格式处理器
     */
    const dateFilter = (time) => {
      return time ? time.substr(0, 10) : "";
    };
    /**
     * 删除活动
     */
    const deleteCampaign = () => {
      Modal.confirm({
        title: "删除营销活动",
        content: `是否确认删除营销活动 ${campaign.value.name}？`,
        onOk: () => {
          main.deleteTask(campaign.value);
        },
      });
    };

    // const commitModel = () => {
    //   Modal.open({
    //     title: "提交审批",
    //     content: "是否确认删除营销活动？",
    //     onOk: commitTask,
    //   });
    // };
    /**
     * 提交活动
     */
    const commitTask = async () => {
      const data = taskCommitRef.value.getData();
      data.campaignId = campaign.value.id;
      data.timestamp = new Date();
      await commitCampaign(data);
      refreshTask();
    };
    /**
     * 审批活动
     */
    const approveTask = async () => {
      const data = taskApproveRef.value.getData();
      data.campaignId = campaign.value.id;
      data.timestamp = new Date();
      await approveCampaign(data);
      refreshTask();
    };

    const startTask = async () => {
      Modal.confirm({
        title: "启动营销活动",
        content: "是否确认启动营销活动？",
        onOk: async () => {
          await startCampaign(campaign.value.id);
          refreshTask();
        },
      });
    };

    const stopTask = async () => {
      Modal.confirm({
        title: "停止营销活动",
        content: "是否确认停止营销活动？",
        onOk: async () => {
          await stopCampaign(campaign.value.id);
          refreshTask();
        },
      });
    };

    const pauseTask = async () => {
      Modal.confirm({
        title: "暂停营销活动",
        content: "是否确认暂停营销活动？",
        onOk: async () => {
          await pauseCampaign(campaign.value.id);
          refreshTask();
        },
      });
    };
    const resumeTask = async () => {
      Modal.confirm({
        title: "恢复营销活动",
        content: "是否确认恢复营销活动？",
        onOk: async () => {
          await resumeCampaign(campaign.value.id);
          refreshTask();
        },
      });
    };

    const refreshTask = async () => {
      campaign.value = await getCampaign(campaign.value.id);
    };
    const handleModifyFlow = (task) => {
      main.modifyFlow(task);
    };
    return {
      campaign,
      campaignType,
      campaignStatus,
      deleteCampaign,
      commitModel,
      approveModel,
      filters,
      handleSelect,
      dateFilter,
      commitTask,
      approveTask,
      startTask,
      pauseTask,
      resumeTask,
      stopTask,
      refreshTask,
      taskCommitRef,
      taskApproveRef,
      main,
    };
  },
  methods: {
    hidePopper() {
      this.$refs.task.hidePopper();
    },
    updateStatus(status, task) {
      if (status == "commit") {
        this.commitModel = true;
      } else if (status == "approve") {
        this.approveModel = true;
      }
    },
    handleBudget(e, campaign) { },
  },
};
</script>

<style lang="less" scoped>
.task {
  min-height: 110px;
  padding: 0 3px 0 10px;
  border-radius: 4px;
  position: relative;
  margin-bottom: 10px;
  background: #ffffff;
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.07);

  .task-title {
    height: 30px;
    cursor: move;
    display: flex;
    font-size: 14px;
    color: #333333;
    align-items: center;
    justify-content: space-between;

    .task-name {
      width: 170px;
      display: flex;

      .name {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 120px;
        overflow: hidden;
      }
    }

    .task-status {
      font-size: x-small;
      color: #677074;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      border-radius: var(--border-radius-medium);
      margin: 0 4px;
      padding: 0 6px;

      &.DRAFT {
        background-color: rgba(var(--green-2));
        color: rgba(var(--green-7));
      }

      &.COMMITTED {
        background-color: rgba(var(--lime-2));
        color: rgba(var(--lime-7));
      }

      &.APPROVED {
        background-color: rgba(var(--cyan-2));
        color: rgba(var(--cyan-7));
      }

      &.REJECTED {
        background-color: rgba(var(--gold-2));
        color: rgba(var(--gold-7));
      }

      &.RUNNING {
        background-color: rgba(var(--blue-2));
        color: rgba(var(--blue-7));
      }

      &.PAUSED {
        background-color: rgba(var(--magent-2));
        color: rgba(var(--magent-7));
      }

      &.FINISHED {
        background-color: rgba(var(--arcoblue-2));
        color: rgba(var(--arcoblue-7));
      }

      &.STOP {
        background-color: rgba(var(--gray-2));
        color: rgba(var(--gray-7));
      }
    }

    .task-action {
      cursor: pointer;
      margin-left: 5px;
    }

    .task-action-menu {
      border-radius: 5px;
      box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.07);

      .iconfont {}

      ul {
        list-style: none;
        padding: 8px 0;

        li {
          cursor: pointer;
          font-size: 12px;
          color: #333333;
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          transition: all ease 0.3s;

          &::before {
            margin-right: 2px;
          }

          &:hover {
            color: #333333;
            background: #f9f9f9;
          }
        }
      }
    }
  }

  .task-tags {
    margin-bottom: 5px;

    .task-tag {
      padding: 1px 10px;
      margin-right: 3px;
      border-radius: 8px;
      background: #ffffff;
      border: 1px solid #dddddd;
    }

    .task-tag-fixed {
      color: #677074;
      padding: 1px 10px;
      margin-right: 3px;
      border-radius: 3px;
      background: #ffffff;
      border: 1px solid #dddddd;
    }
  }

  .task-body {
    font-size: 12px;
    color: #666666;
    padding: 5px;

    .task-time {
      justify-content: space-between;
      display: flex;
      margin-bottom: 5px;

      .task-label {
        font-weight: bold;
      }

      .task-value {
        color: #999999;
      }
    }
  }

  &::before {
    left: 0;
    width: 5px;
    content: "";
    height: 100%;
    position: absolute;
    border-radius: 4px 0 0 4px;
    background: linear-gradient(44deg, #39bcc5, #73ddaa);
  }

  &.event {
    &::before {
      background: linear-gradient(44deg, #39bcc5, #73ddaa);
    }
  }

  &.once {
    &::before {
      background: linear-gradient(90deg, #85c0fa 0%, #4594f3 100%);
    }
  }

  &.repetition {
    &::before {
      background: linear-gradient(44deg, #ff6d69, #ffa295);
    }
  }

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {}

  .task-desc {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .arco-space-item {
    margin: 0 5px 5px 0;
  }
}</style>
