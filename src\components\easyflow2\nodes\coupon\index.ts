import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import CouponNode from "./node.vue";
import CouponPannel from "./pannel.vue";

const nodeData = {
  type: "coupon",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<CouponNode />`,
      components: {
        CouponNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("CouponNode", nodeData.node, true);
};

const Coupon = {
  type: "coupon",
  name: "优惠券",
  shape: "CouponNode",
  iconClass: "icon--coupon-2",
  color: "#ffffff",
  themebg: "#4594f3",
  registerNode: registerNode,
  pannel: CouponPannel,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Coupon;
