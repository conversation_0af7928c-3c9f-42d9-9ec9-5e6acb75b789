export interface AnyObject {
  [key: string]: unknown;
}

export interface Options {
  value: unknown;
  label: string;
}

export interface NodeOptions extends Options {
  children?: NodeOptions[];
}

export interface GetParams {
  body: null;
  type: string;
  url: string;
}

export interface PostData {
  body: string;
  type: string;
  url: string;
}

export interface Pagination {
  current: number;
  pageSize: number;
  total?: number;
}

export interface BussinessUnit {
  code: string;
  createdTime: string;
  description: string;
  id: string;
  invalid: boolean;
  logo: string;
  name: string;
  tenantId: string;
}

export interface PageEntity {
  sort: object;
  size: number;
  last: boolean;
  content: any[];
  first: boolean;
  number: number;
  pageable: object;
  totalPages: number;
  totalElements: number;
  numberOfElements: number;
}

export interface grantedApplication {
  aliasName: string;
  granted: boolean;
  icon: string;
  name: string;
  shortAliasName: string;
  themeColor: string;
  url: string;
}

export type TimeRanger = [string, string];

export interface GeneralChart {
  xAxis: string[];
  data: Array<{ name: string; value: number[] }>;
}
