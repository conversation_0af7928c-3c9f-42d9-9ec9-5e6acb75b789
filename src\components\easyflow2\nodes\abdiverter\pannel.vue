<template>
  <div class="easyflow-pannel-abtest">
    <a-form class="easyflow-pannel-form pannel-form"
            layout="vertical"
            :model="entity"
            :disabled="!editEnable">
      <a-form-item label="A/B测试名称">
        <a-input v-model="entity.abTestName" />
      </a-form-item>
      <a-form-item label="指标配置"
                   :content-flex="false">
        <a-row class="group-item"
               :gutter="24">
          <a-col :span="7"
                 class="item-label">
            选择指标
          </a-col>
          <a-col :span="17">
            <a-select v-model="entity.metrics[0].type"
                      placeholder="请选择">
              <a-option value="CONVERSION">转化率</a-option>
              <a-option value="REVENUE">收入</a-option>
              <a-option value="ENGAGEMENT">签约率</a-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row class="group-item"
               :gutter="24">
          <a-col :span="7"
                 class="item-label">
            原指标比率
          </a-col>
          <a-col :span="17">
            <a-input-number v-model="entity.metrics[0].originalValue"
                            :precision="2">
              <template #append> % </template>
            </a-input-number>
          </a-col>
        </a-row>
        <a-row class="group-item"
               :gutter="24">
          <a-col :span="7"
                 class="item-label">
            期望提升比例
          </a-col>
          <a-col :span="17">
            <a-input-number v-model="entity.metrics[0].targetValue"
                            :precision="2">
              <template #append> % </template>
            </a-input-number>
          </a-col>
        </a-row>
      </a-form-item>
      <a-form-item label="实验配置"
                   :content-flex="false">
        <a-row class="group-item">
          <a-col :span="7"
                 class="item-label">
            占用实验比例
          </a-col>
          <a-col :span="17">
            <a-input-number v-model="entity.experiment"
                            :precision="0">
              <template #append> % </template>
            </a-input-number>
          </a-col>
        </a-row>
        <a-row class="group-item"
               :gutter="24">
          <a-col :span="7"
                 class="item-label">
            设置分组
          </a-col>
          <a-col :span="17">
            <a-radio-group v-model="entity.groupMode">
              <a-radio value="AVERAGE">平均分配</a-radio>
              <a-radio value="SIGNIFICANT">显著抽样</a-radio>
              <a-radio value="MANUAL">手动分配</a-radio>
            </a-radio-group>
          </a-col>
        </a-row>
        <template v-if="entity.groupMode == 'MANUAL'">
          <template v-for="(item, index) in entity.branches"
                    :key="index">
            <a-row class="group-item"
                   :gutter="24">
              <a-col :span="7"
                     class="item-label">
                {{ item.name }}
              </a-col>
              <a-col :span="17">
                <a-input-number v-model="item.ratio"
                                :precision="0">
                  <template #append> % </template>
                </a-input-number>
              </a-col>

            </a-row>
          </template>
        </template>
        <a-row class="group-item"
               :gutter="24">
          <a-col :span="7"
                 class="item-label">
            实验时长
          </a-col>
          <a-col :span="17">
            <a-input-number v-model="entity.duration"
                            :precision="0">
              <template #append> 天 </template>
            </a-input-number>
          </a-col>
        </a-row>
      </a-form-item>
      <a-form-item label="正式发送配置"
                   :content-flex="false">
        <a-row class="group-item"
               :gutter="24">
          <a-col :span="7"
                 class="item-label">
            推送选择
          </a-col>
          <a-col :span="17">
            <a-radio-group v-model="entity.confirmType">
              <a-radio value="RANDOM">随机选择</a-radio>
              <a-radio value="BEST">择优选择</a-radio>
              <a-radio value="MANUAL">手动指定</a-radio>
            </a-radio-group>
          </a-col>
        </a-row>
        <a-row v-if="entity.confirmType === 'MANUAL'"
               class="group-item"
               :gutter="24">
          <a-col :span="7"
                 class="item-label">
            选择分组
          </a-col>
          <a-col :span="17">
            <a-select v-model="entity.configId"
                      placeholder="请选择">
              <a-option v-for="item in entity.branches"
                        :key="item.id"
                        :value="item.id">{{ item.name }}</a-option>
            </a-select>
          </a-col>
        </a-row>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import {ref, inject, onMounted} from 'vue'
import {findMetricList} from '@/api/analysis'
import {uuid} from '@/utils/uuid'

const flow = inject('flow')
const props = defineProps(['node', 'connections'])
const loading = ref(false)
const {node} = props
const {connections} = props
const pannelInject = inject('pannel')
const {editEnable} = pannelInject

const entity = ref({
  branches: [],
  metrics: [
    {
      type: '',
      originalValue: '',
      targetValue: ''
    }
  ]
})
const save = () => {
  entity.value.branches.forEach(it => {
    if (!it.id) {
      it.id = uuid(20)
    }
  })
  return entity.value
}

defineExpose({
  save
})

const addItem = (outgoing, name) => {
  return {
    name,
    outgoing
  }
}
onMounted(() => {
  Object.assign(entity.value, node.data)

  if (connections.outgoings) {
    entity.value.branches = connections.outgoings.map(it => {
      const found = entity.value.branches.find((item, index) => {
        return item.outgoing === it.id
      })
      if (found) {
        found.name = it.name
        return found
      }
      return addItem(it.id, it.name)
    })
  } else {
    entity.value.branches = []
  }
})
</script>


<style lang="less" scoped>
.group-item {
  margin: 10px 0;

  .item-label {
    text-align: right;
    padding-top: 4px;
    padding-bottom: 4px;
  }
}
</style>
