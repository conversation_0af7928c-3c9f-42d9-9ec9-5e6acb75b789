<template>
  <a-modal v-model:visible="visible"
           @before-ok="handleOk"
           @before-cancel="handleCancel">
    <template #title> {{t('campaign.canvas.saveAsTitle')}} </template>

    <a-form ref="formRef"
            class="save-as-form"
            :model="entity">
      <a-form-item :label="t('campaign.column.campagin')"
                   field="newGroup"
                   :rules="[{ required: true, message: '活动不能为空' }]">
        <a-select v-model="entity.newGroup"
                  @change="selectGroup">
          <a-option v-for="item in groupList"
                    :key="item.id"
                    :value="item.id">{{item.name}}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="t('campaign.column.canvasId')"
                   field="newId">
        <a-input v-model="entity.newId"
                 :placeholder="t('campaign.reminder.input_canvas_id')" />
      </a-form-item>
      <a-form-item :label="t('campaign.column.canvasName')"
                   field="newName"
                   :rules="[{ required: true, message: '画布名称不能为空' }]">
        <a-input v-model="entity.newName"
                 :placeholder="t('campaign.reminder.input_activity_name')" />
      </a-form-item>
      <a-form-item :label="t('campaign.column.code')"
                   field="newCampaignCode"
                   :rules="[{ required: true, message: '活动编码不能为空' }]">
        <a-input v-model="entity.newCampaignCode"
                 :disabled="owner?.setting?.customizationSetting?.manualCampaignCode != 'ENABLED'"
                 :placeholder="t('campaign.reminder.input_activity_code')" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { useBussinessUnitStore } from "@/store";
import { saveAsCampaign } from "@/api/campaign";
import { findGroupList } from "@/api/group";
import { Notification } from "@arco-design/web-vue";

const {
      proxy: { t }
    } = getCurrentInstance();

const owner = useBussinessUnitStore().marketingCenter;
let callback;
const visible = ref(false);
const formRef = ref(null);
const entity = ref({});
const campaignId = ref(null);
const groupList = ref();

const show = async (_campaignId) => {
  campaignId.value = _campaignId;
  groupList.value = await findGroupList({ sort: "priority,ASC" });
  visible.value = true;
  return new Promise(async (resolve, reject) => {
    callback = resolve;
  });
};

const selectGroup = (id) => {
  entity.value.newCampaignCode = groupList.value.find(
    (it) => it.id === id
  ).code;
  // entity.value.newCampaignCode =
};

const handleOk = async () => {
  let res = await formRef.value.validate();
  if (!res) {
    await saveAsCampaign(campaignId.value, entity.value);
    Notification.info({
      title: "活动另存",
      content: "活动另存成功。"
    });
    visible.value = false;
    entity.value = {};
    callback();
  } else {
    return false
  }

};

const handleCancel = async () => {
  callback();
};

defineExpose({
  show
});
</script>

<style lang="less" scoped></style>
