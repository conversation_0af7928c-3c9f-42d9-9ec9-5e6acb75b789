/**
  比较节点人群差异
 */
<template>
  <a-modal v-model:visible="visible" @before-ok="handleOk">
    <template v-if="!dataForm.type" #title>比较人群差异</template>
    <div class="compare">
      <div class="col">
        <a-form ref="dataFormRef" layout="vertical" :model="dataForm">
          <a-form-item field="leftType"
                       :rules="[{required:true,message:'请选择人群类型'}]"
                       :validate-trigger="['change','input']"
                       label="类型">
            <a-select v-model="dataForm.leftType"
                      placeholder="请选择人群类型"
                      @change="leftTypeChange">
              <a-option v-for="item in type"
                        :value="item.id"
                        :label="item.name"
                        :key="item.id"></a-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="dataForm.leftType!='CAMPAIGN'"
                       field="leftMasterId"
                       :rules="[{required:true,message:'请选择人群'}]"
                       :validate-trigger="['change','input']"
                       label="人群">
            <a-select v-model="dataForm.leftMasterId"
                      placeholder="请选择人群"
                      @change="leftMasterChange">
              <a-option v-for="item in leftMasterDataList"
                        :value="item.id"
                        :label="item.name"
                        :key="item.id"></a-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="dataForm.leftType=='SNAPSHOT'"
                       field="leftSubId"
                       :rules="[{required:true,message:'请选择人群快照'}]"
                       :validate-trigger="['change','input']"
                       label="人群快照">
            <a-select v-model="dataForm.leftSubId"
                      placeholder="请选择人群快照">
              <a-option v-for="item in leftSubDataList"
                        :value="item.id"
                        :label="item.name"
                        :key="item.id"></a-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="dataForm.leftType=='CAMPAIGN'"
                       field="leftSubId"
                       :rules="[{required:true,message:'请选择活动节点'}]"
                       :validate-trigger="['change','input']"
                       label="活动节点">
            <a-select v-model="dataForm.leftSubId"
                      placeholder="请选择活动节点">
              <a-option v-for="item in leftSubDataList"
                        :value="item.id"
                        :label="item.name"
                        :key="item.id"></a-option>
            </a-select>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import {Message, Modal} from "@arco-design/web-vue";
import { modalCommit } from "@/utils/modal";
import moment from "moment";
import { findAudienceList, getCrowdSnapshot, createCompareJob } from "@/api/audience";
import {getFlowItem} from "@/api/campaign"

const props = defineProps(["budgetId", "bindData"]);
const flowId = ref("");
const node = ref({});
const bindData = props.bindData;
const visible = ref(false);
const dataForm = ref({});
const dataFormRef = ref({});
const leftMasterDataList = ref([]);
const leftSubDataList = ref([]);
const type = [
  { id: "AUDIENCE", name: "人群" },
  { id: "SNAPSHOT", name: "快照" },
  { id: "CAMPAIGN", name: "活动节点" },
]
const typeList = () => {
  const item = [
    { value: "AUDIENCE", label: "人群" },
    { value: "SNAPSHOT", label: "快照" },
    { value: "CAMPAIGN", label: "活动节点" },
  ].find((x) => {
    return x.value === dataForm.value.status;
  });
  return item.label;
};

const handleOk = async () => {
  dataForm.value.rightMasterId = flowId.value
  dataForm.value.rightSubId = node.value.id
  dataForm.value.rightType = 'CAMPAIGN'
  const res = await dataFormRef.value.validate();
  if (res) { return false }

  await createCompareJob(dataForm.value)
  Message.success("创建人群比较任务成功,可在任务管理中查看")
  visible.value = false;
};

const show = () => {
  visible.value = true;
};

const create = (group, annual) => {
  visible.value = true;
};

const edit = (data, type) => {
  dataFormRef.value.clearValidate();
  let item = JSON.parse(JSON.stringify(data));
  dataForm.value = item;
  visible.value = true;
};

const leftTypeChange = async () => {
  if(dataForm.value.leftType != 'CAMPAIGN'){
    leftMasterDataList.value = await findAudienceList({ fields: "name" })
    await leftMasterChange()
  }else {
    const flowData = await getFlowItem(flowId.value)
    dataForm.value.leftMasterId = flowId.value
    leftSubDataList.value = flowData.nodes
  }
}
const leftMasterChange = async () => {
  if (dataForm.value.leftType == 'SNAPSHOT' && dataForm.value.leftMasterId) {
    const data = await getCrowdSnapshot({ expression: "audienceId eq " + dataForm.value.leftMasterId, fields: "id,cdp_update_time" })
    data.content.forEach(item => {
      leftSubDataList.value.push({ id: item.id, name: moment(item.cdp_update_time).format('YYYY-MM-DD HH:mm:ss') })
    })
  }
}

defineExpose({ show, create, edit,flowId,node });
</script>
<style  lang="less" scoped>
.compare {
  display: flex;
  width: 100%;
  .title{
    font-weight: bold;
    font-size: 20px;
    text-align: center;
  }
  .col {
    width: 45%;
    background-color: rgba(249, 249, 172, 0.455);
    margin: 10px;
    padding: 10px;
  }
}
</style>
