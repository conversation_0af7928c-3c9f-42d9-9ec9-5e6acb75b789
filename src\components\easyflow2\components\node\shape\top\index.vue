<template>
  <div class="flow-node-top">
    <!-- <flow-mark @click="handleMarkClick"></flow-mark> -->
    <!-- <flow-record></flow-record> -->
  </div>
</template>

<script>
import mark from "./mark.vue";
import flowRecord from "./record.vue";

export default {
  components: {
    "flow-mark": mark,
    "flow-record": flowRecord,
  },
  data() {
    return {};
  },
  methods: {
    handleMarkClick(e) {
      e.stopPropagation();
    },
  },
};
</script>

<style lang="less" scoped>
.flow-node-top {
  top: -20px;
  width: 100%;
  height: 20px;
  display: flex;
  position: absolute;
  align-items: center;
  justify-content: flex-start;
}
</style>
