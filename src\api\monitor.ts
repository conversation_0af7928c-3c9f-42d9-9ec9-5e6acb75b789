/**
 * 监控数据管理接口
 */
 import axios from "axios";
 import { QueryInfo, Params } from "@/types/api";
 import { useBussinessUnitStore } from "@/store";

 const userBussinessUnitStore = useBussinessUnitStore();
 const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
 const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

 export function monitorCampaign(info: any) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/flow-monitor/query`, info);
}

 export function getMonitorScroll(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-monitor/scroll`, {
    params: {
      ...params,
      ...query
    }
  });
}

export function getMonitorPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-monitor/record/page`, {
    params: {
      ...params,
      ...query
    }
  });
}

export function createMonitorJob(expression: String) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/job/flow-monitor?expression=${expression}`);
}

export function getErrorDetail(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-monitor/page`, {
    params: {
      ...params,
      ...query
    }
  });
}

export function getReachSummary(id: String, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-monitor/reach-summary/${id}`, {
    params
  });
}

export function getAllReachSummary(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-monitor/reach-summary`, {
    params
  });
}

