<template>
  <module view>
    <template #main>
      <a-form ref="formRef"
              layout="vertical"
              class="general-form abtest-form"
              disabled
              :model="entity">
        <a-card class="general-card">
          <div class="tab-content">
            <div class="item-text w100"><span>{{ $t('ab.detail.testName') }}:</span>{{ entity.name }}</div>
            <div class="item-text">
              <span>{{ $t('ab.detail.marketingCampaign') }}:</span>{{ entity.name }}
            </div>
            <div class="item-text">
              <span>{{ $t('ab.detail.testGroupType') }}:</span>
              {{ filters(groupTypes, entity.groupType, 'name', 'id') }}
            </div>
            <div class="item-text">
              <span>{{ $t('ab.detail.relatedCampaign') }}:</span>
              {{ entity.campaignName }}
            </div>
            <div class="item-text">
              <span>{{ $t('ab.detail.testMetrics') }}:</span>
              {{ filters(metrics, entity.metricId, 'name', 'id') }}
            </div>
            <div class="item-text w100">
              <span>{{ $t('ab.detail.testObjective') }}:</span>
              {{ filters(targets, entity.target, 'label', 'value') }}{{ entity.targetValue }}
            </div>
            <div class="item-text w100">
              <span>{{ $t('ab.detail.group') }}:</span>
              <a-card-grid v-for="(item, index) in entity.groups"
                           :key="index"
                           :hoverable="index % 2 === 0"
                           :style="{ width: '25%' }">
                <a-card class="group-card"
                        :title="item.name"
                        :bordered="false">
                  <p :style="{ margin: 0 }">
                    {{ $t('ab.detail.percentage') }}: <i>{{ item.ratio }}%</i>
                  </p>
                </a-card>
              </a-card-grid>
            </div>
          </div>
        </a-card>
      </a-form>
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance } from "vue";
import { useRoute, useRouter } from "vue-router";
import { findMetricList, getAbtest } from "@/api/analysis";
import { getCampaign } from "@/api/campaign";
import { targets } from "@/constant/abtest";
import { filters } from "@/utils/filter";

export default {
  components: {},
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const queryId = route.query.id;
    const module = ref({
      entityIdField: "id",
      // entityName: `查看A/B测试`,
      entityName: t('ab.viewABTitle'),
      breadcrumb: [
        {
          // name: "A/B测试",
          name: t('ab.title'),
          path: "/ab/abtest",
        },
        {
          // name: "A/B测试详情",
          name: t('ab.detail.title'),
        },
      ],
      mainPath: "/ab/abtest",
    });

    const loading = ref(false);
    const entity = ref({});
    const metrics = ref([]);
    const groupTypes = [
      { id: "RATIO", name: t('ab.detail.ratio') },
      { id: "NUMBER", name: t('ab.detail.value') }
    ];
    const findMetrics = async (expression) => {
      metrics.value = await findMetricList({
        expression,
        fields: "name",
      });
    };

    const bindData = async () => {
      // await findMetrics();
      entity.value = await getAbtest(queryId);
      const res = await getCampaign(entity.value.flowId);
      entity.value.campaignName = res.name
    };

    const setup = {
      module,
      loading,
      entity,
      targets: ref(targets),
      metrics,
      groupTypes,
      bindData,
      filters,
    };
    provide("view", setup);
    return setup;
  },
};
</script>

<style  lang="less" scoped>
.abtest-form {
  .arco-card-grid {
    margin: 0 10px;
  }
}

.tab-content {
  //background-color: rgb(247 248 250);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  color: #999999;
  padding: 20px;
  font-size: 14px;
  position: relative;

  .item-status {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .item-text {
    width: 50%;
    line-height: 35px;
    display: flex;
  }

  .w100 {
    width: 100%;
  }

  span {
    color: #333333;
    margin-right: 10px;
    min-width: 90px;
  }
}
</style>
