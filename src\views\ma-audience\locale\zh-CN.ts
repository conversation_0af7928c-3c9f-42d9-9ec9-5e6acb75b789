export default {
  "menu.audience": "人群中心",
  "menu.audience.audience": "活动人群",
  "menu.audience.prop": "用户属性",

  audience :{
    title: "活动人群",
    id: "人群ID",
    name: "人群名称",

    popup: {
      create_title: "新建人群",
      condtion_create_title: "条件创建人群",
      select_title: "选择客户标签",
      compare_audience_title: "比较人群差异",
      leftAudience: "左侧人群",
      rightAudience: "右侧人群",
      type: "类型",
      audience: "人群",
      snapshot: "快照",
      audienceSnapshot: "人群快照",
      optionalLabel:"可选标签",
      selectedLabel:"已选标签",
    },

    button: {
      search: "查询",
      reset: "重置",
      create: "新建",
      data_warehouse_sync: "数仓同步",
      audience_comparison: "人群比较",
    },

    detail: {
      title: "人群详情",
      detail: "人群明细",
      snapshot: "人群快照",
    },

    edit: {
      title: "编辑条件人群",
      name: "人群名称",
      testSwitch: "测试人群开关",
      realTrigger:"真实触发",
      testAudience:"测试人群",
      estimatedAudience: "预估人群",
      description: "描述信息",
      fieldFiltering: "属性字段过滤",
      tagFiltering: "标签过滤",
      requiredTag:"必选标签",
      optionalTag:"可选标签",
      excludeTag:"排除标签",
    },

    analysis: {
      title: "活动人群-分析",
      designCampaignFlowchart: "设计活动流程图",
      craete_compare_audience: "添加对比人群",
    },

    operation: {
      view: "查看",
      edit: "编辑",
      delete: "删除"
    },

    column: {
      id: "人群ID",
      name: "人群名称",
      selection_type: "圈选类型",
      audience_usage: "人群用途",
      modifiable: "可修改",
      description_info: "描述信息",
      action: "操作",
    },

    reminder: {
      input_audience_id: "请输入人群ID",
      input_audience_name: "请输入人群名称",
      input_description: "请输入描述信息",
      data_irretrievable_warning: "删除之后数据不可恢复，请确认是否删除？",
      delete_audience: "删除人群",
      sysn_audience: "同步人群完成",
      generate_new_audience_by_filtering: "通过条件过滤生成新的人群",
      notEmpty: "不能为空",
      
      field:"请选择字段",
      operator:"操作符",
      value:"请输入值",
      allTags:"客户必须命中以下标签全部",
      atLeastOneTag:"客户需要至少命中以下一条标签",
      noTags:"客户不能命中以下任何一条标签", 
      selectAudienceType: "请选择人群类型",
      selectAudience: "请选择人群",
      selectAudienceSnapshot: "请选择人群快照",
      selectComparisonAudience: "请选择对比人群",

      submitSuccess: "提交成功",
      createAudienceSuccess: "创建人群比较任务成功, 可在任务管理中查看",
      
    },

    bool: {
      yes: "是",
      no: "否",
      and: "且",
      or: "或",
    }

  },

  'searchTable.form.number': '集合编号',
  'searchTable.form.number.placeholder': '请输入集合编号',
  'searchTable.form.name': '集合名称',
  'searchTable.form.name.placeholder': '请输入集合名称',
  'searchTable.form.contentType': '内容体裁',
  'searchTable.form.contentType.img': '图文',
  'searchTable.form.contentType.horizontalVideo': '横版短视频',
  'searchTable.form.contentType.verticalVideo': '竖版小视频',
  'searchTable.form.filterType': '筛选方式',
  'searchTable.form.filterType.artificial': '人工筛选',
  'searchTable.form.filterType.rules': '规则筛选',
  'searchTable.form.createdTime': '创建时间',
  'searchTable.form.status': '状态',
  'searchTable.form.status.online': '已上线',
  'searchTable.form.status.offline': '已下线',
  'searchTable.form.search': '查询',
  'searchTable.form.reset': '重置',
  'searchTable.form.selectDefault': '全部',
  'searchTable.operation.create': '新建',
  'searchTable.operation.import': '批量导入',
  'searchTable.operation.download': '下载',
  // columns
  'searchTable.columns.number': '集合编号',
  'searchTable.columns.name': '集合名称',
  'searchTable.columns.contentType': '内容体裁',
  'searchTable.columns.filterType': '筛选方式',
  'searchTable.columns.count': '内容量',
  'searchTable.columns.createdTime': '创建时间',
  'searchTable.columns.status': '状态',
  'searchTable.columns.operations': '操作',
  'searchTable.columns.operations.view': '查看',
};
