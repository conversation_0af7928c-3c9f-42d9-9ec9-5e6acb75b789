/** *by:<EMAIL> on 2022/8/4 0004 */
<template>
  <div class="dashboard-home">
    <MaBi :id="id" />
  </div>
</template>

<script>
import { ref } from "vue";
import MaBi from "@/components/bi/chart/index.vue";
import { useRoute } from "vue-router";

export default {
  name: "dashboard-home",
  components: {
    MaBi,
  },
  setup() {
    const route = useRoute();
    const biId = [
      "819944f5-cc20-4dd8-945a-b4153a560d4f",
      "e81aadb0-318c-4ba7-896e-478a02681fdd",
      "work_wwe45e28a1be51dfdd_main_dashboard",
      "3ce05974-c65c-4dec-a00c-eaa1c7a79b77",
    ];
    const id = ref(route.query.id || biId[3]);
    return {
      id,
    };
  },
};
</script>
<style lang="less" scoped>
.dashboard-home {
}
</style>
