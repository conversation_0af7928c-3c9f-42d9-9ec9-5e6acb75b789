<template>
  <a-card
    class="general-card"
    :title="$t('workplace.quick.operation')"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ padding: '24px 20px 0 20px' }"
  >
    <a-row :gutter="8">
      <a-col
        v-for="link in links"
        :key="link"
        :span="8"
        class="wrapper"
        @click="toPage(link.link)"
      >
        <div class="icon">
          <component :is="link.icon" />
        </div>
        <a-typography-paragraph class="text">
          {{ link.text }}
        </a-typography-paragraph>
      </a-col>
    </a-row>
    <a-divider class="split-line" style="margin: 0" />
  </a-card>
</template>

<script setup>
import { useRouter } from "vue-router";

const router = useRouter();
const links = [
  { text: "创建活动", icon: "icon-file", link: "/campaign/campaign" },
  { text: "沟通模板", icon: "icon-storage", link: "/communicate/template" },
  { text: "人群管理", icon: "icon-settings", link: "/audience/audience" },
  // { text: 'AB测试', icon: 'icon-mobile', link: '/ab/abtest' },
  { text: "系统设置", icon: "icon-fire", link: "/system/setting" }
];
const toPage = (url) => {
  router.push({ path: url });
};
</script>

<style scoped lang="less"></style>
