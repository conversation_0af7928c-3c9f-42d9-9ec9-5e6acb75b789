<template>
  <module main>
    <template #filter></template>
    <template #search></template>
    <template #context><span></span></template>
    <template #main>
      <a-table
        ref="table"
        :bordered="false"
        :data="itemAllSetting"
        :pagination="false"
        style="margin-bottom: 30px"
      >
        <template #columns>
          <a-table-column :title=" t('systemSetting.channelLimitation.name')" data-index="name" />
          <a-table-column :title=" t('systemSetting.channelLimitation.year')" data-index="limitSetting.YEAR">
            <template #cell="{ record }">
              <a-input-number
                v-model="record.limitSetting.YEAR"
                :disabled="!record.isEdit"
                :formatter="formatter"
                :parser="parser"
                :default-value="-1"
                :min="-1"
              />
            </template>
          </a-table-column>
          <a-table-column :title=" t('systemSetting.channelLimitation.month')" data-index="limitSetting.MONTH">
            <template #cell="{ record }">
              <a-input-number
                v-model="record.limitSetting.MONTH"
                :disabled="!record.isEdit"
                :formatter="formatter"
                :parser="parser"
                :default-value="-1"
                :min="-1"
              />
            </template>
          </a-table-column>
          <a-table-column :title=" t('systemSetting.channelLimitation.day')" data-index="limitSetting.DAY">
            <template #cell="{ record }">
              <a-input-number
                v-model="record.limitSetting.DAY"
                :disabled="!record.isEdit"
                :formatter="formatter"
                :parser="parser"
                :default-value="-1"
                :min="-1"
              />
            </template>
          </a-table-column>
          <a-table-column :title="t('global.button.operation')" align="center">
            <template #cell="{ record }">
              <a-button
                v-if="!record.isEdit"
                v-permission="['ma_menu.system.channel-limited.modify']"
                type="text"
                size="small"
                @click="record.isEdit = true"
                >{{t('global.button.update')}}</a-button
              >
              <a-button
                v-else
                v-permission="['ma_menu.system.channel-limited.modify']"
                type="text"
                size="small"
                @click="save(record)"
                >{{t('global.button.save')}}</a-button
              >
            </template>
          </a-table-column>
        </template>
      </a-table>
      <a-table
        ref="table"
        :bordered="false"
        :data="dataSource"
        :pagination="false"
      >
        <template #columns>
          <a-table-column :title=" t('systemSetting.channelLimitation.name')" data-index="name" />
          <a-table-column :title=" t('systemSetting.channelLimitation.year')" data-index="YEAR">
            <template #cell="{ record }">
              <a-input-number
                v-model="record.YEAR"
                :disabled="!record.isEdit"
                :formatter="formatter"
                :parser="parser"
                :default-value="-1"
                :min="-1"
              />
            </template>
          </a-table-column>
          <a-table-column :title=" t('systemSetting.channelLimitation.month')" data-index="MONTH">
            <template #cell="{ record }">
              <a-input-number
                v-model="record.MONTH"
                :disabled="!record.isEdit"
                :formatter="formatter"
                :parser="parser"
                :default-value="-1"
                :min="-1"
              />
            </template>
          </a-table-column>
          <a-table-column :title=" t('systemSetting.channelLimitation.day')" data-index="DAY">
            <template #cell="{ record }">
              <a-input-number
                v-model="record.DAY"
                :disabled="!record.isEdit"
                :formatter="formatter"
                :parser="parser"
                :default-value="-1"
                :min="-1"
              />
            </template>
          </a-table-column>
        </template>
      </a-table>
    </template>
  </module>
</template>

<script>
import { ref, provide, computed,getCurrentInstance } from "vue";
import { Message } from "@arco-design/web-vue";
import { findNodePage } from "@/api/node";
import {
  getAllChannelLimit,
  saveAllChannelLimit,
} from "@/api/marketing_center";
import { useBussinessUnitStore } from "@/store";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const userTc = useBussinessUnitStore();
    // 模块设置
    const module = ref({
      entityIdField: "id",
      entityName: t('systemSetting.channelLimitation.communicateLimit'),
      breadcrumb: [
        {
          name: t('menu.system'),
          path: "/system/setting",
        },
        {
          name: t('systemSetting.communicateChannel.communicateManage'),
        },
      ],
      mainPath: "/system/setting",
      showBtn: false,
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true,
    });

    // 过滤设置
    const filter = ref([
      {
        field: "name",
        label: t('systemSetting.channelLimitation.name'),
        component: "a-input",
        operate: "like",
        placeholder: t('systemSetting.channelLimitation.name'),
        comment: true,
        value: "",
      },
    ]);

    // 数据设置
    const entity = ref({});
    const channelEntity = ref({
      type: "DATE",

      dateLimit: {
        year: "",
        month: "",
        day: "",
      },
    });
    // 列表数据
    const itemAllSetting = ref([
      {
        id: "00001",
        name: t('systemSetting.channelLimitation.allChannel'),
        isEdit: false,
        limitSetting: {
          YEAR: -1,
          MONTH: -1,
          DAY: -1,
          ...userTc.isInitializeData.limitSetting,
        },
      },
    ]);
    // const itemSetting = ref([]);
    // const dataSource = ref([]);
    const dataSource = computed(() => {
      return entity.value.content;
      // ? itemSetting.value.concat(entity.value.content)
      // : itemSetting.value;
    });
    const getExpression = async (expression) => {
      if (expression) {
        return `${expression}`;
      }
      return "";
    };
    // 查询API
    const bindData = async (expression) => {
      const limitSetting = await getAllChannelLimit();

      if (limitSetting?.dateLimit) {
        // eslint-disable-next-line array-callback-return
        const { year, month, day } = limitSetting.dateLimit;

        itemAllSetting.value[0].limitSetting.YEAR = year;

        itemAllSetting.value[0].limitSetting.MONTH = month;

        itemAllSetting.value[0].limitSetting.DAY = day;
      }
      const exp = "category ne SYSTEM";
      const realExpression = await getExpression(exp);
      entity.value = await findNodePage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        { expression: realExpression }
      );
      entity.value.content.map((item) => {
        item.YEAR = -1;
        item.MONTH = -1;
        item.DAY = -1;
        if (item.setting.limitSetting !== undefined) {
          item.setting.limitSetting.dateUnits.map((child) => {
            if (child.unit === "YEAR") {
              item.YEAR = child.times;
            }
            if (child.unit === "MONTH") {
              item.MONTH = child.times;
            }
            if (child.unit === "DAY") {
              item.DAY = child.times;
            }
            return [];
          });
        }
        return [];
      });
      pagination.value.total = entity.value.totalElements;
      // const globalLimitSetting = await findGlobalLimitSetting();
    };

    const formatter = (value) => {
      return value === "-1" || !value ? "不限制" : value;
    };

    const parser = (value) => {
      return value === "不限制" ? -1 : value;
    };
    const save = async (row) => {
      // const temp = [];
      // eslint-disable-next-line no-restricted-syntax
      // for (const item in row.limitSetting) {
      //   if (row.limitSetting[item] !== -1 || row.limitSetting[item]) {
      //     temp.push({
      //       unit: item,
      //       times: row.limitSetting[item],
      //     });
      //   }
      // }
      // const dateLimit = row.limitSetting;
      const { YEAR = 0, MONTH = 0, DAY = 0 } = row.limitSetting;
      channelEntity.value.dateLimit = { year: YEAR, month: MONTH, day: DAY };
      await saveAllChannelLimit(channelEntity.value);
      row.isEdit = false;
      Message.success("保存成功！");
    };

    const setup = {
      module,
      filter,
      entity,
      bindData,
      dataSource,
      itemAllSetting,
      pagination,
      formatter,
      parser,
      // saveData,
      save,
    };
    provide("main", setup);
    return setup;
  },
};
</script>
