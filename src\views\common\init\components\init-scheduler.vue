<template>
  <a-alert type="warning">
    如您有额外的分库要求，您可选择配置独立存储信息，本产品运行时产生的定时任务数据将为您单独存储。
  </a-alert>
  <a-form ref="formRef" :model="formData">
  </a-form>
</template>

<script setup>
import { ref, computed } from "vue";

const formRef = ref(null);
const formData = ref({});

const commit = async (entity) => {
  if (await formRef.value.validate()) {
    return false;
  }

  return true;
}

defineExpose({
  commit
})
</script>

<style lang="less" scoped>

.arco-alert {
    margin: 10px;
  }

</style>
