/**
 * 任务管理接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function saveTask(info?: any) {
  return info.id?axios.put(`/api/ma-manage/${tenantId}/${buCode}/job`,info):axios.post(`/api/ma-manage/${tenantId}/${buCode}/job`,info);
}

export function saveTask2(info?: any) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/job/save`,info);
}

export function startTask(id: string) {
  return axios.put(`/api/ma-manage/${tenantId}/${buCode}/job/${id}/start`);
}

export function findPage(query?: QueryInfo, params?: Params) {
    return axios.get(`/api/ma-manage/${tenantId}/${buCode}/job`, {
        params: {
            ...params,
            ...query
        },
    });
}
export function findTaskItem(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/job/${id}`);
}

export function deleteItem(id: string) {
    return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/job/${id}`);
}

export function deleteItemBatch(ids: Array<string>) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/job/batch-delete`, ids);
}

export function downloadItem(filePath: string) {
    return `/api/ma-manage/${tenantId}/${buCode}/file/download?filePath=${filePath}`
}


export function findViewPage(jobId?: any) {
    return axios.get(`/api/ma-manage/${tenantId}/${buCode}/audience/compare/preview/${jobId}`);
}
