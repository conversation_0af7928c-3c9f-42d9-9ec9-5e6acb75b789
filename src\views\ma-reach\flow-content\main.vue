<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <!-- <a-button v-permission="['ma_menu.flow-content.create', `ma_menu.flow-content.${menuItem.meta.id}.create`]" type="primary" @click="editItem()">
          <template #icon>
            <icon-plus />
          </template>
          {{t('global.button.create')}}
        </a-button> -->
        <a-button type="primary"
                  @click="editItem()">
          <template #icon>
            <icon-plus />
          </template>
          {{t('global.button.create')}}
        </a-button>
      </template>
      <template #main>
        <a-table :bordered="false"
                 :data="dataSource"
                 :pagination="false">
          <template #columns>
            <a-table-column :title="t('reach.sms.contentName')"
                            data-index="id" />
            <a-table-column :title="t('reach.sms.touchpoint')"
                            data-index="flowNodeId">
              <template #cell="{ record }">
                {{ getItemName(record.flowNodeId) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('reach.sms.communicationName')"
                            data-index="name" />
            <a-table-column :title="t('reach.sms.templateCode')"
                            data-index="flowTemplateId" />
            <a-table-column :title="t('reach.sms.templateName')"
                            data-index="flowTemplateId">
              <template #cell="{ record }">
                {{ getTemplateName(record.flowTemplateId) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('reach.sms.communicationGroup')"
                            data-index="group" />
            <a-table-column :title="t('reach.sms.content')"
                            data-index="content"
                            :ellipsis="true"
                            :tooltip="{ class: 'tooltip-content' }" />
            <a-table-column :title="t('reach.sms.createTime')"
                            data-index="createTime"
                            :sortable="{ sortDirections: ['ascend', 'descend'] }"
                            :width="180">
              <template #cell="{ record }">
                {{ $moment(record.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </a-table-column>
            <a-table-column :title="t('reach.sms.status')"
                            data-index="enabled"
                            :width="60">
              <template #cell="{ record }">
                {{ statusFilter(record.enabled) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('reach.column.action')"
                            :align="'center'"
                            :width="180">
              <!-- <template #cell="{ record }">
                <a-button v-permission="['ma_menu.flow-content.modify', `ma_menu.flow-content.${menuItem.meta.id}.modify`]" type="text" size="small"
                  @click="editItem(record.id)">{{t('global.button.edit')}}</a-button>
                <a-button v-permission="['ma_menu.flow-content.delete', `ma_menu.flow-content.${menuItem.meta.id}.delete`]" type="text" size="small"
                  @click="deleteData(record.id)">{{t('global.button.delete')}}</a-button>
              </template> -->
              <template #cell="{ record }">
                <a-button type="text"
                          size="small"
                          @click="editItem(record.id)">{{t('global.button.edit')}}</a-button>
                <a-button type="text"
                          size="small"
                          @click="deleteData(record.id)">{{t('global.button.delete')}}</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
  </div>
</template>

<script>
import {provide, ref, getCurrentInstance} from 'vue'
import {useRouter} from 'vue-router'
import {Modal} from '@arco-design/web-vue'
import {findContentPage, deleteContent} from '@/api/flow-content'
import {useMenuItemState} from '@/store'
import {findTemplateList} from '@/api/flow'
import {findNodeLists} from '@/api/node'

export default {
  setup() {
    const {
      proxy: {t}
    } = getCurrentInstance()
    const router = useRouter()
    const menuItem = useMenuItemState()
    menuItem.loadFromLocalStorage()
    const module = ref({
      entityIdField: 'id',
      breadcrumb: [
        {
          name: `${menuItem.meta.title || ''} ${t('reach.title.communicationList')}`
        }
      ],
      editPath: '/reach/dynamic/edit',
      createPath: '/reach/dynamic/edit',
      editPermission: ['ma.flow-content.modify']
    })
    // 过滤设置
    const templateList = ref([])
    const filter = ref([
      {
        field: 'flowNodeId',
        // label: "沟通触点",
        label: t('reach.title.communicationTouchpoint'),
        component: 'a-select',
        operate: 'eq',
        dataSource: [],
        // placeholder: "请选择数据源",
        placeholder: t('reach.reminder.touchpoint'),
        value: ''
      },
      {
        field: 'id',
        // label: "沟通编码",
        label: t('reach.title.communicationCode'),
        component: 'a-input',
        operate: 'eq',
        // placeholder: "请输入沟通编码",
        placeholder: t('reach.reminder.communicationCode'),
        comment: true,
        value: ''
      },
      {
        field: 'name',
        // label: "沟通名称",
        label: t('reach.title.communicationName'),
        component: 'a-input',
        operate: 'like',
        // placeholder: "请输入沟通名称",
        placeholder: t('reach.reminder.communicationName'),
        comment: true,
        value: ''
      },
      {
        field: 'flowTemplateId',
        // label: "沟通模板",
        label: t('reach.title.communicationTemplate'),
        component: 'a-input',
        operate: 'eq',
        allowClear: true,
        allowSearch: true,
        // placeholder: "请输入沟通模板编码",
        placeholder: t('reach.reminder.communicationTemplate'),
        value: ''
      },
      {
        field: 'group',
        // label: "沟通分组",
        label: t('reach.title.communicationGroup'),
        component: 'a-input',
        operate: 'like',
        allowClear: true,
        allowSearch: true,
        // placeholder: "请输入沟通分组",
        placeholder: t('reach.reminder.communicationGroup'),
        value: ''
      },
      {
        field: 'enabled',
        // label: "状态",
        label: t('reach.title.status'),
        component: 'a-select',
        operate: 'eq',
        dataSource: [
          {value: 'true', label: t('global.button.enable')},
          {value: 'false', label: t('global.button.disable')}
        ],
        // placeholder: "请选择状态",
        placeholder: t('reach.reminder.status'),
        value: ''
      }
    ])

    const editCategoryRef = ref(null)

    const dataSource = ref([])
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true
    })

    // 获取模板名称
    const getTemplateName = id => {
      const item = templateList.value.find(x => {
        return x.id === id
      })
      return item?.name || '--'
    }

    const editItem = async id => {
      const query = {id}
      router.push({path: module.value.editPath, query})
    }

    const getItemName = value => {
      const item = filter.value[0].dataSource.find(x => {
        return x.value === value
      })
      return item?.label || ''
    }

    // 删除
    const deleteData = async id => {
      Modal.confirm({
        // title: "删除沟通模板",
        title: t('reach.title.deleteReachTemplate'),
        // content: "删除之后数据不可恢复，请确认是否删除?",
        content: t('reach.title.deleteReachTemplate'),
        onOk: async () => {
          await deleteContent(id)
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1
          }
          await bindData()
        }
      })
    }

    const statusFilter = v => {
      switch (v) {
        case true:
          // return '启用'
          return t('global.button.enable')
        case false:
          // return '禁用'
          return t('global.button.disable')
        default:
          // return '禁用'
          return t('global.button.disable')
      }
    }

    const searchTemplateList = () => {
      findTemplateList({fields: 'name'}).then(res => {
        templateList.value = res
        filter.value[3].dataSource = []
        res.map(item => {
          filter.value[3].dataSource.push({value: item.id, label: item.name})
        })
      })
    }

    const bindData = async expression => {
      const exp = expression ? `${expression} AND category eq ${menuItem.meta.id}` : `category eq ${menuItem.meta.id}`
      const pageData = await findContentPage(
        {
          fields: 'name,content,category,group,flowNodeId,flowTemplateId,enabled,createdTime,updateTime',
          ...pagination.value,
          page: pagination.value.page - 1
        },
        {
          expression: exp
        }
      )
      dataSource.value = pageData.content
      pagination.value.total = pageData.totalElements

      const params = {}
      if (menuItem.meta.id) {
        params.expression = `flowCategory eq ${menuItem.meta.id}`
      }

      findNodeLists(params).then(res => {
        filter.value[0].dataSource = []
        res.map(item => {
          filter.value[0].dataSource.push({value: item.id, label: item.name})
        })
      })
      await searchTemplateList()
    }

    const setup = {
      t,
      filter,
      module,
      editCategoryRef,
      dataSource,
      pagination,
      menuItem,
      getItemName,
      getTemplateName,
      statusFilter,
      bindData,
      editItem,
      deleteData,
      searchTemplateList
    }
    provide('main', setup)
    return setup
  }
}
</script>

<style lang="less" scoped></style>
