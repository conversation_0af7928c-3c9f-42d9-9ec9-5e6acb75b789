import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import PointsNode from "./node.vue";
import PointsPannel from "./pannel.vue";

const nodeData = {
  type: "points",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<PointsNode />`,
      components: {
        PointsNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("PointsNode", nodeData.node, true);
};

const Points = {
  type: "points",
  name: "积分",
  shape: "PointsNode",
  iconClass: "icon-coin",
  color: "#ffffff",
  themebg: "#4594f3",
  registerNode: registerNode,
  pannel: PointsPannel,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Points;
