/** flow模板 */
<template>
  <div style="width: 100%">
    <module ref="mainModule" main>
      <template #filter></template>
      <template #search>
      </template>
      <template #action>
        <a-button v-permission="['ma_menu.flow-template.create']" type="primary" @click="editItem()">
          <template #icon>
            <icon-plus />
          </template>
          <!-- 新建 -->
          {{t('global.button.create')}}
        </a-button>
        <a-button v-permission="['ma_menu.flow-template.sync']" type="primary" style="margin-left: 10px" :loading="syncing" @click="sync()">
          <template #icon>
            <icon-sync />
          </template>
          <!-- 同步 -->
          {{t('global.button.sync')}}
        </a-button>
      </template>
      <template #main>
        <a-table :bordered="false" :data="dataSource" :pagination="false">
          <template #columns>
            <a-table-column :title="t('reach.column.templateCode')" data-index="id" :width="80"/>
            <a-table-column :title="t('reach.column.templateName')" data-index="name" :width="100"/>
            <a-table-column :title="t('reach.column.touchpoint')" data-index="nodeName" :width="80"/>
            <a-table-column :title="t('reach.column.templateContent')" data-index="content" :ellipsis="true"
              :tooltip="{ class: 'tooltip-content' }"  :width="140"/>
              <a-table-column :title="t('reach.column.category')" data-index="group"  :width="60"/>
            <a-table-column :title="t('reach.column.status')" data-index="status" :width="40">
              <template #cell="{ record }">
                {{ statusFilter(record.status) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('reach.column.signature')" data-index="sign" :width="60" />
            <a-table-column :title="t('reach.column.sync')" data-index="enabled" :width="40">
              <template #cell="{ record }">
                <!-- {{ !record.own ? "同步" : "自建" }} -->
                {{ !record.own ? t('reach.status.sync') : t('reach.status.selfBuilt') }}
              </template>
            </a-table-column>
            <a-table-column :title="t('reach.column.updateTime')" data-index="updateTime" :sortable="{ sortDirections: ['ascend', 'descend'] }" :width="80">
              <template #cell="{ record }">
                {{ $moment(record.updateTime).format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </a-table-column>
            <a-table-column :title="t('reach.column.action')" :align="'center'" :width="80">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.flow-template.modify']" type="text" size="small"
                  @click="editItem(record.id)">{{t('global.button.edit')}}</a-button>
                <a-button v-permission="['ma_menu.flow-template.delete']" type="text" size="small"
                  @click="deleteData(record.id)">{{t('global.button.delete')}}</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
  </div>
</template>

<script>
import { provide, ref, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import { Modal, Message } from "@arco-design/web-vue";
import { filters } from "@/utils/filter";
import { capabilityStatus } from "@/constant/capability";
import {
  findTemplateView,
  deleteTemplate,
  syncAllTemplate,
  syncOneTemplate
} from "@/api/flow";
import { findNodeList } from "@/api/node";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          // name: "触达模板"
          name: t("reach.title.template"),
        }
      ],
      editPath: "/reach/flow-template/edit",
      createPath: "/reach/flow-template/edit"
    });
    const mainModule = ref();
    const nodeConfigs = ref([]);
    const dataSource = ref([]);
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true
    });

    const syncing = ref(false);

    // 过滤设置
    const filter = ref([
      {
        field: "nodeConfigId",
        // label: "沟通触点",
        label: t("reach.title.communicationTouchpoint"),
        component: "a-select",
        operate: "eq",
        dataSource: [],
        // placeholder: "请选择",
        placeholder: t("reach.reminder.touchpoint"),
        value: ""
      },
      {
        field: "id",
        // label: "模板编码",
        label: t("reach.title.templateCode"),
        component: "a-input",
        operate: "eq",
        // placeholder: "请输入模板编码",
        placeholder: t("reach.reminder.templateCode"),
        comment: true,
        value: ""
      },
      {
        field: "name",
        // label: "模板名称",
        label: t("reach.title.templateName"),
        component: "a-input",
        operate: "like",
        // placeholder: "请输入模板名称",
        placeholder: t("reach.reminder.templateName"),
        comment: true,
        value: ""
      },
      {
        field: "status",
        // label: "状态",
        label: t("reach.title.status"),
        component: "a-select",
        operate: "eq",
        allowClear: true,
        allowSearch: true,
        dataSource: [
          { value: 'ENABLED', label: t("global.button.enable") },
          { value: 'DISABLED', label: t("global.button.disable") }
        ],
        // placeholder: "请输选择状态",
        placeholder: t("reach.reminder.status"),
        comment: true,
        value: ""
      }
    ]);
    // 获取列表
    const bindData = async (expression) => {
      const pageData = await findTemplateView(
        {
          ...pagination.value,
          page: pagination.value.page - 1
        },
        {
          expression
        }
      );
      dataSource.value = pageData.content;
      pagination.value.total = pageData.totalElements;
    };

    const editItem = async (id) => {
      const query = { id };
      router.push({ path: module.value.editPath, query });
    };

    // 删除
    const deleteData = async (id) => {
      Modal.confirm({
        // title: "删除流程模板",
        title: t('reach.popup.deleteTitle'),
        // content: "删除之后数据不可恢复，请确认是否删除?",
        content: t('global.tips.warning.delete'),
        onOk: async () => {
          await deleteTemplate(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1;
          }
          await bindData();
        }
      });
    };
    const sync = async () => {
      const { nodeConfigId } = mainModule.value.modelRef.formModel;
      console.log(nodeConfigId);
      // eslint-disable-next-line no-unused-expressions
      syncing.value = true;
      nodeConfigId
        ? await syncOneTemplate(nodeConfigId)
        : await syncAllTemplate();
        await bindData();
        // Message.success("同步完成");
        Message.success(t("global.tips.success.sync"));
      syncing.value = false;
    };
    const getNodeConfigList = async () => {
      const res = await findNodeList({expression: "category eq FLOW_TEMPLATE", fields: "name,groupId"});
      filter.value[0].dataSource = [];
      res.map((item) => {
        if (item.category !== "SYSTEM") {
          filter.value[0].dataSource.push({ value: item.id, label: item.name });
        }
        return [];
      });
    };
    getNodeConfigList();

    const statusFilter = (v) => {
      return filters(capabilityStatus, v);
    };

    const typeName = (name) => {
      const typeList = {
        // sms: "短信",
        // mms: "彩信",
        // wechat_temp: "微信模板",
        // wechat_notify: "微信通知",
        // app: "app"
        sms: t('reach.type.sms'),
        mms: t('reach.type.mms'),
        wechat_temp: t('reach.type.wechatTemplate'),
        wechat_notify: t('reach.type.wechatNotification'),
        app: t('reach.type.app'),
      };
      return typeList[name] || "--";
    };

    const setup = {
      t,
      mainModule,
      filter,
      module,
      nodeConfigs,
      dataSource,
      pagination,
      syncing,
      statusFilter,
      bindData,
      editItem,
      typeName,
      deleteData,
      sync
    };
    provide("main", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped></style>
