<template>
  <module edit>
    <template v-slot:main>
      <a-form
        ref="formRef"
        layout="vertical"
        class="general-form"
        :model="entity"
      >
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="分析指标名称" field="name">
                  <a-input
                    placeholder="请输入分析指标名称"
                    v-model="entity.name"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="12">
                <a-form-item label="指标类型" field="name">
                  <a-select v-model="entity.type" placeholder="请选择指标类型">
                    <a-option
                      v-for="item in metricTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="分析模型类型" field="modelType">
                  <a-select
                    v-model="entity.modelType"
                    placeholder="请选择分析模型类型"
                  >
                    <a-option
                      v-for="item in modelTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="分析模型" field="modelId">
                  <a-select
                    v-model="entity.modelId"
                    placeholder="请选择分析模型"
                  >
                    <a-option
                      v-for="item in models"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="图表类型" field="chartType">
                  <a-select
                    v-model="entity.chartType"
                    placeholder="请选择图表类型"
                  >
                    <a-option
                      v-for="item in chartTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="分析图表" field="chartId">
                  <a-select
                    v-model="entity.chartId"
                    placeholder="请选择分析图表"
                  >
                    <a-option
                      v-for="item in charts"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-item label="摘要" field="summary">
                  <a-textarea
                    placeholder="请输入摘要内容"
                    v-model="entity.summary"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-space>
      </a-form>
    </template>
  </module>
</template>

<script>
import { ref, provide } from "vue";
import { Notification } from "@arco-design/web-vue";
import { useRoute } from "vue-router";
import { metricTypes, modelTypes, chartTypes } from "@/constant/metric";
import { createMetric, modifyMetric, getMetric } from "@/api/analysis";
export default {
  components: {},
  setup() {
    const route = useRoute();
    const queryId = route.query.id;
    const module = ref({
      entityIdField: "id",
      entityName: `编辑分析指标`,
      mainPath: "/system/metric",
    });
    const entity = ref({});
    const charts = ref([]);
    const models = ref([]);

    const bindData = async () => {
      if (!!queryId) {
        entity.value = await getMetric(queryId);
      }
    };

    const save = async () => {
      if (entity.value.id) {
        await modifyMetric(entity.value);
      } else {
        await createMetric(entity.value);
      }
      Notification.info({
        title: "保存分析指标成功",
      });
    };

    // defineExpose({});
    const setup = {
      module,
      entity,
      metricTypes,
      modelTypes,
      chartTypes,
      charts,
      models,
      bindData,
      save,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>
<style  lang="less" scoped>
</style>
