<template>
  <a-modal v-model:visible="modelVisible" title="查看模拟数据" modal-class="simulate-data-view-model" width="50%" ok-text="关闭"
    :hide-cancel="true" @ok="handleOk">
    <a-table ref="table" :bordered="false" :data="dataList" :pagination="false">
      <template #columns>
        <a-table-column title="处理时间" data-index="timestamp" :ellipsis="true" :tooltip="true" :width="80">
          <template #cell="{ record }">
            {{ $moment(record.timestamp).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </a-table-column>
        <!-- <a-table-column
          title="id"
          data-index="id"
          :ellipsis="true"
          :tooltip="true"
          :width="80"
        />
        <a-table-column
          title="追踪Id"
          data-index="trackingId"
          :ellipsis="true"
          :tooltip="true"
          :width="80"
        /> -->
        <a-table-column title="身份标识数据" data-index="payloadId" :ellipsis="true" :tooltip="true" :width="80" />
        <!-- <a-table-column
          title="payload"
          data-index="payload"
          :ellipsis="true"
          :tooltip="true"
          :width="180"
        /> -->
      </template>
    </a-table>
    <a-pagination :total="pagination.total" size="small" :current="pagination.page" :page-size="pagination.size"
      show-total show-jumper show-page-size @change="changePage" @page-size-change="changeSizePage" />
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { Modal } from "@arco-design/web-vue";
import { getTaskFactPage } from "@/api/campaign";

const modelVisible = ref(false);
let instanceId;
let taskId;

const dataList = ref([]);
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
  showPageSize: true,
});

// 切换页数
const changePage = (e) => {
  pagination.value.page = e;
  handleSearch();
};
// 切换条数
const changeSizePage = (e) => {
  pagination.value.page = 1;
  pagination.value.size = e;
  handleSearch();
};

const handleSearch = async () => {
  const page = await getTaskFactPage({
    ...pagination.value,
    page: pagination.value.page - 1,
    expression: `instanceId eq ${instanceId} AND taskId eq ${taskId} AND type eq DATA`,
  });
  dataList.value = page.content;

  pagination.value.total = page.totalElements
};

const show = async (params) => {
  modelVisible.value = true;
  instanceId = params.instanceId;
  taskId = params.taskId;
  await handleSearch();
};
const handleOk = () => {
  modelVisible.value = false;
};

defineExpose({ show });
</script>

<style  lang="less" scoped>
.simulate-data-view-model {}
</style>
