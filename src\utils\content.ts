export function getDynamicFields(content: any) {
  let result: any = [];
  if (content) {
    content.replace(/\${.*?}/g, function (str: any) {
      console.log(str);
      const key = str.match(/\${(\S*)}/)[1];
      result.push({ source: key });
    });

    const pattern = /{{(.*?)\.DATA}}/g;
    const matches = content.match(pattern);
    if (matches) {
      const parameters = matches.map(
        (match: any) => match.match(/{{(.*?)\.DATA}}/)[1]
      );
      parameters.forEach((key: any) => {
        result.push({ source: key });
      });
    }
  }
  return result;
}
