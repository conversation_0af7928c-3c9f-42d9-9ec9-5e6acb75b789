import { defineStore } from "pinia";
import { getBussinessUnit, getMarketingCenter } from "@/api/user";
import { BussinessUnit } from "@/types/global";
import { BussinessUnitState } from "./types";

const useBussinessUnitStore = defineStore("bussinessUnit", {
  state: (): BussinessUnitState => ({
    bussinessUnit: [],
    isInitializeData: {},
    initializeStep: 5, // 0 还未初始化 1 初始化 类推
    currentBussinessUnit: {
      id: "",
      code: "",
      logo: "",
      name: "",
      tenantId: "",
      invalid: false,
      createdTime: "",
      description: "",
    },
    marketingCenter: {},
  }),
  actions: {
    setBussinessUnit(partial: any) {
      this.$patch({
        bussinessUnit: partial,
      });
    },

    setCurrentBussinessUnit(partial: any) {
      this.$patch({
        currentBussinessUnit: partial,
      });
    },
    setMarketingCenter(partial: any) {
      this.$patch({
        marketingCenter: partial,
      });
    },
    // setIsInitializeData(partial: any) {
    //   this.$patch({
    //     isInitializeData: partial,
    //   });
    // },
    // setInitializeStep(index: any) {
    //   this.$patch({
    //     initializeStep: index,
    //   });
    // },

    async getBussinessUnit() {
      const res: any = (await getBussinessUnit()) || [];

      this.setBussinessUnit(res);
      const currentBuId = localStorage.getItem("current-bussiness-unit");
      let currentBu = null;
      if (currentBuId) {
        currentBu = res.find((i: BussinessUnit) => i.id === currentBuId);
        if (currentBu) {
          this.setCurrentBussinessUnit(currentBu);
        } else if (res[0]) {
          localStorage.setItem("current-bussiness-unit", res[0].id);
          this.setCurrentBussinessUnit(res[0]);
          currentBu = res[0];
        }
      } else if (res[0]) {
        localStorage.setItem("current-bussiness-unit", res[0].id);
        this.setCurrentBussinessUnit(res[0]);
        currentBu = res[0];
      }
      await this.getMarketingCenter(currentBu);
    },

    async getMarketingCenter(currentBu: any) {
      // 获取单元数据 判断是否初始化
      await getMarketingCenter(currentBu.tenantId, currentBu.code).then(
        (res1: any) => {
          if (res1 && res1 instanceof Object) {
            this.setMarketingCenter(res1);
          } else {
            this.setMarketingCenter({ status: "NONE" });
          }
        }
      ).catch((err) => {
        console.log("error")
        this.setMarketingCenter({ status: "ERROR" });
      });
    },
  },
});

export default useBussinessUnitStore;
