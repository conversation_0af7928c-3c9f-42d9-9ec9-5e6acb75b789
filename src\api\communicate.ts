import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";
const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findCommunicatePage(query?: QueryInfo, params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/communicate`,
    {
      params: {
        ...params,
        ...query,
      },
    }
  );
}

export function findCommunicateList(params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/communicate/list`,
    {
      params: params,
    }
  );
}

export function findCommunicateItem(id: String) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/communicate/${id}`
  );
}

export function saveCommunicateInfo(info?: any) {
  return info.id
    ? axios.put(
        `/api/ma-manage/${tenantId}/${buCode}/communicate`,
        info
      )
    : axios.post(
        `/api/ma-manage/${tenantId}/${buCode}/communicate`,
        info
      );
}

export function deleteCommunicateItem(id: String) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/communicate/${id}`
  );
}

export function findChannelList(params: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/marketing_channel/list`,
    {
      params: params,
    }
  );
}

export function simulateCommunicateSend(info?: any) {
  info.tenantId = tenantId;
  info.buCode = buCode;
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/communicate/simulate`,
    info
  );
}

export function getCommunicatePreview(info?: any) {
  info.tenantId = tenantId;
  info.buCode = buCode;
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/communicate/preview`,
    {
      ...info,
      tenantId: tenantId,
      buCode: buCode,
    }
  );
}

export function findThirdCapabilityList(params?: Params){
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/third_capability/list`,
    {
      params: params,
    }
  );
}
