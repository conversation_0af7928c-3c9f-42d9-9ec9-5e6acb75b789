export default {
  "menu.report": "Report",
  'menu.report.report': 'Report',

  report:{
    communicationReport: "Communication Report",
    cumulativeReachCount: "Cumulative Reach Count",
    cumulativeReachPeople: "Cumulative Reach People",
    reachChannel: "Reach Channel",
    reachCount: "Reach Count",
    todayReachCount: "Today Reach Count",
    todayReachSuccessRate: "Today Reach Success Rate",
    cumulativeReachSuccessCount: "Cumulative Reach Success Count",
    cumulativeReachSuccessRate: "Cumulative Reach Success Rate",
    channel: "Channel",
    operationTime: "Operation Time",
    campaignId: "Campaign ID",
    campaignName: "Campaign Name",
    campaignCategory: "Campaign Category",
    canvasId: "Canvas ID",
    canvasName: "Canvas Name",
    executionStatus: "Execution Status",
    customerId: "Customer ID",
    customerIdentityField: "Customer Identity Field",
    reachField: "Reach Field",
    reachFieldValue: "Reach Field Value",
    reachContent: "Reach Content",
    reachData: "Reach Data",
    reachStatus: "Reach Status",
    processInstanceId: "Process Instance ID",
    canvasNodeId: "Canvas Node ID",
    reachTemplateId: "Reach Template ID",
    reachTemplateName: "Reach Template Name",
    reachContentId: "Reach Content ID",
    reachContentName: "Reach Content Name",
    acceptanceStatus: "Acceptance Status",
    completionStatus: "Completion Status",
    errorStatus: "Error Status",
    nodeType: "Node Type",
    isTemplateFrequencyLimit: "Template Frequency Limit",
    isChannelFrequencyLimit: "Channel Frequency Limit",
    isGlobalFrequencyLimit: "Global Frequency Limit",
    message: "Message",
    reminder: {
      channel: "Select a channel",
      operationTime: "Select operation time",
      campaignId: "Enter campaign ID",
      campaignName: "Select campaign name",
      campaignCategory: "Select campaign category",
      canvasId: "Enter canvas ID",
      canvasName: "Select canvas name",
      executionStatus: "Select execution status",
      customerId: "Enter customer ID",
      customerIdentityField: "Select customer identity field",
      reachField: "Select reach field",
      reachFieldValue: "Enter reach field value",
      reachContent: "Enter reach content",
      reachData: "Select reach data",
      reachStatus: "Select reach status",
      processInstanceId: "Process instance ID",
      canvasNodeId: "Enter canvas node ID",
      reachTemplateId: "Enter reach template ID",
      reachTemplateName: "Enter reach template name",
      reachContentId: "Enter reach content ID",
      reachContentName: "Enter reach content name",
      acceptanceStatus: "View acceptance status",
      completionStatus: "View completion status",
      errorStatus: "View error status",
      nodeType: "Select node type",
      isTemplateFrequencyLimit: "Enter template frequency limit",
      isChannelFrequencyLimit: "Enter channel frequency limit",
      isGlobalFrequencyLimit: "Enter global frequency limit",
    }
},
};
