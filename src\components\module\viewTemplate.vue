<template>
  <div class="container">
    <div class="actions">
      <slot name="title">
        <template v-if="module.breadcrumb">
          <a-breadcrumb style="margin-left: 15px">
            <template v-for="item in module.breadcrumb" :key="item.name">
              <a-breadcrumb-item v-if="item.path">
                <router-link :to="{ path: item.path, query: item.query || {} }">
                  <span style="font-size: 16px; font-weight: normal">{{ item.name }}</span>
                </router-link>
              </a-breadcrumb-item>
              <a-breadcrumb-item v-else>
                <span style="font-size: 16px; font-weight: bold">{{ item.name }}</span>
              </a-breadcrumb-item>
            </template>
          </a-breadcrumb>
        </template>
        <template v-else>
          <span class="page-title">{{ module?.entityName }}</span>
        </template>
      </slot>
      <a-space>
        <a-button v-if="quit" type="outline" @click="quit"> {{ t('global.button.back') }} </a-button>
        <slot name="action">
        </slot>
      </a-space>
    </div>
    <div class="main">
      <slot name="main"> </slot>
    </div>
  </div>
</template>

<script>
import {
  ref,
  inject,
  toRefs,
  reactive,
  computed,
  onMounted,
  onUpdated,
  onUnmounted,
  onBeforeMount,
  onBeforeUpdate,
  onBeforeUnmount,
  getCurrentInstance,
  watch
} from "vue";

import { useRouter, useRoute } from "vue-router";

export default {
  name: "viewTemplate",
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const route = useRoute();
    const router = useRouter();

    const edit = inject("view");

    const module = edit?.module?.value || {
      mainPath: null,
      breadcrumb: null,
      entityName: null,
      entityIdField: "id",
      ...edit.module.value
    };

    // 面包屑导航处理
    if (!module.breadcrumb) {
      module.breadcrumb = JSON.parse(localStorage.getItem('breadcrumb'))
      if (module.breadcrumb) {
        module.breadcrumb.push({
          name: edit.module.value.entityName
        })
      }
      watch(module, (count, prevCount) => {
        if (module.breadcrumb) {
          module.breadcrumb[module.breadcrumb.length - 1].name = count.entityName
        }
      })
    }


    const formData = {};
    const quit = edit.quit
      ? edit.quit
      : () => {
        router.push({ path: module.mainPath, query: module.mainQuery });
      };
    const params = {};
    const paramsKey = route.query[module.entityIdField];
    params[module.entityIdField] = paramsKey;

    if (edit.bindData) {
      onMounted(() => {
        edit.bindData();
      });
    }
    onBeforeUnmount(() => {
      localStorage.removeItem('breadcrumb')
    })
    return {
      t,
      quit,
      module,
      formData,
    };
  },
};
</script>

<style scoped lang="less">
.page-title {
  padding: 14px 20px;
  display: flex;
  align-items: center;
}
</style>
