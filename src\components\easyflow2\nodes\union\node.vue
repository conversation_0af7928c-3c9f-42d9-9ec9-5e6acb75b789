<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    const item = nodeApi.find(item => { return item.type === 'union' })
    const setup = {
      title: item.name || "并集",
      summary: item.name || "并集节点",
      iconClass: item.icon || "icon-merge",
      nodeClass: "easyflow-node-end",
      headerColor: item.themeColor|| "#fba980",
      headerBgColor: item.themeColor || "#fba980",
      background: item.background || "#fff9ef",
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
