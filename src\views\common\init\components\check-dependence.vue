<template>
  <div>
    <a-alert v-if="result.status == 'SUCCESS'" type="info">
      您已完成所有前置系统初始化。
       <!-- {{  t(init.reminder.envCheck) }} -->
    </a-alert>
    <div v-if="result.status != 'SUCCESS'">
      <a-alert v-for="item in result.causes" :key="item" type="warning">
        {{ item }}
      </a-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { checkDependencies } from "@/api/marketing_center";

const {
      proxy: { t }
    } = getCurrentInstance()

const props = defineProps({

});

const commit = (entity) => {
  console.log('checkDependencies commit')
  return true;
}

const result = ref({});

onMounted(async () => {
  result.value = await checkDependencies();
});

defineExpose({
  commit
})
</script>


<style lang="less" scoped>

.arco-alert {
    margin: 10px;
  }

</style>
