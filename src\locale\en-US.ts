import localeLogin from "@/views/common/login/locale/en-US";
import localeMessageBox from "@/components/message-box/locale/en-US";

import localeCalendar from "@/views/ma-calendar/calendar/locale/en-US";
import localeWorkplace from "@/views/ma-dashboard/locale/en-US";
import localeAudienceAudience from "@/views/ma-audience/locale/en-US";

import localeCampaignCampaign from "@/views/ma-campaign/locale/en-US";
// import localeCommunicate from "@/views/ma-communicate/locale/en-US";

// import localeSystem from "@/views/ma-system/locale/en-US";
import calendar from "@/views/ma-calendar/calendar/locale/en-US";
import audience from "@/views/ma-audience/locale/en-US";
import campaign from "@/views/ma-campaign/locale/en-US";
import system from "@/views/ma-system/locale/en-US";
import analysis from "@/views/ma-analysis/locale/en-US";
import reach from "@/views/ma-reach/locale/en-US";
import abtest from "@/views/ma-abtest/locale/en-US";
import budget from "@/views/ma-budget/locale/en-US";
import report from "@/views/ma-report/locale/en-US"
import dashboard from "@/views/ma-dashboard/locale/en-US";
import init from "@/views/common/locale/en-US";
import task from "@/views/ma-job/locale/en-US"
import material from "@/views/ma-material/locale/en-US"

import global from "./en-US/global";

export default {
  "menu.visualization": "Data Visualization",
  "menu.user": "User Center",
  "navbar.docs": "Docs",
  "navbar.action.locale": "Switch to English",

  ...global,
  ...localeMessageBox,
  ...localeLogin,
  ...localeWorkplace,
  ...localeAudienceAudience,
  ...localeCalendar,
  ...localeCampaignCampaign,
  ...dashboard,
  ...audience,
  ...calendar,
  ...campaign,
  ...system,
  ...analysis,
  ...reach,
  ...abtest,
  ...budget,
  ...report,
  ...task,
  ...material,
  ...init,
};
