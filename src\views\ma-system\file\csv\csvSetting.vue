<template>
  <module edit>
    <template v-slot:main>
      <a-form ref="formRef" layout="vertical" :model="entity">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <template #title> {{ module.entityName }}</template>
            <a-row :gutter="80">
              <a-col :span="12">
                <a-form-item label="第一行是否为header" field="entity.withFirstRecordAsHeader">
                  <a-switch type="round" v-model="entity.withFirstRecordAsHeader">
                    <template #checked>
                      是
                    </template>
                    <template #unchecked>
                      否
                    </template>
                  </a-switch>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="分隔符" field="entity.delimiter">
                  <a-input placeholder="请输入分隔符" v-model="entity.delimiter">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="忽略空行" field="entity.ignoreEmptyLines">
                  <a-switch type="round" v-model="entity.ignoreEmptyLines">
                    <template #checked>
                      是
                    </template>
                    <template #unchecked>
                      否
                    </template>
                  </a-switch>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="字段去掉前后空格" field="entity.trim">
                  <a-switch type="round" v-model="entity.trim">
                    <template #checked>
                      是
                    </template>
                    <template #unchecked>
                      否
                    </template>
                  </a-switch>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="代表null的字符串" field="entity.nullString">
                  <a-input placeholder="请输入代表null的字符串" v-model="entity.nullString">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="忽略空字段" field="entity.ignoreNull">
                  <a-switch type="round" v-model="entity.ignoreNull">
                    <template #checked>
                      是
                    </template>
                    <template #unchecked>
                      否
                    </template>
                  </a-switch>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-space>
      </a-form>
    </template>
    <template v-slot:action></template>
  </module>
</template>

<script>
import {ref, provide} from "vue";
import {useRouter, useRoute} from "vue-router";
import {saveInfo} from "@/api/channel";

export default {
  setup() {
    const route = useRoute();
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: "系统设置",
          path: "/system/setting",
        },
        {
          name: "csv文件设置"
        }
      ]
    });

    const queryValue = route.query.id;
    const isEdit = !!queryValue;


    const formRef = ref(null)

    const entity = ref({
      withFirstRecordAsHeader: true,
      delimiter: null,
      ignoreEmptyLines: true,
      trim:true,
      nullString:null,
      ignoreNull:true
    });


    const bindData = async () => {
      if (isEdit) {
        // entity.value = await findItem(queryValue)
      }
    };
    const quit = async () => {
      await router.push({path: module.value.mainPath});
    }
    const formData = ref({});
    const save = async () => {
      return
      await saveInfo(entity.value);
      await quit()
    };
    const setup = {
      formRef,
      module,
      entity,
      formData,
      bindData,
      save,
      quit,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>
