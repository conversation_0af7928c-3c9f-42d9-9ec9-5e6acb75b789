export default {
  "menu.analysis": "报表分析",
  'menu.visualization.dataAnalysis': '分析页',
  'dataAnalysis.title.publicOpinion': '舆情分析',
  'dataAnalysis.card.title.allVisitors': '访问总人次',
  'dataAnalysis.card.title.contentPublished': '内容发布量',
  'dataAnalysis.card.title.totalComment': '评论总量',
  'dataAnalysis.card.title.totalShare': '分享总量',
  'dataAnalysis.card.yesterday': '较昨日',
  'dataAnalysis.contentPublishRatio': '内容发布比例',
  'dataAnalysis.popularAuthor': '热门作者榜单',
  'dataAnalysis.popularAuthor.column.ranking': '排名',
  'dataAnalysis.popularAuthor.column.author': '作者',
  'dataAnalysis.popularAuthor.column.content': '内容量',
  'dataAnalysis.popularAuthor.column.click': '点击量',
  'dataAnalysis.contentPeriodAnalysis': '内容时段分析',

  analysis:{
    statementAnalysis:"报表分析",
    systemReport: "系统报表",
    throughputAnalysisReport: "吞吐量分析报告",
    timeUnit: "时间单位",
    days: "天",
    hours: "小时",
    timeRange: "时间范围",
    reachThroughput: "触达吞吐量",
    reachChannel: "触达渠道",
    reachStatus: "触达状态",
    reachTemplate: "触达模板",
    activityReachCount: "活动触达次数"
},

};
