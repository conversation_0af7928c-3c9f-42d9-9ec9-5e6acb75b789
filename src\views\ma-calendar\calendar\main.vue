<template>
  <module main>
    <template #main>
      <vue-cal
        ref="calRef"
        style="height: calc(100vh - 200px)"
        :selected-date="currentData"
        :start-date="startDate"
        :time="false"
        active-view="month"
        :disable-views="['day']"
        locale="zh-cn"
        :events-on-month-view="true"
        :events="events"
        cell-contextmenu
        @ready="onCalReady($event)"
        @event-focus="onEventClick"
      />

      <!--  弹窗编辑  -->
      <a-modal v-model:visible="isShowModel" @cancel="onUnfocusEvent">
        <template #title> 画布内容 </template>
        <a-form :model="formData">
          <a-form-item field="name" label="画布名称">
            <a-input v-model="formData.name" placeholder="这是一个画布名称" />
          </a-form-item>
          <a-form-item field="name" label="画布分组">
            <a-select v-model="formData.groupId">
              <a-option
                v-for="item in groupList"
                :key="item.id"
                :value="item.id"
                >{{ item.name }}</a-option
              >
            </a-select>
          </a-form-item>
          <a-form-item field="sTime" label="开始时间">
            <a-date-picker
              v-model="formData.startTime"
              style="width: 100%"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
            />
          </a-form-item>
          <a-form-item field="nTime" label="结束时间">
            <a-date-picker
              v-model="formData.endTime"
              style="width: 100%"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
            />
          </a-form-item>
        </a-form>
        <template #footer>
          <a-button
            v-permission="['ma_menu.calendar.view']"
            @click="handDetails"
            >详情</a-button
          >
          <a-button
            v-permission="['ma_menu.calendar.report']"
            type="primary"
            @click="handleOk"
            >报表</a-button
          >
        </template>
      </a-modal>
    </template>
  </module>
</template>

<script>
import { provide, ref, onMounted, getCurrentInstance } from "vue";
import { findCampaignList } from "@/api/campaign";
import { findGroupList } from "@/api/group";
import { useRouter, useRoute } from "vue-router";
import VueCal from "vue-cal";
import "vue-cal/dist/vuecal.css";
import "vue-cal/dist/i18n/zh-cn.js";
// import "vue-cal/dist/i18n/zh-cn.es.js";

export default {
  components: {
    VueCal,
  },
  setup() {
    const {
      proxy: { t },
    } = getCurrentInstance();
    const router = useRouter();
    const module = ref({
      entityName: "营销日历",
      showBtn: false,
    });

    const currentData = ref(Date());
    const events = ref([]);
    const calRef = ref(null);
    const startDate = ref(null);
    // 设置日历内容
    const campaignStatus = (x) => {
      const y = [
        { value: "DRAFT", label: "1" },
        { value: "COMMITTED", label: "4" },
        { value: "APPROVED", label: "3" },
        { value: "REJECTED", label: "5" },
        { value: "STOPPED", label: "2" },
        { value: "RUNNING", label: "6" },
        { value: "PAUSED", label: "7" },
        { value: "FINISHED", label: "6" },
        { value: "ERROR", label: "7" },
      ].find((item) => {
        return item.value === x.status;
      });

      return y.label;
    };
    const setCalendarItem = (item, index) => {
      const startTime = new Date(item.startTime);
      const endTime = new Date(item.endTime);
      const event = {
        start: startTime,
        end: endTime,
        title: item.name,
        groupId: item.groupId,
        id: item.id,
        // content: `${item.name}`,
        class: `vuecal--color-${campaignStatus(item)}`,
      };
      events.value.push(event);
    };

    // 编辑日期内容
    const formData = ref({});
    const isShowModel = ref(false);
    const onEventClick = (event) => {
      formData.value = {
        id: event.id,
        groupId: event.groupId,
        name: event.title,
        startTime: event.start,
        endTime: event.end,
        groupId: event.groupId,
      };
      // 显示弹窗
      isShowModel.value = true;
    };
    const handleClick = () => {
      isShowModel.value = true;
    };
    const handleOk = () => {
      isShowModel.value = false;
      localStorage.setItem(
        "workplaceItem",
        JSON.stringify({
          ...formData.value,
          module: {
            mainPath: "/calendar/calendar",
            name: "营销日历",
          },
        })
      );

      const query = { id: formData.value.id };
      router.push({ path: "/campaign/report", query });
    };

    const handDetails = () => {
      const query = { id: formData.value.id, groupId: formData.value.groupId };
      router.push({ path: "/campaign/campaign/view-flow", query });
    };

    const getTimeAddDays = (time, days) => {
      return new Date(time.getTime() + days * 24 * 60 * 60 * 1000);
    };

    const getCampaignList = async (expression = null) => {
      const data = await findCampaignList({
        fields:
          "name,description,groupId,priority,status,tags,type,startTime,endTime,createdTime",
        expression,
      });

      data
        .sort((p, n) => p.priority - n.priority)
        .forEach((item, index) => {
          setCalendarItem(item, index);
        });
    };

    const groupList = ref([]);
    const getGroupList = async () => {
      groupList.value = await findGroupList({
        sort: "priority,ASC",
      });
    };

    const onCalReady = (event) => {
      const expression = `(startTime ge ${event.firstCellDate.toISOString()} AND startTime le ${event.lastCellDate.toISOString()}) OR (endTime ge ${event.firstCellDate.toISOString()} AND endTime le ${event.lastCellDate.toISOString()})`;
      getCampaignList(expression);
    };
    onMounted(() => {
      getGroupList();
    });

    // 处理样式
    const onUnfocusEvent = () => {
      calRef.value.unfocusEvent();
    };

    const setup = {
      t,
      onCalReady,
      module,
      calRef,
      startDate,
      currentData,
      events,
      formData,
      handDetails,
      isShowModel,
      groupList,
      onEventClick,
      handleClick,
      onUnfocusEvent,
      handleOk,
    };
    provide("main", setup);
    return setup;
  },
};
</script>

<style scoped lang="less">
:deep(.vuecal__cell) {
  text-align: left;
}

:deep(.vuecal__cell-events) {
  margin-top: 30px;
}

:deep(.vuecal--month-view .vuecal__cell-content) {
  justify-content: flex-start;
}

:deep(.vuecal--no-time .vuecal__event) {
  padding: 3px 5px;
  cursor: pointer;
}

:deep(.vuecal__cell-date) {
  position: absolute;
  top: 5px;
  left: 5px;
}

:deep(.vuecal__event-title) {
  font-size: 14px;
  color: #ffffff;
}

:deep(.vuecal__event-content) {
  font-size: 10px;
  color: #ffffff;
}

:deep(.vuecal__weekdays-headings) {
  padding-right: 0;
}

:deep(.vuecal--color-0) {
  background-color: #aa9bf6;
}

:deep(.vuecal--color-1) {
  background-color: #00b42a;
}

:deep(.vuecal--color-2) {
  background-color: #86909c;
}

:deep(.vuecal--color-3) {
  background-color: #14c9c9;
}

:deep(.vuecal--color-4) {
  background-color: #9fdb1d;
}

:deep(.vuecal--color-5) {
  background-color: #f7ba1e;
}

:deep(.vuecal--color-6) {
  background-color: #99cc66;
}

:deep(.vuecal--color-7) {
  background-color: #ff9966;
}

:deep(.vuecal__menu) {
  justify-content: flex-end;
}

:deep(.vuecal__menu button) {
  font-size: 16px;
  border-bottom: 2px solid transparent;

  &.vuecal__view-btn--active {
    border-bottom: 2px solid rgb(var(--primary-6));
    color: rgb(var(--primary-6));
  }
}

:deep(.vuecal__event--focus, .vuecal__event:focus) {
  box-shadow: 0px 10px 10px -6px rgb(0 0 0 / 60%);
}
</style>
