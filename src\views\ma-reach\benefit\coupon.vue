<template>
  <module edit>
    <template #main>
      <div class="template-body">
        <a-form
          ref="formRef"
          layout="vertical"
          class="general-form"
          :model="entity"
        >
          <a-space direction="vertical" :size="16">
            <a-card class="general-card">
              <a-row :gutter="80">
                <a-col :span="18">
                  <a-form-item label="模板名称" field="name">
                    <a-input
                      v-model="entity.name"
                      placeholder="请输入模板名称"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="状态" field="status">
                    <a-switch
                      v-model="entity.status"
                      type="round"
                      checked-value="ENABLED"
                      unchecked-value="DISABLED"
                    >
                      <template #checked> 启用 </template>
                      <template #unchecked> 禁用 </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="渠道" field="channelType">
                    <div class="m-t-p m-t-sl">
                      <a-select
                        v-model="entity.channelId"
                        placeholder="请选择渠道"
                        popup-container=".m-t-sl"
                      >
                        <a-option
                          v-for="item in channelList"
                          :key="item.id"
                          :value="item.id"
                          :label="item.name"
                        />
                      </a-select>
                    </div>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="卡券编码" field="setting.templateId">
                    <a-input
                      v-model="entity.setting.templateId"
                      placeholder="请输入卡券编码"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="描述信息" field="summary">
                    <a-textarea
                      v-model="entity.summary"
                      placeholder="请输入描述信息"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-card>
          </a-space>
        </a-form>
      </div>
    </template>
    <template #action>
      <a-space>
        <!-- <a-button v-if="module.isEdit" type="primary" @click="simulateSend"> 模拟发送</a-button> -->
      </a-space>
      <simulate-dialog ref="simulateDlg" />
    </template>
  </module>
</template>

<script>
import { ref, provide } from "vue";
import { useRoute, useRouter } from "vue-router";
import { findBenefitItem, saveBenefitInfo, findChannelList } from "@/api/benefit";
// import { formatFields } from "@/utils/field";
import { Message } from "@arco-design/web-vue";
// import { findCustomerModel } from "@/api/system";
import SimulateDialog from "@/components/modal-dlg/customer-selector-dlg.vue";

export default {
  components: {
    SimulateDialog
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      mainPath: "/reach/benefit",
      breadcrumb: [
        {
          name: "营销云权益",
          path: "/reach/benefit"
        },
        {
          name: "编辑卡券模板"
        }
      ],
      capabilityType: "coupon",
      isEdit: !!route.query.id
    });

    let queryValue = route.query.id;

    const entity = ref({
      status: false,
      type: module.value.capabilityType,
      category: "benefit",
      budgetSetting: {},
      setting: {
        type: module.value.capabilityType,
        feedback: false
      }
    });

    const channelList = ref([]);
    const simulateDlg = ref(null);

    const bindData = async () => {
      if (module.value.isEdit) {
        entity.value = await findBenefitItem(queryValue);
      }
      channelList.value = await findChannelList({
        expression: `type eq ${module.value.capabilityType}`
      });
      if (!entity.value.channelId && channelList.value.length === 1) {
        entity.value.channelId = channelList.value[0].id;
      }
    };

    const save = async () => {
      await saveBenefitInfo(entity.value);
      queryValue = entity.value.id;
      module.value.isEdit = true;
      Message.success("保存成功！");
      if (!entity.value.id) {
        router.push({ path: module.value.mainPath });
      }
    };

    const simulateSend = async () => {
      simulateDlg.value.handleClick(entity.value.id);
    };

    const setup = {
      module,
      entity,
      channelList,
      bindData,
      save,
      simulateSend,
      simulateDlg
    };
    provide("edit", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped>
.template-body {
  display: flex;
  height: calc(100vh - 160px);
  background-color: #ffffff;

  .left-form {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}

::v-deep(.general-card) {
  min-height: calc(100vh - 160px);
}

.m-t-p {
  position: relative;
  width: 100%;
}
</style>
