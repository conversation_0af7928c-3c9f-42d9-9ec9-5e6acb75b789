<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (!data.filters) {
        return brief;
      }
      for (let index = 0; index < data.filters.length; index += 1) {
        const element = data.filters[index];
        let { name } = element;
        if (data.filterStrategy === "PRIORITY") {
          name += `[${element.priority}]`;
        }
        if (data.defaultEnable && element.defaultFilter) {
          name = `*${name}`;
        }

        brief.push({ sort: 0, label: name, value: "" });
      }

      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'filter' })
    const setup = {
      title: item.name || "过滤",
      summary: item.name || "过滤节点",
      iconClass: item.icon || "icon-ma-filter",
      nodeClass: "easyflow-node-filter",
      headerColor: item.themeColor|| "#fba980",
      headerBgColor: item.themeColor || "#fba980",
      background: item.background || "#fff9ef",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
