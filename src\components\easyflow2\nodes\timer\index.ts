import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import TimerNode from "./node.vue";
import TimerPannel from "./pannel.vue";

const nodeData = {
  type: "timer",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<TimerNode />`,
      components: {
        TimerNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("TimerNode", nodeData.node, true);
};

const Timer = {
  type: "timer",
  name: "定时",
  shape: "TimerNode",
  iconClass: "icon-timing",
  color: "#105a63",
  themebg: "#f9f5ff",
  registerNode: registerNode,
  pannel: TimerPannel,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Timer;
