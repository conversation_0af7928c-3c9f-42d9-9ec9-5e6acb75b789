<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <module edit>
    <template #main>
      <a-form layout="vertical" class="general-form" :model="entity">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row :gutter="80">
              <a-col :span="8">
                <a-form-item field="YEAR" label="限制年" label-col-flex="70px">
                  <a-input-number
                    v-model="dateUnits.YEAR"
                    placeholder="沟通限制年次数"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="MONTH" label="限制月" label-col-flex="70px">
                  <a-input-number
                    v-model="dateUnits.MONTH"
                    placeholder="沟通限制月次数"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="DAY" label="限制日" label-col-flex="70px">
                  <a-input-number
                    v-model="dateUnits.DAY"
                    placeholder="沟通限制日次数"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-space>
      </a-form>
    </template>
  </module>
</template>

<script>
import { ref, provide,getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import {
  getMarketingCenter,
  saveAllChannelLimit
} from "@/api/marketing_center";
import { Message } from "@arco-design/web-vue";

export default {
  setup() {const {
      proxy: { t }
    } = getCurrentInstance()
    const router = useRouter();

    const spinLoading = ref(false);
    const module = ref({
      entityIdField: "id",
      entityName: "全渠道限制",
      mainPath: "/system/setting",
      breadcrumb: [
        {
          name: t('menu.system'),
          path: "/system/setting"
        },
        {
          name: "全渠道限制"
        }
      ]
    });
    const loading = ref(false);
    const dateUnits = ref({
      YEAR: null,
      MONTH: null,
      DAY: null
    });
    const entity = ref({
      enabled: true,
      type: "DATE",
      // frequencies: [
      //   {
      //     days: 0,
      //     times: 0
      //   }
      // ],
      dateUnits: []
    });

    const bindData = async () => {
      const res = await getMarketingCenter();
      if (res.setting?.limitSetting?.dateUnits) {
        // eslint-disable-next-line array-callback-return
        res.setting.limitSetting.dateUnits.map((item) => {
          if (item.unit === "YEAR") {
            dateUnits.value.YEAR = item.times;
          }
          if (item.unit === "MONTH") {
            dateUnits.value.MONTH = item.times;
          }
          if (item.unit === "DAY") {
            dateUnits.value.DAY = item.times;
          }
        });
      }
    };

    const save = async () => {
      // eslint-disable-next-line no-restricted-syntax
      for (const item in dateUnits.value) {
        if (dateUnits.value[item]) {
          entity.value.dateUnits.push({
            unit: item,
            times: dateUnits.value[item]
          });
        }
      }
      spinLoading.value = true;
      await saveAllChannelLimit(entity.value);
      spinLoading.value = false;
      Message.success("保存成功！");
      bindData();
    };
    const quit = () => {
      router.push({ path: module.value.mainPath });
    };
    const setup = {
      t,
      save,
      router,
      module,
      entity,
      bindData,
      loading,
      dateUnits,
      quit
    };
    provide("edit", setup);
    return setup;
  }
};
</script>
