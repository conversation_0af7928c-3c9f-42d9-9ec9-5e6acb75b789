<template>
  <a-alert type="warning">
    MA分析功能配置。
  </a-alert>
  <a-form ref="formRef" :model="formData">
  </a-form>
</template>

<script setup>
import { ref, computed } from "vue";

const formRef = ref(null);
const formData = ref({});

const commit = async (entity) => {
  if (await formRef.value.validate()) {
    return false;
  }

  return true;
}

defineExpose({
  commit
})
</script>

<style lang="less" scoped>

.arco-alert {
    margin: 10px;
  }

</style>
