<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { shortDate } from "@/utils/date"
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (!data.schedule) {
        return brief;
      }
      brief.push({ sort: 0, label: "批次数量", value: data.batchSize });
      brief.push({ sort: 1, label: "执行时间", value: data.schedule.cron });
      brief.push({ sort: 2, label: "执行周期", value: `${shortDate(data.schedule.startTime)} 至 ${shortDate(data.schedule.startTime)}`});

      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    const item = nodeApi.find(item => { return item.type === 'restrict' })
    const setup = {
      title: item.name || "限流",
      summary: item.name || "限流节点",
      iconClass: item.icon || "icon-timing",
      nodeClass: "easyflow-pannel-timer",
      headerColor: item.themeColor || "#105a63",
      headerBgColor: item.themeColor || "#9b69ff",
      background: item.background || "#f9f5ff",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
