<template>
  <div class="content">
    <a-result
      class="result"
      status="404"
      :subtitle="'页面好像没有找到，点击返回'"
    >
    </a-result>
    <div class="operation-row">
      <a-button key="back" type="primary" @click="back"> 返回上一页 </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from "vue-router";

const router = useRouter();
const back = () => {
  // warning： Go to the node that has the permission
  router.go(-1);
};
</script>

<style scoped lang="less">
.content {
  // padding-top: 100px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -95px;
  margin-top: -121px;
  text-align: center;
}
</style>
