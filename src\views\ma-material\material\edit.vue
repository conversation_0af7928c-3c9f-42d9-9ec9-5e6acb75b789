<template>
  <module edit>
    <template #main>
      <a-form
        ref="formRef"
        layout="horizontal"
        class="general-form"
        :model="entity"
      >
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="type"
                  :label="t('material.form.type')"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-radio-group v-model="entity.type" :disabled="true">
                    <a-radio value="image">图片</a-radio>
                    <a-radio value="audio">音频</a-radio>
                    <a-radio value="video">视频</a-radio>
                    <a-radio value="file">文件</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="name"
                  :label="t('material.form.name')"
                  :rules="[
                    { required: true, message: t('material.form.name_required') },
                    { max: 50, message: '素材名称不能超过50个字符' }
                  ]"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-input
                    v-model="entity.name"
                    :placeholder="t('material.form.name_placeholder')"
                    :max-length="50"
                    show-word-limit
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="id"
                  :label="t('material.form.id')"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-input v-model="entity.id" disabled />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="80" v-if="entity.type === 'video'">
              <a-col :span="24">
                <a-form-item
                  field="videoTitle"
                  :label="t('material.form.video_title')"
                  :rules="[
                    { required: true, message: t('material.form.video_title_required') },
                    { max: 64, message: '视频标题不能超过64个字符' }
                  ]"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-input
                    v-model="entity.videoTitle"
                    :placeholder="t('material.form.video_title_placeholder')"
                    :max-length="64"
                    show-word-limit
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="url"
                  :label="t('material.form.link')"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-input v-model="entity.url" disabled />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="80" v-if="entity.type === 'video'">
              <a-col :span="24">
                <a-form-item
                  field="videoDescription"
                  :label="t('material.form.video_description')"
                  :rules="[
                    { required: true, message: t('material.form.video_description_required') },
                    { max: 200, message: '视频描述不能超过200个字符' }
                  ]"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-textarea
                    v-model="entity.videoDescription"
                    :placeholder="t('material.form.video_description_placeholder')"
                    :max-length="200"
                    show-word-limit
                    :auto-size="{ minRows: 3, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="updateTime"
                  :label="t('material.form.update_time')"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-date-picker v-model="entity.updateTime" disabled style="width: 100%" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="createTime"
                  :label="t('material.form.create_time')"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-date-picker v-model="entity.createTime" disabled style="width: 100%" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-space>
      </a-form>
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Message } from "@arco-design/web-vue";
import { getMaterial, updateMaterial } from "@/api/material";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const queryValue = route.params.id || route.query.id;

    const module = ref({
      entityIdField: "id",
      mainPath: "/material/material",
      breadcrumb: [
        { name: t('material.title'), path: "/material/material" },
        { name: t('material.operation.edit') }
      ],
      isEdit: !!queryValue
    });

    const loading = ref(false);
    const entity = ref({
      id: "",
      name: "",
      type: "image",
      url: "",
      videoTitle: "",
      videoDescription: "",
      updateTime: "",
      createTime: ""
    });
    const formRef = ref({});

    const getTypeText = (type) => {
      const textMap = {
        "image": t('material.tabs.image'),
        "video": t('material.tabs.video'),
        "audio": t('material.tabs.audio'),
        "file": t('material.tabs.file')
      };
      return textMap[type] || type;
    };

    const bindData = async () => {
      if (module.value.isEdit) {
        try {
          loading.value = true;
          const res = await getMaterial(parseInt(queryValue));
          const materialData = res.data || res;

          // 转换类型字段从中文到英文
          const typeMap = {
            '图片': 'image',
            '音频': 'audio',
            '视频': 'video',
            '附件': 'file'
          };

          entity.value = {
            ...materialData,
            type: typeMap[materialData.type] || materialData.type.toLowerCase()
          };
        } catch (error) {
          console.error('获取素材详情失败:', error);
          Message.error('获取素材详情失败');
          // 如果API调用失败，保持默认的空数据结构
          entity.value = {
            id: queryValue,
            name: "",
            type: "image",
            url: "",
            videoTitle: "",
            videoDescription: "",
            updateTime: "",
            createTime: ""
          };
        } finally {
          loading.value = false;
        }
      } else {
        // 新增模式初始化
        entity.value = {
          id: "",
          name: "",
          type: "image",
          url: "",
          videoTitle: "",
          videoDescription: "",
          updateTime: "",
          createTime: ""
        };
      }
    };

    const quit = () => {
      router.push({ path: module.value.mainPath });
    };

    const save = async () => {
      const valid = await formRef.value.validate();
      if (valid) return;
      loading.value = true;
      try {
        if (module.value.isEdit) {
          // 转换类型字段从英文到中文以保持数据一致性
          const reverseTypeMap = {
            'image': '图片',
            'audio': '音频',
            'video': '视频',
            'file': '附件'
          };

          const saveData = {
            ...entity.value,
            type: reverseTypeMap[entity.value.type] || entity.value.type
          };

          await updateMaterial(entity.value.id, saveData);
        } else {
          // 新增功能暂时不实现，因为素材通常通过上传创建
          Message.warning('新增素材请通过上传功能');
          return;
        }
        Message.success(t('material.message.update_success'));
        quit();
      } catch (e) {
        console.error('保存失败:', e);
        Message.error(t('material.message.update_failed'));
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      bindData();
    });

    const setup = {
      t,
      save,
      route,
      router,
      module,
      entity,
      formRef,
      bindData,
      loading,
      getTypeText
    };
    provide("edit", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped>
// 让所有表单标签左对齐且不换行
:deep(.arco-form-item-label) {
  text-align: left !important;
  white-space: nowrap !important;
}

// 让表单内容左对齐
:deep(.arco-form-item-content) {
  text-align: left !important;
}

// 让所有输入框内容左对齐
:deep(.arco-input),
:deep(.arco-textarea),
:deep(.arco-radio-group) {
  text-align: left !important;
}
</style>