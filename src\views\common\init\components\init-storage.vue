<template>
  <a-form ref="formRef" :model="formData">
    <a-form-item field="type" label="OSS类型" :rules="[{ required: true, message: '请选择OSS类型' }]">
      <a-select v-model="formData.type" allow-clear placeholder="请选择OSS类型">
        <template #empty>
          <a-empty description="没有可使用的数据源" />
        </template>
        <a-option value="s3"> S3 </a-option>
      </a-select>
    </a-form-item>
    <a-form-item field="endpoint" label="服务地址" :rules="[{ required: true, message: '请输入OSS存储服务地址' }]">
      <a-input v-model="formData.endpoint" placeholder="请输入OSS存储服务地址" />
    </a-form-item>
    <a-form-item field="accessKey" label="访问键" :rules="[{ required: true, message: '请输入OSS服务访问键' }]">
      <a-input v-model="formData.accessKey" placeholder="请输入OSS服务访问键" />
    </a-form-item>

    <a-form-item field="secretKey" label="访问密钥" :rules="[{ required: true, message: '请输入OSS服务访问密钥' }]">
      <a-input v-model="formData.secretKey" placeholder="请输入OSS服务访问密钥" />
    </a-form-item>

    <a-form-item field="bucketName" label="存储桶" :rules="[{ required: true, message: '请输入存储桶名称' }]">
      <a-input v-model="formData.bucketName" placeholder="请输入存储桶名称" />
    </a-form-item>

    <a-form-item field="basePath" label="基础目录">
      <a-input v-model="formData.basePath" placeholder="请输入OSS存储基础目录" />
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps(["setting"]);
const formData = ref(props.setting);
const formRef = ref(null);

const commit = async (entity) => {
  if (await formRef.value.validate()) {
    return false;
  }
  entity.value.setting.bucketSetting = formData.value;
  return true;
};
defineExpose({
  commit
})
</script>

<style lang="less" scoped>

.arco-alert {
    margin: 10px;
  }

</style>
