<template>
  <module edit>
    <template v-slot:main>
      <a-tabs v-model="activeTabCode"
              class="tab-body-list"
              type="capsule"
              @tab-click="changeActiveTabCode"
              default-active-key="1">
        <a-tab-pane key="1" title="高级设置">
          <a-form :model="entity" ref="formDataRef">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item field="name"
                             :rules="[
                                     { required: true, message: '不允许为空' }
                                  ]"
                             :validate-trigger="['input']"
                             label="模型名称"
                             label-col-flex="70px">
                  <a-input v-model="entity.name" placeholder="请输入模型名称"/>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="code"
                             :rules="[
                                     { required: true, message: '不允许为空' }
                                  ]"
                             :validate-trigger="['input']"
                             label="模型编码"
                             label-col-flex="70px">
                  <a-input v-model="entity.code" placeholder="请输入模型编码"/>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="period" label="窗口期" label-col-flex="70px">
                  <a-select v-model="entity.period" placeholder="请输入模型名称">
                    <a-option v-for="item of timeList" :value="item.value" :label="item.label"/>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="interval" label="计算区间" label-col-flex="70px">
                  <a-select v-model="entity.interval"
                            @change="changeInterval"
                            placeholder="请选择">
                    <a-option :value="3">3</a-option>
                    <a-option :value="4">4</a-option>
                    <a-option :value="5">5</a-option>
                    <a-option :value="6">6</a-option>
                    <a-option :value="7">7</a-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item field="period" label="行为模型" label-col-flex="70px">
                  <a-select v-model="entity.behaviorPersistentModelId"
                            :disabled="isEdit"
                            @change="changeCollectionItem"
                            placeholder="请选择行为模型">
                    <a-option v-for="item of collectionList" :value="item.id" :label="item.aliasName"/>
                  </a-select>
                </a-form-item>
              </a-col>

              <template v-if="!entity.basicModel && entity.behaviorPersistentModelId">
                <a-col :span="12">
                  <a-form-item field="targetTimeField"
                               :rules="[
                                     { required: true, message: '不允许为空' }
                                  ]"
                               :validate-trigger="['change']"
                               label="R" label-col-flex="70px">
                    <a-tree-select
                        v-model="entity.targetTimeField"
                        :data="sourceListR"
                        :disabled="isEdit"
                        placeholder="请选择用于记录最后一次购买时间的会员字段"
                        :fieldNames="{
                          key: 'path',
                          title: 'aliasName',
                          children: 'fields'
                        }"></a-tree-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="sourceTimeField"
                               :rules="[
                                     { required: true, message: '不允许为空' }
                                  ]"
                               :validate-trigger="['change']"
                               no-style>
                    <a-tree-select
                        v-model="entity.sourceTimeField"
                        :data="targetListR"
                        :disabled="isEdit"
                        placeholder="请选择订单购买时间字段"
                        :fieldNames="{
                          key: 'path',
                          title: 'aliasName',
                          children: 'fields'
                        }"></a-tree-select>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item field="rRemark" label="R值描述" label-col-flex="70px">
                    <a-input v-model="entity.rRemark" placeholder="请输入R值描述"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="targetFrequencyField"
                               :rules="[
                                     { required: true, message: '不允许为空' }
                                  ]"
                               :validate-trigger="['change']"
                               label="F" label-col-flex="70px">
                    <a-tree-select
                        v-model="entity.targetFrequencyField"
                        :data="sourceListF"
                        :disabled="isEdit"
                        placeholder="请选择用于记录购买频次的用户字段"
                        :fieldNames="{
                          key: 'path',
                          title: 'aliasName',
                          children: 'fields'
                        }"></a-tree-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="sourceIdField"
                               :rules="[
                                     { required: true, message: '不允许为空' }
                                  ]"
                               :validate-trigger="['change']"
                               no-style>
                    <a-tree-select
                        v-model="entity.sourceIdField"
                        :data="targetListF"
                        :disabled="isEdit"
                        placeholder="请选择订单id字段"
                        :fieldNames="{
                          key: 'path',
                          title: 'aliasName',
                          children: 'fields'
                        }"></a-tree-select>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item field="fRemark" label="F值描述" label-col-flex="70px">
                    <a-input v-model="entity.fRemark" placeholder="请输入F值描述"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="targetAmountField"
                               :rules="[
                                     { required: true, message: '不允许为空' }
                                  ]"
                               :validate-trigger="['change']"
                               label="M" label-col-flex="70px">
                    <a-tree-select
                        v-model="entity.targetAmountField"
                        :data="sourceListM"
                        :disabled="isEdit"
                        placeholder="请选择用于记录消费总额的会员字段"
                        :fieldNames="{
                          key: 'path',
                          title: 'aliasName',
                          children: 'fields'
                        }"></a-tree-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="sourceAmountField"
                               :rules="[
                                     { required: true, message: '不允许为空' }
                                  ]"
                               :validate-trigger="['change']"
                               no-style>
                    <a-tree-select
                        v-model="entity.sourceAmountField"
                        :data="targetListM"
                        :disabled="isEdit"
                        placeholder="请选择订单消费金额字段"
                        :fieldNames="{
                          key: 'path',
                          title: 'aliasName',
                          children: 'fields'
                        }"></a-tree-select>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item field="mRemark" label="M值描述" label-col-flex="70px">
                    <a-input v-model="entity.mRemark" placeholder="请输入M值描述"/>
                  </a-form-item>
                </a-col>
              </template>
            </a-row>
          </a-form>
          <a-alert v-if="!entity.behaviorPersistentModelId">配置助手：您需要设置‘行为模型’方可进行进一步配置。</a-alert>
          <a-alert v-if="entity.behaviorPersistentModelId && isDisabled">配置助手：请按提示依次配置R/F/M对应会员/订单字段。</a-alert>
        </a-tab-pane>
        <a-tab-pane key="2"
                    :disabled="isDisabled"
                    title="区间设置">
          <a-divider orientation="left">RFM 基本配置</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="code" label="模型编码" label-col-flex="70px">
                <a-input v-model="entity.code" disabled placeholder="请输入模型编码"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="period" label="窗口期" label-col-flex="70px">
                <a-select v-model="entity.period" placeholder="请选择窗口期">
                  <a-option v-for="item of timeList" :value="item.value" :label="item.label"/>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-divider orientation="left">RFM 人群分布参考</a-divider>
          <a-row :gutter="16">
            <a-col :span="8">
              <ChartColumnar
                  flag="R"
                  icon="icon-timing"
                  :avg="entity.items[0].avg"
                  title="Rencency 消费间隔"
                  :rangeSize="entity.interval"
                  subtitle="R 消费间隔 - 人群分布"
                  :period="entity.period"
                  :value="entity.items[0].targetSections"
                  :range="resultSections.R"/>
            </a-col>
            <a-col :span="8">
              <ChartColumnar
                  flag="F"
                  icon="icon-management"
                  :avg="entity.items[1].avg"
                  title="Frequency 消费频率"
                  :rangeSize="entity.interval"
                  subtitle="F 消费频率 - 人群分布"
                  :value="entity.items[1].targetSections"
                  :range="resultSections.F"/>
            </a-col>
            <a-col :span="8">
              <ChartColumnar
                  flag="M"
                  icon="icon-money"
                  :avg="entity.items[2].avg"
                  title="Monetary 消费金额"
                  :rangeSize="entity.interval"
                  subtitle="M 消费金额 - 人群分布"
                  :value="entity.items[2].targetSections"
                  :range="resultSections.M"/>
            </a-col>
          </a-row>
          <a-divider orientation="left">RFM 范围区间配置</a-divider>
          <a-row :gutter="16">
            <a-col :span="8">
              <FormNumber :entity="entity"
                          flag="R"
                          :rangeSize="entity.interval"
                          v-model:targetNumber="entity.items[0].targetSections"/>
            </a-col>
            <a-col :span="8">
              <FormNumber :entity="entity"
                          flag="F"
                          :rangeSize="entity.interval"
                          v-model:targetNumber="entity.items[1].targetSections"/>
            </a-col>
            <a-col :span="8">
              <FormNumber :entity="entity"
                          flag="M"
                          :rangeSize="entity.interval"
                          v-model:targetNumber="entity.items[2].targetSections"/>
            </a-col>
          </a-row>
          <a-divider orientation="left">RFM 对比值配置</a-divider>
          <div class="footer-number">
            取平均值:
            <div class="item"><span>Rencency:</span> {{
                entity.items[0].avg ||
                (entity.items[0].avg == 0 ? 0 : "--")
              }}</div>
            <div class="item"><span>Frequency:</span>  {{
                entity.items[1].avg ||
                (entity.items[1].avg == 0 ? 0 : "--")
              }}</div>
            <div class="item"><span>Monetary:</span>  {{
                entity.items[2].avg ||
                (entity.items[2].avg == 0 ? 0 : "--")
              }}</div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </template>
  </module>
</template>

<script>
import {ref, provide, reactive, onMounted, watch} from "vue";
import {useRoute, useRouter} from "vue-router";
import {Message} from "@arco-design/web-vue";
import ChartColumnar from './component/chart-columnar.vue'
import FormNumber from "./component/form-number.vue";
import {
  findBehaviorModelList,
  getCustomerModelList,
  setTargetSourceData,
  getInfoData
} from "@/api/system";
import {findCustomerPage} from "@/api/audience";
import {useBussinessUnitStore} from "@/store";
import {FieldTypes} from "@/utils/field";

export default {
  components: {
    ChartColumnar,
    FormNumber
  },
  setup() {
    const userTc = useBussinessUnitStore()
    const route = useRoute();
    const router = useRouter();
    const addType = route.query.type;
    const module = ref({
      entityIdField: "id",
      mainPath: "/system/rfm",
      breadcrumb: [
        {
          name: "系统设置",
          path: "/system/setting",
        },
        {
          name: "分析模型-RFM",
          path: "/system/rfm"
        },
        {
          name: "编辑RFM"
        }
      ],
    });
    let queryValue = route.query.id;
    let isEdit = ref(!!queryValue);

    module.value.breadcrumb[2].name = isEdit.value ? `编辑${addType === 'basis' ? '基础' : '自定义'}RFM` : `新建${addType === 'basis' ? '基础' : '自定义'}RFM`;

    const timeList = ref([
      {value: 30, label: "最近30天"},
      {value: 90, label: "最近90天"},
      {value: 120, label: "最近120天"},
      {value: 150, label: "最近150天"},
    ])
    const activeTabCode = ref('1')
    let entity = ref({
      code: "",
      behaviorPersistentModelId: '',
      customerPersistentModelId: userTc.isInitializeData.cdpSetting.modelId,
      collectionId: userTc.isInitializeData.cdpSetting.collectionId,
      collectionName: userTc.isInitializeData.infraSetting?.dataWareHouseSetting.dataCollectionName || '',
      customerModelName: userTc.isInitializeData.infraSetting?.dataWareHouseSetting.customerModelName || '',
      autoCalcAvg: true,
      enable: true,
      items: [
        {
          targetSections: [0, 0, 0, 0, 0],
          timeField: "time",
          type: "recency",
        },
        {
          targetSections: [0, 0, 0, 0, 0],
          timeField: "time",
          type: "monetary",
        },
        {
          targetSections: [0, 0, 0, 0, 0],
          timeField: "time",
          type: "frequency",
        },
      ],
      period: 30,
      interval: 5
    })
    const isDisabled = ref(false)
    const sourceList = ref([])

    // 处理数据
    const initFields = (source, data, parent, is) => {
      if (!Array.isArray(data)) return;
      data.forEach((i, index) => {
        if (parent) {
          i.path = `${parent.name}.${i.name}`;
          i.aliasName = `${i.name}      「${parent.aliasName}-${i.aliasName}」`;
        } else {
          i.path = i.name;
          if (!i?.fields || i?.fields?.length === 0) {
            i.aliasName = `${i.name}      「${i.aliasName}」`
          }
        }


        if (is) {
          let isOk = is.some(x => x === i.type)
          if (isOk) {
            source.push({
              ...i,
              fields: []
            })
          }
        }

        if (i.fields && i.fields.length > 0) {
          return initFields(source, i.fields, i, is);
        }
      });
    }
    const initTargetFields = (data, parent) => {
      if (!Array.isArray(data)) return;
      data.forEach((i, index) => {
        if (parent) {
          i.path = `${parent.name}.${i.name}`;
        } else {
          i.path = i.name;
        }

        i.aliasName = `${i.name}      「${i.aliasName}」`
        if (i.fields && i.fields.length > 0) {
          return initTargetFields(i.fields, i);
        }
      });
    }

    watch(() => entity, () => {
      changeActiveTabCode()
    }, { deep: true })

    // 判断是否可以切换
    const changeActiveTabCode = (e) => {
      if (!entity.value.targetTimeField ||
          !entity.value.sourceTimeField ||
          !entity.value.targetFrequencyField ||
          !entity.value.sourceIdField ||
          !entity.value.targetAmountField ||
          !entity.value.sourceAmountField) {
        isDisabled.value = true
        return false
      } else {
        isDisabled.value = false
      }
    }

    // 获取行为模型
    const collectionList = ref([])
    const sourceListR = ref([])
    const sourceListF = ref([])
    const sourceListM = ref([])
    findBehaviorModelList().then(res => {
      res.forEach(item => {
        if (item.dataPersistentInfo.persistentType === 'behavior') {
          collectionList.value.push(item)
        }
      })
    })

    getCustomerModelList(userTc.isInitializeData.infraSetting.dataWareHouseSetting.customerModelId).then(res => {
      initFields(sourceListR.value, JSON.parse(JSON.stringify(res.fields)), '', [FieldTypes.DATE])
      initFields(sourceListF.value, JSON.parse(JSON.stringify(res.fields)), '', [FieldTypes.INTEGER, FieldTypes.LONG])
      initFields(sourceListM.value, JSON.parse(JSON.stringify(res.fields)), '', [FieldTypes.FLOAT, FieldTypes.DOUBLE])
    })

    // 选择行为模型
    const targetListR = ref([])
    const targetListF = ref([])
    const targetListM = ref([])
    const changeCollectionItem = (id) => {
      if (route.query.type === 'basis') { return false }

      targetListR.value = []
      targetListF.value = []
      targetListM.value = []
      entity.value.sourceTimeField = ''
      entity.value.sourceIdField = ''
      entity.value.sourceAmountField = ''
      const item = collectionList.value.find(x => { return x.id === id })
      initFields(targetListR.value, JSON.parse(JSON.stringify(item.fields)), '', ['date'])
      initFields(targetListF.value, JSON.parse(JSON.stringify(item.fields)), '', ['string'])
      initFields(targetListM.value, JSON.parse(JSON.stringify(item.fields)), '', ['FLOAT', 'DOUBLE'])
    }


    const getDay = (num) => {
      const date = new date();
      date.setDate(date.getDate() - Number(num))
      let year = date.getFullYear()				//年
      let month = date.getMonth() + 1				//月
      let day = date.getDate();					//日
      return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
    }

    const resultSections = reactive({
      R: new Array(entity.value.interval).fill(0),
      F: new Array(entity.value.interval).fill(0),
      M: new Array(entity.value.interval).fill(0)
    })
    const getChartValue = (curValue, data, x, y) => {
      let arr = new Array(data.length).fill(0)
      let and = 0
      let take = 0
      data.forEach((item, index) => {
        let expression = ''

        if (x === 'R') {
          if (index === 0) {
            expression = `${curValue} gt ${getDay(item)} AND  ${curValue} lt ${getDay(data[index + 1])}`
          } else if (index === data.length - 1) {
            expression = `${curValue} gt ${getDay(item)} AND ${curValue} lt ${getDay(entity.value.interval)}`
          } else {
            expression = `${curValue} gt ${getDay(item + 1)} AND ${curValue} lt ${getDay(data[index + 1])}`
          }
        } else {
          if (index === data.length - 1) {
            expression = `${curValue} ge ${item + 1}`
          } else if (index === 0) {
            expression = `${curValue} ge 0 AND ${curValue} le ${Number(data[index + 1])}`
          } else {
            expression = `${curValue} ge ${Number(data[index]) + 1} AND ${curValue} le ${Number(data[index + 1])}`
          }
        }
        findCustomerPage({ expression: expression }, userTc.isInitializeData.cdpSetting.collectionName, userTc.isInitializeData.cdpSetting.modelId).then(res => {
          // this.$set(this.entity.value.items[x].resultSections, res.totalElements, index)
          arr[index] = res.totalElements
          resultSections[x] = JSON.parse(JSON.stringify(arr))

          if (x === 'R') {
            take += res.totalElements * (entity.value.interval - index)
          } else {
            take += res.totalElements * (1 + index)
          }
          and += res.totalElements

          entity.value.items[y].avg = (take / (and === 0 ? 1: and)).toFixed(2)
        })
      })
    }

    // 监听数据变化
    watch(() => entity.value.items[0].targetSections, (v) => {
      if (!entity.value.targetTimeField) return false
      getChartValue(entity.value.targetTimeField, v, 'R', 0)
    }, { deep: true })
    watch(() => entity.value.items[1].targetSections, (v) => {
      if (!entity.value.targetFrequencyField) return false
      getChartValue(entity.value.targetFrequencyField, v, 'F', 1)
    }, { deep: true })
    watch(() => entity.value.items[2].targetSections, (v) => {
      if (!entity.value.targetAmountField) return false
      getChartValue(entity.value.targetAmountField, v, 'M', 2)
    }, { deep: true })

    const changeInterval = () => {
      entity.value.items[0].targetSections = new Array(entity.value.interval).fill(0)
      entity.value.items[1].targetSections = new Array(entity.value.interval).fill(0)
      entity.value.items[2].targetSections = new Array(entity.value.interval).fill(0)
    }

    onMounted(() => {
      if (route.query.type === 'basis') {
        entity.value.basicModel = true
        entity.value.sourceTimeField = 'time'
        entity.value.targetTimeField = 'basicInfo.lastPurchaseTime'
        entity.value.sourceIdField = 'id'
        entity.value.targetFrequencyField = 'basicInfo.orderTotal'
        entity.value.sourceAmountField = 'amount'
        entity.value.targetAmountField = 'basicInfo.consumption'
      }
      changeActiveTabCode()
    })

    // 返回
    const quit = () => {
      router.push({ path: module.value.mainPath })
    }

    // 保存
    const formDataRef = ref(null)
    const save = async () => {
      const res = await formDataRef.value.validate();
      if (res) { return false }
      await setTargetSourceData(entity.value);
      Message.success('提交成功！');
      await quit()
    };

    if (queryValue) {
      getInfoData(queryValue).then(res => {
        entity.value = res
        if (entity.behaviorPersistentModelId) {
          changeCollectionItem(entity.behaviorPersistentModelId)
        }
      })
    }

    const setup = {
      module,
      activeTabCode,
      entity,
      isDisabled,
      timeList,
      sourceListR,
      sourceListF,
      sourceListM,
      targetListR,
      targetListF,
      targetListM,
      sourceList,
      collectionList,
      isEdit,
      save,
      quit,
      resultSections,
      changeActiveTabCode,
      changeCollectionItem,
      getDay,
      getChartValue,
      formDataRef,
      changeInterval
    };
    provide("edit", setup);
    return setup;
  },
};
</script>
<style lang="less" scoped>
.tab-body-list {
  margin: 20px;
}
.footer-number{
  display: flex;
  align-items: center;
  .item{
    margin-left: 50px;
  }
}
</style>
