<template>
  <div class="easyflow-pannel-points">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="积分规则" class="form-item-select">
        <a-select v-model="entity.capabilityId" class="easyflow-select" placeholder="请选择积分规则" :loading="loading"
          allow-search>
          <a-option v-for="item of capabilities" :key="item.id" :label="item.label" :value="item.id">
            [{{ item.name }}] <span class="light-text"> {{ item.id }} </span>
          </a-option>
        </a-select>
      </a-form-item>
      <!-- <a-form-item label="预算设置" :content-flex="false">
        <a-checkbox v-model="entity.budgetSetting.enabled" class="budget" value="1" @change="toogleBudeget">参与计算费用 ( 单价:
          <span> {{ entity.budgetSetting.price }}</span>元 )</a-checkbox>
        <div v-if="entity.budgetSetting.enabled">
          <div class="flex-line">
            <a-select v-model="entity.budgetSetting.budgetItemId" class="easyflow-select" placeholder="请选择预算条目"
              :loading="loading" :filter-option="false" @search="handleSearchBudget">
              <a-option v-for="item of budgets" :key="item.id" :value="item.id">{{ item.name }}</a-option>
            </a-select>
            <a-button class="btn" type="outline" @click="addBudgetItemRef.show()">新增</a-button>
          </div>
        </div>
        <template #extra>
          <div class="tip">预算不足时停止发送</div>
        </template>
</a-form-item> -->
    </a-form>

    <AddBudgetItemDlg ref="addBudgetItemRef" :budget-id="budgetId" :bind-data="handleSearchBudget" />
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { findBenefitList, findBenefitItem } from "@/api/benefit";
import { findBudgetItemList } from "@/api/budget";
import AddBudgetItemDlg from "@/components/modal-dlg/add-budget-item-dlg.vue";

const { node } = defineProps(["node"]);
const flowIjt = inject("flow");
const budgetId = ref(flowIjt.flowId);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const entity = ref({
  capabilityId: null,
  budgetSetting: {
    enabled: false,
    budgetId: budgetId.value
  },
  frequencyLimit: true
});
const capabilities = ref([]);
const loading = ref(false);
const budgets = ref([]);
const addBudgetItemRef = ref(null);

const handleSearchBenefit = async (name) => {
  loading.value = true;
  const params = { fields: "name,budgetSetting", expression: "" };
  params.expression = "type eq points AND status eq ENABLED";
  if (name) {
    params.expression += " AND name like value";
  }
  capabilities.value = await findBenefitList(params);
  capabilities.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
  loading.value = false;
};

const showContent = async (capabilityId) => {
  const communicate = await findBenefitItem(capabilityId);
  entity.value.budgetSetting.price = communicate.budget;
};

const handleSearchBudget = async (name) => {
  loading.value = true;
  const params = {
    fields: "name",
    expression: `budgetId eq ${budgetId.value}`
  };
  if (name) {
    params.expression += ` AND name like ${name}`;
  }
  budgets.value = await findBudgetItemList(params);
  loading.value = false;
};

const toogleBudeget = () => {
  if (entity.value.budgetSetting.enabled) {
    entity.value.budgetSetting.budgetId = budgetId.value;
  }
};

const save = () => {
  return entity.value;
};

defineExpose({
  save
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  handleSearchBenefit();
  // showContent(entity.value.capabilityId);
  // handleSearchBudget();
});
</script>

<style lang="less" scoped>
.easyflow-pannel-points {
  .points-content {
    color: #b3b3b3;
  }

  .budget {
    color: rgb(var(--primary-6));

    .tip {
      color: #b3b3b3;
    }
  }
}
</style>
