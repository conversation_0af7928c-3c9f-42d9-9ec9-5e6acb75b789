import { Graph } from "@antv/x6";
import { getPorts, rect } from "@/components/easyflow2/components/node";
import AudienceNode from "./node.vue";
import AudiencePannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "audience_receive",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<AudienceNode />`,
      components: {
        AudienceNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("AudienceNode", nodeData.node, true);
};

const getModelId = (data: any) => {
  const result = new Set();
  result.add("Customer:customer");
  return result;
};

const Audience = {
  type: "audience_receive",
  name: "人群包",
  shape: "AudienceNode",
  iconClass: "icon-user-info",
  color: "#ffffff",
  themebg: "#39BCC5",
  skippable: true,
  pannel: AudiencePannel,
  help: Help,
  registerNode,
  getModelId,
  auth: [
    "export_task_record"
  ]
};

export default Audience;
