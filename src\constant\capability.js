import i18n from "../locale";

export const capabilityType = [
    {
        type: "sms",
        router: "sms",
        // title: "短信",
        title: i18n.global.t("global.reach.sms"),
    },
    {
        type: "email",
        router: "email",
        // title: "邮件",
        title: i18n.global.t("global.reach.email"),
    },
    {
        type: "wechat",
        router: "wechat",
        // title: "微信公众号",
        title: i18n.global.t("global.reach.weChatOfficialAccount"),
    },
    // {
    //     type: "wechat_mini",
    //     router: "wechat_mini",
    //     title: "微信小程序",
    // },
    // {
    //     type: "wecom",
    //     router: "wecom",
    //     title: "企业微信",
    // },
    // {
    //     type: "common",
    //     router: "common",
    //     title: "通用沟通渠道",
    // },
    // {
    //     type: "wechat_temp_msg",
    //     router: "wechatTemplate",
    //     title: "微信公众号模板消息",
    // },
    // {
    //     type: "wechat_customer_msg",
    //     router: "wechatCustomer",
    //     title: "微信公众号客服消息",
    // },
    // {
    //     type: "wechat_mass_msg",
    //     router: "wechatMass",
    //     title: "微信公众号群发消息",
    // },
    // {
    //     type: "APP",
    //     router: "APP",
    //     title: "APP",
    // },
];
export const wechatMsgTypes = [
    // { id: "template", name: "模板消息" },
    // { id: "customer", name: "客服消息" },
    // { id: "mass", name: "群发消息" }
    { id: "template", name: i18n.global.t("global.reach.templateMessage"), },
    { id: "customer", name: i18n.global.t("global.reach.customerServiceMessage"), },
    { id: "mass", name: i18n.global.t("global.reach.broadcastMessage"), }
];

export const capabilityStatus = [
    {
        // label: "启用",
        label: i18n.global.t("global.button.enable"),
        value: "ENABLED"
    },
    {
        // label: "禁用",
        label: i18n.global.t("global.button.disable"),
        value: "DISABLED"
    }
]
