/**
 * 活动管理接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function createCampaign(info: any) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/campaign`, info);
}

export function createCampaignByTemplate(templateId: string, info: any) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/campaign/template/${templateId}`, info);
}


export function saveAsCampaign(id: string, params?: Params) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/${id}/clone`,
    null,
    {
      params: {
        ...params
      }
    }
  );
}

export function getCampaignPage(query?: QueryInfo) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign/view`, {
    params: {
      ...query
    }
  });
}

export function modifyCampaign(info: any) {
  return axios.put(`/api/ma-manage/${tenantId}/${buCode}/campaign`, info);
}

export function modifyCampaignFlow(info: any) {
  return axios.put(`/api/ma-manage/${tenantId}/${buCode}/campaign/flow`, info);
}

export function deleteCampaign(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/campaign/${id}`);
}

export function findCampaignPage(query?: QueryInfo) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign/view`, {
    params: {
      ...query
    }
  });
}

export function findCampaignList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign/list`, {
    params
  });
}

export function getCampaign(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign/${id}`);
}

export function commitCampaign(info: any) {
  return axios.put(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/commit`,
    info
  );
}

export function saveCampaignList(groupId: string, info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/campaign/order/${groupId}`;
  return axios.post(uri, info);
}

export function approveCampaign(info: any) {
  return axios.put(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/approve`,
    info
  );
}

export function startCampaign(id: string) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/${id}/start`
  );
}

export function stopCampaign(id: string) {
  return axios.put(`/api/ma-manage/${tenantId}/${buCode}/campaign/${id}/stop`);
}

export function pauseCampaign(id: string) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/${id}/pause`
  );
}

export function resumeCampaign(id: string) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/${id}/resume`
  );
}

export function getRecordList(query?: QueryInfo) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/opt-log/view`, {
    params: {
      ...query
    }
  });
}

export function getRecordViewList(query?: QueryInfo) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/opt-log/view`, {
    params: {
      ...query
    }
  });
}

export function getRecordViewId(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/opt-log/view/${id}`);
}

export function createTemplate(info: any) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/campaign-template`, info);
}

export function getTemplateList() {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/campaign-template/list`
  );
}

// Flow代理接口
export function findFlowPage(params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/flow-proxy/publish/flow`,
    {
      params
    }
  );
}

export function getFlow(id?: string) {
  const params = {
    fields: "name",
    expression: `id eq ${id} AND status eq ENABLED`
  };
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/flow-proxy/publish/flow/list`,
    {
      params
    }
  );
}

export function findFlowList(params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/flow-proxy/publish/flow/list`,
    {
      params
    }
  );
}

export function findFlowStarts(id: string) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/flow-proxy/publish/flow/${id}/starts`
  );
}

export function findFlowEndModel(id: string) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/flow-proxy/publish/flow/${id}/model/end`
  );
}

// 活动流程接口
export function flowValidate(id: string, info: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/${id}/validate`,
    info
  );
}

export function getFlowItem(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow/${id}`);
}

export function queryFlowInstance(params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/instance/query`,
    {
      params
    }
  );
}

export function getFlowModelsByIds(modelIds?: Array<string>) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/campaign/model`;
  return axios.post(uri, modelIds);
}

export function getFlowModelsByIds2(modelIds?: Array<string>) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/campaign/flow-model`;
  return axios.post(uri, modelIds);
}

export function simulateCampaign(info: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/debug/start`,
    info
  );
}

export function flowStepOver(request: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/debug/step-over`,
    request
  );
}

export function flowStopDebug(instId: string) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/debug/stop/${instId}`
  );
}

export function getDebugRecord(instId: string) {
  const params = { instId };
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/debug/facts/agg`,
    {
      params
    }
  );
}

export function getTaskFactPage(query?: QueryInfo) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign/debug/facts`, {
    params: {
      ...query
    }
  });
}

export function retryTaskCustomer(recordId: string) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/retry-task/${recordId}`
  );
}

export function retryTask(info: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/campaign/retry-task`,
    info,
    { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
  );
}

export function runAppendJob(info: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/audience/flow/append`,
    info
  );
}

export function findDependentPage(query?: QueryInfo) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/dependent`, {
    params: {
      ...query
    }
  });
}
