export default {
  "global.button.sync": 'Sync',
  "global.button.create": 'Create',
  "global.button.edit": 'Edit',
  "global.button.delete": 'Delete',
  "global.button.update": 'Update',
  "global.button.submit": 'Submit',
  "global.button.cancel": 'Cancel',
  "global.button.save": 'Save',
  "global.button.confirm": 'Confirm',
  "global.button.back": 'Back',
  "global.button.next": 'Next',
  "global.button.previous": 'Previous',
  "global.button.search": 'Search',
  "global.button.refresh": 'Refresh',
  "global.button.upload": 'Upload',
  "global.button.download": 'Download',
  "global.button.close": 'Close',
  "global.button.apply": 'Apply',
  "global.button.reset": 'Reset',
  "global.button.add": 'Add',
  "global.button.view": 'View',
  "global.button.more": 'More',
  "global.button.filter": 'Filter',
  "global.button.sort": 'Sort',
  "global.button.export": 'Export',
  "global.button.import": 'Import',
  "global.button.query": 'Query',
  "global.button.disable": 'Disable',
  "global.button.enable": 'Enable',
  "global.button.operation": 'Operation',
  "global.button.expand": 'Expand',
  "global.button.retract": 'Retract',
  "global.button.list": 'List',
  "global.button.yes": 'Yes',
  "global.button.no": 'No',
  "global.button.card": 'Card',
  "global.button.group": 'Group',
  "global.button.baseInfo": "Basic Info",
  "global.button.and": 'AND',
  "global.button.or": 'OR',
  "global.button.saveTemplate": 'Save Template',

  "global.button.design": "Design",
  "global.button.approval": "Approval",
  "global.button.start": "Start",
  "global.button.pause": "Pause",
  "global.button.continueAction": "Continue",
  "global.button.stop": "Stop",
  "global.button.saveAs": "Save As",
  "global.button.operationRecord": "Operation Record",
  "global.button.report": "Report",

  "global.tips.success.save": 'Save Success',
  "global.tips.success.sync": 'Sync Success',
  "global.tips.success.edit": 'Edit Success',
  "global.tips.success.export": 'Export Success',
  "global.tips.success.import": 'Import Success',
  "global.tips.success.apply": 'Apply Success',
  "global.tips.success.delete": 'Delete Success',
  "global.tips.success.upload": 'Upload Success',
  "global.tips.success.download": 'Download Success',
  "global.tips.success.submit": 'Submit Success',
  "global.tips.success.creacreateActivityMonitoringAudienceTaskSuccess": "Successfully created activity monitoring audience task, you can view it in task management",
  "global.tips.success.reportDataExport": "_Export_Report_Data",

  "global.tips.success.createFullCommunicationReportExportTaskSuccess": "Successfully created full communication report data export task, you can view it in task management",
  "global.tips.success.fullCommunicationReportDataExport": "Full Communication Report Data Export",

  "global.tips.error.permission": 'No Permission',
  "global.tips.error.systemPrompt": "System Prompt",
  "global.tips.error.permissionException": "Permission Exception",
  "global.tips.error.loginTimeoutOrNoPermission": "Login timeout or no permission, please log in again or apply for permission.",
  "global.tips.error.resourceException": "Resource Exception",
  "global.tips.error.systemException": "System Exception",
  "global.tips.error.networkErrorOrServerBusy": "Network connection error or server is busy, please try again later...",

  "global.tips.warning.delete": 'After deletion, the data cannot be recovered. Please confirm whether to delete it?',

  "editTemplate.willProceed": 'You will proceed',
  "editTemplate.willSubmit":'You will submit, are you sure?',
  "editTemplate.willSave":'You will save, are you sure?',
  "editTemplate.isConfirm": 'yes',

  "global.total": "Total",

  "global.abtest.conversion": "Conversion",
  "global.abtest.revenue": "Revenue",
  "global.abtest.engagement": "Engagement",
  "global.abtest.up": "Improve",
  "global.abtest.down": "Decrease",
  "global.abtest.upDays": "Consecutive Days Improvement",
  "global.abtest.downDays": "Consecutive Days Decrease",
  "editTemplate.yes":'yes',

  "menu.flow.template":'',

  "global.task.status.submitted": 'Submitted',
  "global.task.status.ready": 'Ready',
  "global.task.status.success": 'Successful',
  "global.task.status.running": 'Running',
  "global.task.status.error": 'Failed',
  "global.task.status.expired": 'Expired',

  "global.task.type.audienceComparisonTask": 'Audience Comparison Task',
  "global.task.type.audienceDeliveryLimitationVerificationTask": 'Audience Delivery Limitation Verification Task',
  "global.task.type.activityMonitoringTask": 'Activity Monitoring Task',
  "global.task.type.activityAudienceAppendTask": 'Activity Audience Append Task',
  "global.task.type.exportProcessNodeRecord": 'Export Process Node Records',
  "global.task.type.exportProcessTouchRecord": 'Export Process Touch Records',
  "global.task.type.exportActivityReportRecord": 'Export Activity Report Records',
  "global.task.type.exportProcessNodeTouchRecord": 'Export Process Node Touch Records',

  "global.task.audience.calculateAudience": "Calculate Audience",
  "global.task.audience.importAudience": "Import Audience",
  "global.task.audience.testAudience": "Test Audience",
  "global.task.audience.realTrigger": "Real Trigger",
  "global.task.audience.select": "Please Select",

  "global.task.campaign.audiencePackage": "Audience Package",
  "global.task.campaign.event": "Event",
  "global.task.campaign.flow": "Flow",
  "global.task.campaign.acquisition": "Acquisition",
  "global.task.campaign.activation": "Activation",
  "global.task.campaign.retention": "Retention",
  "global.task.campaign.draft": "Draft",
  "global.task.campaign.submitted": "Submitted",
  "global.task.campaign.approved": "Approved",
  "global.task.campaign.rejected": "Rejected",
  "global.task.campaign.stopped": "Stopped",
  "global.task.campaign.running": "Running",
  "global.task.campaign.paused": "Paused",
  "global.task.campaign.finished": "Finished",
  "global.task.campaign.error": "Error",

  "global.reach.status.sending": "Sending",
  "global.reach.status.sendSuccess": "Send Successful",
  "global.reach.status.restricted": "Restricted",
  "global.reach.status.sendError": "Send Error",
  "global.reach.status.postponeExecution": "Postpone Execution",
  "global.reach.status.ignore": "Ignore",
  "global.reach.status.received": "Received",
  "global.reach.status.accepted": "Accepted",
  "global.reach.status.rejected": "Rejected",
  "global.reach.status.rejectChannelMessages": "Reject Channel Messages",
  "global.reach.status.blacklist": "Blacklist",
  "global.reach.status.touchTimeout": "Touch Timeout",

  "global.flow.none": "None",
  "global.flow.sms": "SMS",
  "global.flow.mms": "MMS",
  "global.flow.email": "Email",
  "global.flow.weChatOfficialAccount": "WeChat Official Account",
  "global.flow.templateMessage": "Template Message",
  "global.flow.customerServiceMessage": "Customer Service Message",
  "global.flow.broadcastMessage": "Broadcast Message",

  "global.flow.receive": "Receive",
  "global.flow.success": "Success",
  "global.flow.failure": "Failure",
  "global.flow.identityFieldAssociation": "Identity Field Association",
  "global.flow.queryConditionAssociation": "Query Condition Association",

}
