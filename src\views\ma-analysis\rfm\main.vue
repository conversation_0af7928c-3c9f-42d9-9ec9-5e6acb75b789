/** *by:<EMAIL> on 2022/8/14 0004 */
<template>
  <module view>
    <template #main>
      <a-select v-if="selectData.length > 0" v-model="id" style="margin: 20px" :style="{ width: '320px' }"
        placeholder="选择RFM分析数据" @change="onChangeRfm">
        <a-option v-for="item in selectData" :key="item.id" :value="item.id">{{item.name}}</a-option>
      </a-select>
      <MaBi :id="id" ref="maBiRef" :is-get-data="false" :is-filter="false" />
    </template>
  </module>
</template>

<script>
import { ref, onMounted, provide } from "vue";
import MaBi from "@/components/bi/chart/index.vue";
import { useRoute } from "vue-router";
import { useBussinessUnitStore } from "@/store";
import { findAnalysisRFMModelList } from "@/api/system";

export default {
  name: "TemplateStatement",
  components: {
    MaBi,
  },
  setup() {
    const route = useRoute();
    const maBiRef = ref(null);
    const userTc = useBussinessUnitStore();
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: "报表分析",
          path: "/analysis/analysis",
        },
        {
          name: route.query.title,
        },
      ],
      mainPath: "/analysis/analysis",
    });

    const buCode = userTc.currentBussinessUnit?.code;
    const tenantId = userTc.currentBussinessUnit?.tenantId;
    const id = ref("");
    if (route.query.id) {
      id.value = `${tenantId}_${buCode}_${route.query.id}`;
    }
    // id列表
    const selectData = ref([]);
    onMounted(async () => {
      // 报表类型
      if (route.query?.type === "one") {
        maBiRef.value.onGetMaDataList(id.value);
      } else if (route.query?.type === "rfm") {
        const { content } = await findAnalysisRFMModelList({
          page: 0,
          size: 10,
          total: 0,
          showPageSize: true,
        });
        selectData.value = content;
        id.value = content[0].id;
        maBiRef.value.onGetMaDataList(id.value);
      } else {
        maBiRef.value.getItemReport();
      }
    });

    // 选择rfm
    const onChangeRfm = () => {
      maBiRef.value.onGetMaDataList(id.value);
    };

    const setup = {
      id,
      maBiRef,
      module,
      selectData,
      onChangeRfm,
    };

    provide("view", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.dashboard-home {}
</style>
