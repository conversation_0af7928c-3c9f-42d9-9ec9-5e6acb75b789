<template>
  <div class="easyflow-pannel-extend-data">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="定制功能">
        <a-select v-model="entity.handleId" class="easyflow-select" placeholder="请选择定制功能" :loading="loading"
          :filter-option="false">
          <a-option>查找客户绑定BA</a-option>
          <a-option>查找90天内过期积分</a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, watch } from 'vue'

const props = defineProps(["node", "easyflow"]);
const { node, easyflow } = props;
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const entity = ref({
  queryContent: ""
});
const templateData = ref([]);


const save = () => {
  return entity.value;
};

defineExpose({
  save,
});

onMounted(() => {
  Object.assign(entity.value, node.data);
});
</script>


<style lang="less" scoped></style>
