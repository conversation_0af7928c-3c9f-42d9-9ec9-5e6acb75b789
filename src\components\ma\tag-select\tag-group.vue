<template>
  <div class="tag-group">
    <a-collapse-item :key="groupIndex" :header="header">
      <template #extra>
        <a-button type="outline" size="mini" @click.stop="changeData(currentTags)">编辑</a-button>
      </template>
      <div class="alert">
        <icon-exclamation-circle-fill size="16" /> {{ description }}
      </div>
      <template v-for="(item, index) in currentTags" :key="item">
        <a-tag color="arcoblue" class="tag">{{ item.label }}</a-tag>
        <a-tag v-if="index !== currentTags.length - 1" size="small" class="tag" bordered>{{ connector }}</a-tag>
      </template>
    </a-collapse-item>
    <TagSelectDlg ref="tagSelectDlgRef" />
  </div>
</template>

<script setup>
import { ref, inject } from "vue";
import TagSelectDlg from "./tag-select-dlg.vue";

const editInject = inject("edit");

const props = defineProps({
  groupIndex: Number,
  header: String,
  isEdit: Boolean,
  connector: String,
  description: String,
  allTags: Array,
  selectedTags: Array,
  tagType: String,
});

// 选择条件
const tagSelectDlgRef = ref(null);

const currentTags = ref([]);
currentTags.value = props.allTags.filter((item) =>
  props.selectedTags?.includes(item.value)
);

const changeData = async (tags) => {
  const res = await tagSelectDlgRef.value.show(
    props.tagType,
    tags.map((it) => it.value)
  );
  if (res !== undefined) {
    currentTags.value = res;
    editInject.changeTags(
      props.tagType,
      res.map((it) => it.value)
    );
  }
};
</script>

<style lang="less" scoped>
.tag-group {
  .tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }

  .alert {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #999999;
    font-size: 12px;
  }
}
</style>
