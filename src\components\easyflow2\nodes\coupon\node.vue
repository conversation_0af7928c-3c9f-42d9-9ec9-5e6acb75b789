<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { findBenefitItem } from "@/api/benefit";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (data.capabilityId) {
        const benefit = await findBenefitItem(data.capabilityId);
        brief.push({ sort: 0, label: "优惠券模板", value: benefit.name });
      }
      if (data.budgetSetting && data.budgetSetting.enabled) {
        brief.push({
          sort: 1,
          label: "单价",
          value: data.budgetSetting.budgetValue,
        });
      }
      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    const item = nodeApi.find(item => { return item.type === 'coupon' })
    const setup = {
      title: item.name || "优惠券",
      summary: item.name || "优惠券节点",
      iconClass: item.icon || "icon--coupon-2",
      nodeClass: "easyflow-node-benefit",
      headerColor: item.themeColor || "#85c0fa",
      headerBgColor: item.themeColor || "#4594f3",
      background: item.background || "#f5faff",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
