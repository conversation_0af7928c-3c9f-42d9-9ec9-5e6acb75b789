<template>
  <a-modal v-model:visible="visible" @before-ok="handleOk">
    <template #title>追加人群</template>
    <a-form :model="entity">
      <a-form-item label="追加方式" :extra="typeExtra[entity.payload.appendType]">
        <a-select v-model="entity.payload.appendType" placeholder="请选择追加方式">
          <a-option value="audience" label="选择人群" />
          <a-option value="csv" label="CSV文件导入" />
          <a-option value="json" label="JSON文件导入" />
        </a-select>
      </a-form-item>
      <a-form-item v-if="entity.payload.appendType === 'audience'" label="追加人群">
        <a-select v-model="entity.payload.audienceId" :allow-clear="true" placeholder="请选择人群" allow-search
          @change="handleEstimateAppendAudience(entity.payload.audienceId)">
          <a-option v-for="item of audiences" :key="item.id" :label="item.name" :value="item.id">
            <template #label>
              <div class="between-option">
                <span>{{ item.name }}</span>
                <span class="usage-type">{{ item.usageType }}</span>
              </div>
            </template>
          </a-option>
        </a-select>
        <template #extra>
          <div class="estimate-number">
            <span>预估人数<i>{{ appendAudienceEstimate !== "" ? appendAudienceEstimate : "--" }}</i>人</span>
          </div>
        </template>
      </a-form-item>
      <a-form-item v-if="entity.payload.appendType === 'csv' || entity.payload.appendType === 'json'" label="文件导入">
        <a-upload :accept="filterAccept(entity.payload.appendType)" :limit="1" :action="uploadUrl"
          @success="successUploadFile" />
      </a-form-item>
      <a-form-item v-if="entity.payload.appendType === 'csv' || entity.payload.appendType === 'json'" label="客户映射字段">
        <a-tree-select v-model="entity.payload.identifyField" :data="customerModelFields" :field-names="{
    key: 'path',
    value: 'path',
    title: 'aliasName',
    children: 'fields',
  }" placeholder="请选择映射字段" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { queryFlowInstance } from "@/api/campaign";
import { findAudienceList, countAudience } from "@/api/audience";
import { saveTask2, startTask } from "@/api/task";
import { useBussinessUnitStore, useUserStore } from "@/store";
import { findCustomerModel } from "@/api/system";
import { formatFields } from "@/utils/field";
import { uuid } from "@/utils/uuid";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;
const userStore = useUserStore();
const userInfo = computed(() => {
  return userStore.userAuthInfo;
});

const uploadUrl = `/api/ma-manage/${tenantId}/${buCode}/file/upload?path=append_audience`;

const { flowId, flowName, instanceId, taskId, modelId } = defineProps([
  "flowId",
  "flowName",
  "instanceId",
  "taskId",
  "modelId"
]);

const visible = ref(false);
const audiences = ref(null); // 人群列表
const appendAudienceEstimate = ref(0); // 预估人数
const customerModelFields = ref([]);

const entity = ref({
  name: `活动: ${flowName}-追加人群任务`,
  type: "flow_append",
  status: "SUBMITTED",
  userId: userInfo.value.username,

  flowSetting: {
    // audienceId: entity.value.appendAudienceId,
    flowId,
    instanceId,
    taskId,
  },
  payload: {},
  files: [],
  executeMode: {
    type: "Manual",
  },
});

const typeExtra = ref({
  "audience": "",
  "csv": "导入文件首行是字段名, 且只接受第一列作为导入数据。",
  "json": "导入文件需要按照标准JSON格式, 即包含\"[]\"和逗号。 客户映射字段用于标记客户主键。"
});
const filterAccept = (type) => {
  switch (type) {
    case 'csv':
      return 'text/csv';
    case 'json':
      return 'application/json';
    default:
      return '';
  }
};

const handleSearchAudience = async (value) => {
  const params = { fields: "name,usageType", expression: "usageType ne TEST" };
  if (value) {
    params.expression = `AND name like ${value}`;
  }
  audiences.value = await findAudienceList(params);
};

const handleEstimateAppendAudience = async (audienceId) => {
  // 查询前判断是否还存在分组
  const item = audiences.value.find((x) => {
    return x.id === audienceId;
  });
  if (!item) {
    entity.value.appendAudienceId = "";
    appendAudienceEstimate.value = "";
    return false;
  }
  appendAudienceEstimate.value = await countAudience(audienceId);
};

const successUploadFile = (res) => {
  if (entity.value.files.length === 0) {
    entity.value.files.push({});
  }
  entity.value.files[0] = {
    fileName: res.response.filePath,
    size: res.response.size,
  };
};

const show = async () => {
  const data = await findCustomerModel();
  if (data) {
    customerModelFields.value = formatFields(data.fields);
  }
  visible.value = true;
  await handleSearchAudience();
};

const handleOk = async () => {
  try {
    const instances = await queryFlowInstance({expression : `flowId eq ${flowId} AND context.executeSetting.engineType eq STANDARD`});
    if(instances.size < 1){
      Message.success("活动状态错误");
      return;
    }
    const instance = instances[0];
    entity.value.id = `${instance.id}_${entity.value.flowSetting.taskId}_${uuid(6)}`;
    entity.value.flowSetting.instanceId = instance.id;
    entity.value.timestamp = new Date();
    entity.value.payload.model = modelId;
    const res = await saveTask2(entity.value);
    await startTask(res.id);
    Message.success("人群追加任务启动,可在任务管理查看运行状态");
    visible.value = false;
  } catch (error) {
    console.error(error);
  }
};

defineExpose({ show });
</script>

<style lang="less" scoped></style>
