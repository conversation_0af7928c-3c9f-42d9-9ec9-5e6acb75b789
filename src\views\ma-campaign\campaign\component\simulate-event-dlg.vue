<template>
  <a-modal v-model:visible="modelVisible" title="模拟事件" @before-ok="handleOk" @before-cancel="handleCancel">
    <a-form ref="dataFormRef" :model="dataForm">
      <a-form-item label="事件">
        <a-select v-model="selectedBehavior" placeholder="请选择事件" :loading="loading" :filter-option="false"
          :show-extra-options="false" @change="changeBehavior">
          <a-option v-for="item of behaviorNodes" :key="item.data.behaviorId"
            :label="item.data._name + ' : ' + item.data.eventName" :value="item"></a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="客户" field="customer" :rules="[{ required: true, message: '客户不能为空' }]">
        <a-select v-model="dataForm.customer" allow-search :allow-clear="true" :filter-option="false"
          :show-extra-options="false" placeholder="请输入手机号搜索" @search="customerSearch">
          <a-option v-for="item of customers" :key="item.id"
            :label="mobileValue + ' ( ' + getCustomerName(item.payload) + ' )'" :value="item.id" />
        </a-select>
      </a-form-item>
      <a-form-item label="事件时间" field="timestamp" :rules="[{ required: true, message: '事件时间不能为空' }]">
        <a-date-picker v-model="dataForm.timestamp" :show-time="true" value-format="YYYY-MM-DDTHH:mm:ss.000ZZ"
          class="form-date" />
      </a-form-item>
      <a-form-item label="事件报文" field="payload">
        <a-textarea v-model="payloadContent" :auto-size="{ minRows: 2, maxRows: 5 }" placeholder="请输入JSON格式的事件报文" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { modalCommit } from "@/utils/modal";
import {
  sendSimulateEvent,
  findBehaviorModelSample
} from "@/api/behavior";
import { findCustomerByPurpose } from "@/api/audience";

let callback;
const modelVisible = ref(false);
const dataForm = ref({
  timestamp: new Date(),
});
const dataFormRef = ref({});
const payloadContent = ref("");
const loading = ref(false);
const customers = ref([]);
const mobileValue = ref("");
const behaviorNodes = ref([]);
const selectedBehavior = ref(null);

const changeBehavior = async () => {
  if (selectedBehavior.value) {
    dataForm.value.code = selectedBehavior.value.behaviorId;
    dataForm.value.taskId = selectedBehavior.value.id;
    payloadContent.value = JSON.stringify(await findBehaviorModelSample(selectedBehavior.value.data.behaviorModelId), null, 2);
  }
}

const handleOk = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    if (payloadContent.value) {
      dataForm.value.payload = JSON.parse(payloadContent.value);
    }
    await sendSimulateEvent(dataForm.value);

    callback(dataForm.value);
  });
};

const handleCancel = async () => {
  callback();
};

const customerSearch = async (value) => {
  const page = await findCustomerByPurpose("mobile", value);
  customers.value = page.content;
  mobileValue.value = value
};

const getCustomerName = (payload) => {
  return payload.name !== undefined ? payload.name : payload.nickname;
}

const show = async (params) => {
  modelVisible.value = true;
  behaviorNodes.value = params.behaviorNodes;

  dataForm.value.flowId = params.flowId;
  dataForm.value.instanceId = params.instanceId;
  return new Promise((resolve, reject) => {
    callback = resolve;
  });
};

defineExpose({ show });
</script>

<style lang="less" scoped></style>
