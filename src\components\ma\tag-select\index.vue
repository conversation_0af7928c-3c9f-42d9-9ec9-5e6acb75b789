/** * 标签过滤组件 */
<template>
  <div v-if="completed" class="tag-selection">
    <div class="or-change-and"><span>{{t('audience.bool.and')}}</span></div>
    <a-collapse :default-active-key="[1, 2, 3]">
      <TagGroup :group-index="1" :header="t('audience.edit.requiredTag')" :connector="t('audience.bool.and')" :is-edit="isEdit" :all-tags="allTags"
        :selected-tags="filter.requiredTags" tag-type="requiredTags" :description="t('audience.reminder.allTags')" />
      <TagGroup :group-index="2" :header="t('audience.edit.optionalTag')" :connector="t('audience.bool.or')" :is-edit="isEdit" :all-tags="allTags"
        :selected-tags="filter.optionalTags" tag-type="optionalTags" :description="t('audience.reminder.atLeastOneTag')" />
      <TagGroup :group-index="3" :header="t('audience.edit.excludeTag')" :connector="t('audience.bool.or')" :is-edit="isEdit" :all-tags="allTags"
        :selected-tags="filter.excludedTags" tag-type="excludedTags" :description="t('audience.reminder.noTags')" />
    </a-collapse>
  </div>

  <!--  穿梭框  -->
  <TagSelectDlg ref="tagSelectDlgRef" />
</template>

<script>
import { defineComponent, ref, onMounted, getCurrentInstance } from "vue";
import { findTagList } from "@/api/audience";
import TagSelectDlg from "./tag-select-dlg.vue";
import TagGroup from "./tag-group.vue";

export default defineComponent({
  name: "TagSelect",
  components: {
    TagGroup,
    TagSelectDlg,
  },
  props: {
    filter: Object,
    isEdit: {
      // 是否显示编辑
      type: Boolean,
      default: true,
    },
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const completed = ref(false);
    const tagSelectDlgRef = ref(null);
    const modelVisible = ref(false);
    const allTags = ref([]);

    onMounted(async () => {
      findTagList({}).then((res) => {
        allTags.value = res.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
        completed.value = true;
      });
    });
    return {
      t,
      completed,
      tagSelectDlgRef,
      modelVisible,
      allTags,
    };
  },
});
</script>

<style lang="less" scoped>
.tag-selection {
  position: relative;
  padding-left: 30px;

  .or-change-and {
    width: 25px;
    border: 2px solid rgb(var(--primary-6));
    border-right: 0;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    height: calc(100% + 10px);
    display: flex;
    align-items: center;
    position: absolute;
    top: -5px;
    left: 5px;
    bottom: -5px;

    span {
      border: 1px solid rgb(var(--primary-6));
      background-color: #ffffff;
      padding: 1px 3px;
      font-weight: bold;
      margin-left: -12px;
      font-size: 12px;
    }
  }
}
</style>
