<template>
  <module main>
    <template #filter></template>
    <template #search></template>
    <template #action><a-button v-permission="['ma_menu.job.delete']" type="primary" @click="deleteBatch">{{t('global.button.delete')}}</a-button></template>
    <template #context><span></span></template>
    <template #main>
      <a-table ref="table" v-model:selectedKeys="selectedKeys" row-key="id" :row-selection="rowSelection"
        :bordered="false" :data="dataSource" :pagination="false">
        <template #columns>
          <a-table-column :title="t('task.taskManagement')" data-index="name"> </a-table-column>
          <a-table-column :title="t('task.taskType')" data-index="type">
            <template #cell="{ record }">
              {{ filters(taskTypes, record.type) }}
            </template>
          </a-table-column>
          <a-table-column :title="t('task.taskStatus')" data-index="status">
            <template #cell="{ record }">
              <a-tag :color="filters(taskStatus, record.status, 'color')" size="mini" :status="record.status">{{
                filters(taskStatus, record.status) }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column :title="t('task.creationTime')" data-index="createTime" :sortable="{ sortDirections: ['ascend', 'descend'] }">
            <template #cell="{ record }">
              {{ $moment(record.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </a-table-column>
          <a-table-column :title="t('task.action')" align="right" :width="260">
            <template #cell="{ record }">
              <!-- <a-button v-if="record.type == 'audience_compare'" :disabled="record.status != 'SUCCESS'" type="text"
                size="small" @click="openViewPage(record.id)">预览
              </a-button> -->
              <a-button  v-if="record.type != 'capability_limit_check' && record.type != 'flow_append'" v-permission="['ma_menu.job.file']"
                :disabled="record.status != 'SUCCESS'" type="text" size="small" @click="detail(record.files)">{{t('task.fileList')}}
              </a-button>
              <a-button v-if="record.type == 'capability_limit_check'" :disabled="record.status != 'SUCCESS'" type="text"
                size="small" @click="taskDetail(record.payload)">任务详情
              </a-button>
              <a-button v-permission="['ma_menu.job.delete']" type="text" size="small" @click="deleteData(record.id)">{{t('global.button.delete')}}</a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>

      <!--  下载数据  -->
      <a-modal v-model:visible="listVisible" :footer="false">
        <template #title> {{t('task.fileList')}} </template>
        <a-table :data="listData" :pagination="false">
          <template #columns>
            <a-table-column :title="t('task.fileName')" data-index="fileName"></a-table-column>
            <a-table-column :title="t('task.fileSize')" data-index="size"></a-table-column>
            <a-table-column :title="t('task.fileCount')" data-index="count"></a-table-column>
            <a-table-column :title="t('task.action')">
              <template #cell="{ record }">
                <a :href="onDownloadItem(record.fileName)" target="_blank">
                  <a-button type="outline" size="mini">{{t('global.button.download')}}</a-button>
                </a>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-modal>

      <a-modal v-model:visible="detailVisible" :footer="false">
        <template #title> 任务详情 </template>
        <div v-for="item in listData" :key="item.title" class="task-item">
          {{ item.title }}<span>{{ item.num }}</span>
        </div>
      </a-modal>
    </template>
  </module>
</template>

<script>
import { ref, provide, computed, reactive, getCurrentInstance } from "vue";

import { useRouter } from "vue-router";
import { findPage, deleteItem, downloadItem, deleteItemBatch } from "@/api/task";
import { Modal } from "@arco-design/web-vue";
import { taskTypes, taskStatus } from "@/constant/task";
import { addExp } from "@/utils/common";
import { filters } from "@/utils/filter";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    // 路由API
    const router = useRouter();

    // 模块设置
    const module = ref({
      entityIdField: "id",
      // entityName: "任务管理",
      entityName: t('task.taskManagement'),
      breadcrumb: [
        {
          // name: "任务管理",
          name: t('task.taskManagement'),
        },
      ],
      viewPath: "/task/view",
    });

    const sort = ref("createTime,DESC");
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true,
    });

    // 过滤设置
    const filter = ref([
      {
        field: "type",
        // label: "任务类型",
        label: t('task.taskType'),
        component: "a-select",
        operate: "eq",
        dataSource: taskTypes,
        // placeholder: "请选择任务类型",
        placeholder: t('task.reminder.taskType'),
        value: "",
      },
      {
        field: "status",
        // label: "任务状态",
        label: t('task.taskStatus'),
        component: "a-select",
        operate: "eq",
        dataSource: taskStatus,
        placeholder: t('task.reminder.taskStatus'),
        value: "",
      },
    ]);

    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    const selectedKeys = ref([]);

    const rowSelection = reactive({
      type: 'checkbox',
      showCheckedAll: true,
      onlyCurrent: false,
    });

    // 查询API
    const bindData = async (expression) => {
      entity.value = await findPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
          sort: sort.value,
        },
        {
          expression: addExp(expression, "hidden eq false"),
          fields: "name,type,status,payload,files,createTime",
        }
      );
      pagination.value.total = entity.value.totalElements;
    };
    // 详情跳转
    const listVisible = ref(false);
    const detailVisible = ref(false);
    const listData = ref([]);
    const detail = (row) => {
      const list = [];
      row.forEach((item) => {
        list.push(item);
      });
      listData.value = list;
      listVisible.value = true;
    };

    const taskDetail = (row) => {
      const res = JSON.parse(row);
      listData.value = [
        {
          title: "触达人数",
          num: res.accessCount,
        },
        {
          title: "限制人数",
          num: res.limitedCount,
        },
      ];
      detailVisible.value = true;
    };

    // 新建跳转
    const create = () => router.push({ path: module.value.createPath });

    // 删除
    const deleteData = async (id) => {
      Modal.confirm({
        // title: "删除行为任务",
        title: t('task.popup.deleteTitle'),
        // content: "删除之后数据不可恢复，请确认是否删除?",
        content: t('global.tips.warning.delete'),
        onOk: async () => {
          await deleteItem(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page--;
          }
          await bindData();
        },
      });
    };

    const deleteBatch = async () => {
      Modal.confirm({
        // title: "删除行为任务",
        title: t('task.popup.deleteTitle'),
        // content: "删除之后数据不可恢复，请确认是否删除?",
        content: t('global.tips.warning.delete'),
        onOk: async () => {
          await deleteItemBatch(selectedKeys.value);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1;
          }
          await bindData();
        },
      });
    }

    // 下载数据
    const onDownloadItem = (name) => {
      return downloadItem(name);
    };

    // 跳转查看页
    const openViewPage = (id) => {
      router.push({ path: module.value.viewPath, query: { id } });
    };

    const setups = {
      t,
      module,
      filter,
      entity,
      detail,
      taskDetail,
      create,
      bindData,
      deleteData,
      dataSource,
      pagination,
      filters,
      taskTypes,
      taskStatus,
      listVisible,
      detailVisible,
      listData,
      onDownloadItem,
      openViewPage,
      selectedKeys,
      rowSelection,
      deleteBatch
    };
    provide("main", setups);
    return setups;
  },
};
</script>

<style lang="less" scoped>
.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #efefef;
  margin-bottom: 10px;
}
</style>
