<!-- TODO: 导入人群，页面需要重写 -->
<template>
  <module edit>
    <template #main>
      <div class="audience-edit">

      </div>
    </template>
  </module>
</template>

<script>
import { ref, provide, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

export default {
  setup() {
    const route = useRoute();
    const router = useRouter();

    const module = ref({
      entityIdField: "id",
      entityName: "导入人群",
      mainPath: "/audience/main",
    });


    const setup = {
      module,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style scoped lang="less">
.audience-edit {
  :deep(.arco-progress-type-circle) {
    display: none;
  }

  :deep(.arco-upload-icon-start) {
    display: none;
  }
}
</style>
