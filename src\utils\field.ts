import { clone } from "./common";

// 字段类型常量
export const FieldTypes = {
  STRING: "string",
  TEXT: "text",
  SHORT: "short",
  INTEGER: "integer",
  LONG: "long",
  FLOAT: "float",
  DOUBLE: "double",
  DECIMAL: "double",
  DATE: "date",
  BOOLEAN: "boolean",
  GEO_POINT: "geo_point",
  NESTED: "struct",
};
// 字段运算符常量
export const FieldOperators = {
  eq: "eq",
  ne: "ne",
  lt: "lt",
  le: "le",
  ge: "ge",
  gt: "gt",
};

/**
 * 判断字段是文本型
 * @param type
 * @returns
 */
export const isText = (type: string) => {
  return type === FieldTypes.TEXT;
};

/**
 * 判断字段是数字型
 * @param type
 * @returns
 */
export const isNumber = (type: string) => {
  return [
    FieldTypes.SHORT,
    FieldTypes.INTEGER,
    FieldTypes.LONG,
    FieldTypes.FLOAT,
    FieldTypes.DOUBLE,
    FieldTypes.DECIMAL,
  ].includes(type);
};

/**
 * 判断字段是布尔型
 * @param type
 * @returns
 */
export const isBoolean = (type: string) => {
  return type === FieldTypes.BOOLEAN;
};

/**
 * 判断字段是日期型
 * @param type
 * @returns
 */
export const isDate = (type: string) => {
  return type === FieldTypes.DATE;
};

/**
 * 判断字段是结构型
 * @param type
 * @returns
 */
export const isNested = (type: string) => {
  return type === FieldTypes.NESTED;
};

/**
 * 判断字段可比较大小
 * @param type
 * @returns
 */
export const comparable = (type: string) => {
  return [
    FieldTypes.SHORT,
    FieldTypes.INTEGER,
    FieldTypes.LONG,
    FieldTypes.FLOAT,
    FieldTypes.DOUBLE,
    FieldTypes.DECIMAL,
    FieldTypes.DATE,
  ].includes(type);
};

export const getFieldByName = (fields: Array<any>, name: string) => {
  return fields.find((it) => it.name === name);
};

export const getFieldBySegments = (field: any, segs: Array<string>) : any => {
  if (segs.length === 0) return field;
  if (field == null) return [];
  const findFields = getFieldByName(field.fields, segs[0]);
  segs.splice(0, 1);
  return getFieldBySegments(findFields, segs);
};

export const getFieldByPath = (fields: Array<any>, path: string) => {
  if(!path) return null;
  const segs = path.split(".");
  const findField = getFieldByName(fields, segs[0]);
  segs.splice(0, 1);
  return getFieldBySegments(findField, segs);
};

// 初始化数据模型字段，增加path和显示名称
export const formatFields = (fields: Array<any>, parent: any) => {
  const result: any[] = [];
  if (!Array.isArray(fields)) return result;
  fields.forEach((item) => {
    const field = clone(item);
    if (parent) {
      field.path = `${parent.path}.${item.name}`;
    } else {
      field.path = item.name;
    }
    if (item.aliasName) {
      field.label = `「${item.aliasName}」${item.name}`;
    } else {
      field.label = `${item.name}`;
    }

    if (item.fields && item.fields.length > 0) {
      field.fields = formatFields(item.fields, field);
    }
    result.push(field);
  });
  return result;
};
