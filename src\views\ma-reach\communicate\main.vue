<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <a-button v-permission="['ma_menu.communicate.create']" type="primary" @click="showSelectDlg">
          <template #icon>
            <icon-plus />
          </template>
          新建
        </a-button>
      </template>
      <template #context><span></span></template>
      <template #main>
        <a-table ref="table" :bordered="false" :data="dataSource" :pagination="false" @page-change="onPageChange">
          <template #columns>
            <a-table-column title="名称" data-index="name" :sortable="{ sortDirections: ['ascend', 'descend'] }"
              :width="200" />
            <a-table-column title="类型" data-index="type" :sortable="{ sortDirections: ['ascend', 'descend'] }"
              :width="100">
              <template #cell="{ record }">
                {{ typeFilter(record.type) }}
              </template>
            </a-table-column>
            <a-table-column title="渠道" data-index="status" :sortable="{ sortDirections: ['ascend', 'descend'] }"
              :width="150">
              <template #cell="{ record }">
                {{ channelFilter(record.channelId) }}
              </template>
            </a-table-column>
            <a-table-column title="状态" data-index="status" :sortable="{ sortDirections: ['ascend', 'descend'] }"
              :width="90">
              <template #cell="{ record }">
                {{ statusFilter(record.status) }}
              </template>
            </a-table-column>
            <a-table-column title="摘要" data-index="summary" :ellipsis="true" :tooltip="{class:'tooltip-content'}" />
            <a-table-column title="创建时间" data-index="createTime" :sortable="{ sortDirections: ['ascend', 'descend'] }"
              :width="180">
              <template #cell="{ record }">
                {{ $moment(record.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </a-table-column>
            <a-table-column title="操作" align="center" :width="160">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.communicate.modify']" type="text" size="small" @click="detail(record)">编辑</a-button>
                <!-- <a-button type="text" size="small" @click="toStatement(record.id)">报表</a-button> -->
                <a-button v-permission="['ma_menu.communicate.delete']" type="text" size="small" @click="deleteData(record)">删除</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>

        <capabilitySelectorDlg ref="csDlgRef" :prefix-path="module.editPath" :model-data="modelData" />
      </template>
    </module>
  </div>
</template>

<script>
import { ref, provide, computed, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import Pagination from "@/utils/pagination";
import { findCommunicatePage, deleteCommunicateItem, findChannelList } from "@/api/communicate";
import { Modal } from "@arco-design/web-vue";
import { filters, convertFilter } from "@/utils/filter";
import { addExp } from "@/utils/common";
import { capabilityType, capabilityStatus } from "@/constant/capability";
import CapabilitySelectorDlg from "@/components/modal-dlg/capability-selector-dlg.vue";
import reference from "@/components/reference/index.vue"

export default {
  components: {
    CapabilitySelectorDlg,
    reference
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    // 路由API
    const router = useRouter();
    // 模块设置
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: "触达模板管理"
        }
      ],
      editPath: "/reach/communicate/",
      statementPath: "/reach/communicate/statement"
    });
    const modelData = ref([
      // { title: "微信", type: "wechat" },
      { title: "短信", type: "sms" },
      { title: "邮件", type: "email" }
    ]);

    // 分页设置
    const pagination = ref(Pagination);

    // 过滤设置
    const filter = ref([
      {
        field: "name",
        label: "沟通模板名称",
        component: "a-input",
        operate: "like",
        placeholder: "请输入沟通名称",
        comment: true,
        value: ""
      },
      {
        field: "type",
        label: "沟通模板类型",
        component: "a-select",
        operate: "eq",
        dataSource: convertFilter(capabilityType, "title", "router"),
        placeholder: "请选择数据源",
        value: ""
      },
      {
        field: "status",
        label: "状态",
        component: "a-select",
        operate: "eq",
        dataSource: [
          { value: "ENABLED", label: t("global.button.enable") },
          { value: "DISABLED", label: t("global.button.disable") }
        ],
        placeholder: "请选择状态",
        value: ""
      }
    ]);

    // 数据设置
    const entity = ref({});
    const channelList = ref([]);

    const csDlgRef = ref(null);

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    const routerFilter = (v) => {
      return filters(capabilityType, v, "router", "type");
    };

    const typeFilter = (v) => {
      return filters(capabilityType, v, "title", "type", "-");
    };

    const statusFilter = (v) => {
      return filters(capabilityStatus, v);
    };

    // 查询API
    const bindData = async (expression) => {
      const realExpression = addExp(expression, "category eq reach");
      channelList.value = await findChannelList({ fields: "name" });
      entity.value = await findCommunicatePage(
        {
          ...pagination.value,
          sort: "createTime,DESC",
          page: pagination.value.page - 1
        },
        {
          expression: realExpression,
          fields: "name,code,status,type,createTime,channelId,summary"
        }
      );
      pagination.value.total = entity.value.totalElements;
    };

    // 详情跳转
    const detail = (row) => {
      const query = {};
      query[module.value.entityIdField] = row[module.value.entityIdField];
      router.push({
        path: module.value.editPath + routerFilter(row.type),
        query
      });
    };

    // 跳转报表
    const toStatement = (id) => {
      router.push({ path: module.value.statementPath, query: { id } });
    };

    // 新建弹窗
    const showSelectDlg = () => {
      csDlgRef.value.show();
    };

    const onPageChange = () => {
      entity.value = this.bindData();
    };

    const deleteData = async (record) => {
      Modal.confirm({
        title: "删除模板",
        content: "删除之后数据不可恢复，请确认是否删除?",
        onOk: async () => {
          await deleteCommunicateItem(record.id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page--;
          }
          await bindData();
        }
      });
    };

    const channelFilter = (id) => {
      const item = channelList.value.find((item) => {
        return item.id === id;
      });
      return item?.name || "";
    };

    const setup = {
      module,
      filter,
      entity,
      detail,
      bindData,
      csDlgRef,
      dataSource,
      pagination,
      typeFilter,
      statusFilter,
      deleteData,
      showSelectDlg,
      onPageChange,
      modelData,
      channelList,
      channelFilter,
      toStatement
    };
    provide("main", setup);
    return setup;
  }
};
</script>

<style scoped lang="less">
.arco-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.arco-radio {
  width: calc(50% - 10px);
  margin-bottom: 20px;

  &:nth-child(2n) {
    margin-right: 0;
  }
}
</style>
