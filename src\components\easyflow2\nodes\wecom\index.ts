import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import WecomNode from "./node.vue";
import WecomPannel from "./pannel.vue";

const nodeData = {
    type: "wecom",
    node: {
        inherit: "vue-shape",
        ...rect,
        component: {
            template: `<WecomNode />`,
            components: {
                WecomNode,
            },
        },
        ports: getPorts(true, true, true, true),
    },
};

const registerNode = () => {
    Graph.registerNode("WecomNode", nodeData.node, true);
};

const Wecom = {
    type: "wecom",
    name: "企微",
    shape: "WecomNode",
    iconClass: "icon-scrm-l",
    color: "#ffffff",
    themebg: "#4594f3",
    registerNode: registerNode,
    pannel: WecomPannel,
    skippable: true,
    auth: [
        "export_task_record"
    ]
};

export default Wecom;