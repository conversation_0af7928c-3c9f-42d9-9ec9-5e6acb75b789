<template>
  <template v-if="showGroupVisible">
    <a-modal v-model:visible="showGroupVisible"
             :title="dataForm.id ? $t('campaign.popup.edit_title') : $t('campaign.popup.create_title')"
             @cancel="showGroupVisible = false"
             @before-ok="addGroup">
      <a-form ref="dataFormRef"
              :model="dataForm">
        <a-form-item field="code"
                     :label="t('campaign.popup.code')"
                     :rules="[
                      // { required: true, message: '请输入活动编码' },
                      { required: true, message: $t('campaign.reminder.input_activity_code') },
                      {
                        match: /^[a-zA-Z][a-zA-Z0-9\-_]*$/,
                        // message: '活动编码只能输入大小写字母、数字和下划线',
                        message: $t('campaign.reminder.activity_code_format'),
                      },
                    ]"
                     :disabled="isEdit"
                     label-col-flex="70px">
          <a-input v-model="dataForm.code"
                   :placeholder="$t('campaign.reminder.input_activity_code')" />
        </a-form-item>
        <a-form-item field="name"
                     :label="$t('campaign.popup.name')"
                     :rules="[{ required: true, message: $t('campaign.reminder.input_activity_name') }]"
                     label-col-flex="70px">
          <a-input v-model="dataForm.name"
                   :placeholder="$t('campaign.reminder.input_activity_name')" />
        </a-form-item>
        <a-form-item field="dataRoles"
                     :label="$t('campaign.popup.dataRole')"
                     label-col-flex="70px">
          <!-- <a-select
            multiple
            :max-tag-count="2"
            allow-clear
            v-model="dataForm.dataRoles"
            placeholder="数据角色"
            :options="roleData"
            :field-names="{ value: 'id', label: 'name' }"
          ></a-select> -->
          <a-tree-select v-model="dataForm.dataRoles"
                         multiple
                         :max-tag-count="2"
                         allow-clear
                         allow-search="true"
                         :tree-checkable="true"
                         tree-check-strictly
                         :placeholder="$t('campaign.reminder.input_data_role')"
                         :data="roleData"
                         :field-names="{ key: 'id', title: 'name' }">
          </a-tree-select>
        </a-form-item>
        <a-form-item field="summary"
                     :label="$t('campaign.popup.description')"
                     label-col-flex="70px">
          <a-textarea v-model="dataForm.summary"
                      type="textarea"
                      :placeholder="$t('campaign.reminder.input_description')" />
        </a-form-item>
      </a-form>
    </a-modal>
  </template>
</template>

<script setup>
import { onMounted, ref, getCurrentInstance } from "vue";
import { createGroup, saveGroup } from "@/api/group";
import { getTenantDataRole } from "@/api/user";
import { modalCommit } from "@/utils/modal";
import { useBussinessUnitStore } from "@/store";

const {
  proxy: { t }
} = getCurrentInstance()

const showGroupVisible = ref(false);
const dataForm = ref({});
const dataFormRef = ref({});
const isEdit = ref(false);
const emit = defineEmits(["change"]);
const roleData = ref([]);
const userTc = useBussinessUnitStore();
const tenantId = userTc.currentBussinessUnit?.tenantId;

onMounted(() => {
  getTenantDataRole({
    sort: "createTime, DESC",
    expression: `tenantId eq ${tenantId}`,
  }).then((res) => {
    roleData.value = toTree(res.content);
  });
});

const toTree = (data, parent) => {
  parent = parent || null;
  const tree = [];
  let temp;
  for (let i = 0; i < data.length; i++) {
    if (data[i].pid == parent) {
      const obj = data[i];
      temp = toTree(data, data[i].id);
      if (temp.length > 0) {
        obj.children = temp;
      }
      tree.push(obj);
    }
  }
  return tree;
};

const addGroup = async (done) => {
  return modalCommit(dataFormRef, done, async () => {
    dataForm.value.id = dataForm.value.code;
    dataForm.value.dataRoles = dataForm.value?.dataRoles?.length ? dataForm.value.dataRoles : ['0'];
    if (isEdit.value) {
      await saveGroup(dataForm.value);
      emit("change");
    } else {
      await createGroup(dataForm.value);
      emit("change");
    }
  });
};

const show = () => {
  showGroupVisible.value = true;
};

const createEdit = (data) => {
  console.log("createEdit", data);
  if (data) {
    data.dataRoles = data.dataRoles.includes('0') ? [] : data.dataRoles;
  }
  dataForm.value = data ? JSON.parse(JSON.stringify(data)) : {};
  isEdit.value = !!data;
  showGroupVisible.value = true;
};

defineExpose({ show, createEdit });
</script>

<style lang="less" scoped></style>
