export default {
  "menu.benefit": "Rights and interests of the template",
  "menu.reach.template": "Reach Manage",
  "menu.flow.template": "Flow Template",
  reach: {
    title: {
      template: "Communication Template",
      communicationTouchpoint: "Communication Touchpoint",
      communicationList: "Communication Lists",
      templateCode: "Template Code",
      templateName: "Template Name",
      status: "Status",
      reachTemplateManagement: "Communication Template Management",
      editReachTemplate: "Edit Communication Template",
      aiGenerated: "AI Generated",
      communicationCode: "Communication Code",
      communicationName: "Communication Name",
      communicationTemplate: "Communication Template",
      communicationGroup: "Communication Group",
      deleteReachTemplate: "Delete Communication Template",
    },
    popup: {
      deleteTitle: "Delete Process Template",
    },
    edit: {
      communicationLimitModel: "Communication Limit Model",
      limitFrequencyModel: "Model to limit frequency on this channel",
      remark: "Remarks",
      frequencyLimit: "Frequency Limit",
      communicationLimitIdentificationField: "Communication Limit Identifier Field",
      annualCount: "Annual Count",
      monthlyCount: "Monthly Count",
      dailyCount: "Daily Count",
    },
    column: {
      templateCode: "Template Code",
      templateName: "Template Name",
      touchpoint: "Touchpoint",
      templateContent: "Template Content",
      category: "Category",
      status: "Status",
      signature: "Signature",
      sync: "Sync",
      updateTime: "Update Time",
      action: "Action",
    },
    status: {
      sync: "Sync",
      selfBuilt: "Self-built",
      yes: "yes",
      no: "no",
    },
    type: {
      sms: "SMS",
      mms: "MMS",
      wechatTemplate: "WeChat Template",
      wechatNotification: "WeChat Notification",
      app: "App"
    },
    sms: {
      contentName: "Content Name",
      touchpoint: "Touchpoint",
      communicationId: "Communication ID",
      communicationName: "Touchpoint Name",
      templateCode: "Template Code",
      templateName: "Template Name",
      communicationGroup: "Communication Group",
      content: "Content",
      status: "Status",
      createTime: "Creation Time",
      template: "Communication Template",
      templateContent: "Template Content",
      description: "Description",
      fieldMappingConfiguration: "Field Mapping Configuration",
      templateField: "Template Field",
      mappingRelationship: "Mapping Relationship",
      mappedField: "Mapped Field",
      defaultValue: "Default Value",
      fieldLengthLimit: "Field Length Limit",
      mappingType: "Mapping Type",
      syncTemplateField: "Sync Template Field",
      customerField: "Customer Field",
      behaviorField: "Behavior Field",
      fixedValue: "Fixed Value",
      replacementValue: "Replacement Value",
      flow: "FLOW",
      currentTime: "Current Time",
      currentDate: "Current Date",
    },
    reminder: {
      touchpoint: "Select Touchpoint",
      templateCode: "Template code",
      templateName: "Template name",
      templateContent: "Enter Template Content",
      category: "Enter Category",
      status: "Select status",
      signature: "Enter Signature",
      remark: "Enter Remarks",
      description: "Enter Description Info",
      frequencyLimit: "Does the current channel require frequency limits?",
      communicationLimitIdentificationField: "User identification field for frequency limits on this channel",
      annualCount: "Annual reach limit for users on this channel",
      monthlyCount: "Monthly reach limit for users on this channel",
      dailyCount: "Daily reach limit for users on this channel",
      field: "Select Field",
      communicationCode: "Enter Communication Code",
      communicationName: "Enter Communication Name",
      communicationTemplate: "Enter Communication Template",
      communicationGroup: "Enter Communication Group",
      communicationId: "Enter Communication Id",
      template: "Select Template",
      selectReplacementValue: "Select a replacement value",
      bindingFieldNotRequired: "Binding field is not required to select",
      inputExpression: "Enter an expression",
      inputProcessStartId: "Enter the process start ID",
      inputProcessId: "Enter the process ID",
      inputFixedValue: "Enter fixed value",
      inputDefaultValue: "Enter default value",
      inputLimitSize: "limit size",
      timeFormat: "Time Format",
      behaviorModel: "Behavior Model",
      selectModel: "Select Model",
    }
  }
};
