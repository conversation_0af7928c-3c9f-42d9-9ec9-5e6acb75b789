import i18n from "../locale";

export const selectTypes = [
    {
        // label: "计算人群",
        label: i18n.global.t("global.task.audience.calculateAudience"),
        value: "CONDITION",
    },
    {
        // label: "导入人群",
        label: i18n.global.t("global.task.audience.importAudience"),
        value: "MANUAL",
    },
];

export const usageTypes = [
    {
        // label: "测试人群",
        label: i18n.global.t("global.task.audience.testAudience"),
        value: "TEST",
    },
    {
        // label: "真实触发",
        label: i18n.global.t("global.task.audience.realTrigger"),
        value: "CAMPAIGN",
    },
    {
        // label: "请选择",
        label: i18n.global.t("global.task.audience.select"),
        value: "",
    },
];
