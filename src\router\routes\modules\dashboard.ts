export default {
  path: "dashboard",
  name: "dashboard",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.dashboard",
    requiresAuth: true,
    icon: "icon-dashboard",
    order: 10,
    parentMenu:true,
  },
  children: [
    {
      path: "workplace",
      name: "workplace",
      component: () => import("@/views/ma-dashboard/workplace/statistics.vue"),
      meta: {
        type:'menu',
        locale: "menu.dashboard.workplace",
        requiresAuth: true,
        roles: ["ma_menu.workplace"],
      },
    },
    
  ],
};
