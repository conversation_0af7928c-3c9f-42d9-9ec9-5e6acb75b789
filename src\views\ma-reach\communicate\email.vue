<template>
  <module edit>
    <template #main>
      <div class="template-body">
        <a-form ref="formRef" class="left-form" :model="entity">
          <a-space direction="vertical" :size="16">
            <a-card class="general-card">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="模板名称" field="name">
                    <a-input v-model="entity.name" placeholder="请输入模板名称">
                    </a-input>
                  </a-form-item>
                  <a-form-item label="渠道" field="channelType">
                    <div class="m-t-p m-t-sl">
                      <a-select v-model="entity.channelId" placeholder="请选择渠道" popup-container=".m-t-sl">
                        <a-option v-for="item in channelList" :key="item.id" :value="item.id"
                          :label="item.name"></a-option>
                      </a-select>
                    </div>
                  </a-form-item>
                  <a-form-item label="邮件主题" field="subject">
                    <a-input v-model="entity.setting.subject" placeholder="请输入邮件主题"></a-input>
                  </a-form-item>
                  <a-form-item label="是否为html" field="html">
                    <a-switch v-model="entity.setting.html" type="round">
                      <template #checked> 是 </template>
                      <template #unchecked> 否 </template>
                    </a-switch>
                  </a-form-item>
                  <!-- <a-form-item label="沟通单价" field="budget">
                    <a-input-number v-model="entity.budget" placeholder="请输入沟通单价" :min="0" />
                  </a-form-item> -->
                  <a-form-item label="描述信息" field="summary">
                    <a-textarea v-model="entity.summary" placeholder="请输入描述信息" />
                  </a-form-item>
                  <a-form-item label="状态" field="status">
                    <a-switch v-model="entity.status" type="round" checked-value="ENABLED" unchecked-value="DISABLED">
                      <template #checked> 启用 </template>
                      <template #unchecked> 禁用 </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="模板内容" field="content">
                    <a-textarea v-if="!entity.setting.html" v-model="entity.setting.template" placeholder="请输入模板内容" />
                    <TinymceEditor v-else v-model:modelValue="entity.setting.template" />
                    <template #extra>
                      例如：用户:${name}欢迎使用本公司的产品
                    </template>
                  </a-form-item>
                </a-col>
                <a-col :span="18">
                  <a-typography-title :heading="6">
                    字段映射配置
                  </a-typography-title>
                  <MappingTemplate v-model:dataList="entity.setting.templateMappings" :customer-model="customerModel" />
                </a-col>
              </a-row>
            </a-card>
          </a-space>
        </a-form>

        <!-- 显示 -->
        <!-- <ModelView v-if="entity?.content" :entity="entity" view-type="EMAIL" /> -->
      </div>
    </template>

    <template #action>
      <a-space>
        <a-button v-if="module.isEdit" v-permission="['ma.marketing_capability.simulate']" type="primary" :loading="simulating" @click="simulate">模拟发送</a-button>
      </a-space>
      <simulate-dialog ref="simulateDlg" />
    </template>
  </module>
</template>

<script>
import { ref, provide } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Message } from "@arco-design/web-vue";
import { formatFields } from "@/utils/field";
import {
  findCommunicateItem,
  saveCommunicateInfo,
  findChannelList,
  simulateCommunicateSend
} from "@/api/communicate";
import { findCustomerModel } from "@/api/system";
import TinymceEditor from "@/components/ma/tinymce-editor/index.vue";
import SimulateDialog from "@/components/modal-dlg/customer-selector-dlg.vue";
// import ModelView from "@/components/ma/template-preview/index.vue";
import MappingTemplate from "@/components/ma/mapping-template/index.vue";

export default {
  components: {
    SimulateDialog,
    MappingTemplate,
    // ModelView,
    TinymceEditor
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      mainPath: "/reach/communicate",
      breadcrumb: [
        {
          name: "营销云触达",
          path: "/reach/communicate"
        },
        {
          name: "编辑邮件模板"
        }
      ],
      capabilityType: "email",
      isEdit: !!route.query.id
    });

    let queryValue = route.query.id;

    const entity = ref({
      status: "ENABLED",
      type: module.value.capabilityType,
      category: "reach",
      budgetSetting: {},
      setting: {
        html: false,
        type: module.value.capabilityType,
        feedback: false,
        templateMappings: []
      }
    });
    const channelList = ref([]);
    const customerModel = ref({});

    const simulateDlg = ref(null);
    const simulating = ref(false);

    const bindData = async () => {
      if (module.value.isEdit) {
        entity.value = await findCommunicateItem(queryValue);
      }
      const data = await findCustomerModel();
      if (data) {
        customerModel.value = data;
        customerModel.value.fields = formatFields(data.fields);
      }
      channelList.value = await findChannelList({
        expression: `type eq ${module.value.capabilityType}`
      });
    };

    const save = async () => {
      entity.value = await saveCommunicateInfo(entity.value);
      queryValue = entity.value.id;
      module.value.isEdit = true;
      Message.success("保存成功！");
      if (!entity.value.id) {
        router.push({ path: module.value.mainPath });
      }
    };

    const simulate = async () => {
      const customers = await simulateDlg.value.show(customerModel.value);
      if (customers) {
        simulating.value = true;
        try {
          for (let i = 0; i < customers.length; i += 1) {
            const customer = customers[i];
            const simResult = await simulateCommunicateSend({
              capabilityId: entity.value.id,
              customerId: customer,
              triggerType: "simulate",
              setting: {
                limited: false
              },
              budgetSetting: {
                enabled: false
              }
            });
            if (simResult.status !== "SENT") {
              Message.error({
                content: `客户${simResult.customerId}模拟失败：${simResult.message}`,
                closable: true
              });
              return;
            }

          }
          Message.success("模拟发送成功！");
        } finally {
          simulating.value = false;
        }
      }
    };

    const setup = {
      module,
      entity,
      channelList,
      customerModel,
      bindData,
      save,
      simulate,
      simulateDlg,
      simulating
    };
    provide("edit", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped>
.template-body {
  display: flex;
  height: calc(100vh - 160px);
  background-color: #ffffff;

  .left-form {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}

::v-deep(.general-card) {
  min-height: calc(100vh - 160px);
}

.m-t-p {
  position: relative;
  width: 100%;
}
</style>
