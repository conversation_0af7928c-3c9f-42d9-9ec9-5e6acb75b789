import { App } from "vue";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  VisualMapComponent,
  ToolboxComponent,
  TitleComponent,
  MarkLineComponent,
} from "echarts/components";
import Chart from "./chart/index.vue";
import Breadcrumb from "./breadcrumb/index.vue";
import module from "./module/index.vue";
import cardwarp from "./card-warp/index.vue";
import CustomerModelConfig from "./model-config/customer.vue";
import BehaviorModelConfig from "./model-config/behavior.vue";

use([
  Canvas<PERSON>ender<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Grid<PERSON>omponent,
  Tooltip<PERSON>omponent,
  VisualMapComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  MarkLineComponent,
  ToolboxComponent,
  Funnel<PERSON>hart,
  TitleComponent,
]);

export default {
  install(Vue: App) {
    Vue.component("Chart", Chart);
    Vue.component("Breadcrumb", Breadcrumb);
    Vue.component("Module", module);
    Vue.component("Cardwarp", cardwarp);
    Vue.component("CustomerModelConfig", CustomerModelConfig);
    Vue.component("BehaviorModelConfig", BehaviorModelConfig);
  },
};
