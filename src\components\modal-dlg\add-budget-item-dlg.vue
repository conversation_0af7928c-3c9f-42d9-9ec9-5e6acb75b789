/**
* 添加预算条目
*/
<template>
  <a-modal v-model:visible="addItemVisible" @before-ok="handleOk">
    <template #title>{{ dataForm.id ? "编辑条目" : "新增条目" }}</template>
    <a-form ref="dataFormRef" :model="dataForm">
      <a-form-item
        field="name"
        label="条目名称"
        :rules="[{ required: true, message: '不能为空' }]"
        label-col-flex="70px"
      >
        <a-input v-model="dataForm.name" placeholder="请输入条目名称" />
      </a-form-item>
      <a-form-item
        field="planned"
        label="预算金额"
        :rules="[{ required: true, message: '不能为空' }]"
        label-col-flex="70px"
      >
        <a-input-number
          v-model="dataForm.planned"
          placeholder="请输入预算金额"
          :min="0"
        />
      </a-form-item>
<!--      <a-form-item field="adjust" label="开启共享" label-col-flex="70px">-->
<!--        <a-checkbox v-model="dataForm.adjust"></a-checkbox>-->
<!--      </a-form-item>-->
      <a-form-item
        field="alarmLimit"
        label="预警"
        :rules="[{ required: true, message: '不能为空' }]"
        label-col-flex="70px"
      >
        <a-input-number v-model="dataForm.alarmLimit" :max="dataForm.planned">
          <template #prefix>￥</template>
        </a-input-number>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { Modal } from "@arco-design/web-vue";
import { addBudgetItem } from "@/api/budget";
import { modalCommit } from "@/utils/modal";

const props = defineProps(["budgetId", "bindData"]);
const budgetId = props.budgetId;
const bindData = props.bindData;
const addItemVisible = ref(false);
const dataForm = ref({});
const dataFormRef = ref({});

const handleOk = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    await addBudgetItem({
      ...dataForm.value,
      budgetId: budgetId,
      type: "AMOUNT",
    });
    await bindData();
  });
};

const show = () => {
  dataFormRef.value.clearValidate()
  addItemVisible.value = true;
};

const create = () => {
  dataFormRef.value.clearValidate()
  dataForm.value = {};
  addItemVisible.value = true;
};

const edit = (data) => {
  dataFormRef.value.clearValidate()
  dataForm.value = JSON.parse(JSON.stringify(data));
  addItemVisible.value = true;
};

defineExpose({ show, create, edit });
</script>
<style  lang="less" scoped>
</style>
