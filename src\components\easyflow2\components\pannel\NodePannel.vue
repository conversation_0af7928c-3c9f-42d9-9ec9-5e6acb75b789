<template>
  <a-drawer :mask="true" :width="pannelWidth" :footer="false" :mask-closable="true" :visible="show"
    class="easyflow-pannel" popup-container="#easyflow-content" @cancel="handleCancel">
    <template #title>
      <div v-if="node" class="title-name theme-color">
        <span class="node-icon iconfont" :class="iconClass" />
        <div v-if="!editTitle" class="pannel-title">
          <span class="node-name">{{ node?.data?._name }}</span>
          <icon-edit v-if="editEnable" @click="showEditTitle" />
        </div>
        <span v-show="editTitle">
          <a-input ref="newTitleRef" v-model="newTitle" @blur="changeNewTitle()" @press-enter="changeNewTitle()" />
        </span>
      </div>
      <div>
        <span class="arco-icon-hover">
          <a-popover :title="t('campaign.pannel.nodeDescription')" trigger="click" position="br">
            <icon-question-circle />
            <template #content>
              <div>
                <p>节点Id: <span class="node-id">{{ id }}</span></p>

              </div>
              <div ref="helpRef" class="help">
                <component :is="help" :key="id" type="config"/>
              </div>
            </template>
          </a-popover>
        </span>
      </div>
    </template>
    <component :is="pannel" :key="id" ref="pannelRef" :easyflow="easyflow" :node="node" :connections="connections"
      :edit-enable="editEnable" :graph="graph" class="easyflow-component" />
    <!-- 备注信息 -->
    <p class="node-desc">{{t('campaign.pannel.remarkInfo')}}</p>
    <a-textarea v-model="nodeDesc" />
  </a-drawer>
</template>

<script setup>
import { ref, shallowRef, inject, provide, computed, getCurrentInstance } from "vue";

const {
      proxy: { t }
    } = getCurrentInstance();

const flow = inject("flow");
const { easyflow } = flow;
const id = ref(null);
const node = ref(null);
const connections = ref(null);
const show = ref(false);
const pannel = shallowRef(null);
const iconClass = ref(null);
const pannelWidth = ref(360);
const editTitle = ref(false);
const newTitle = ref("");
const help = shallowRef(null);
const nodeDesc = ref("");

const newTitleRef = ref(null);
const pannelRef = ref(null);
const { editEnable } = flow;
const graph = computed(() => {
  return flow?.graph.value;
});

const showPannel = (nodePannel) => {
  pannel.value = nodePannel;
};

const showHelp = (helpPannel) => {
  help.value = helpPannel;
};

const tooglePannel = (is, _node, _connections) => {
  if (is) {
    id.value = _node.id;
    node.value = _node;
    show.value = is;
    connections.value = _connections;
    pannelWidth.value = _node.data._type === "filter" ? 900 : 360;
    iconClass.value = easyflow.value.getNodeConfig(_node.shape).iconClass
    nodeDesc.value = _node.data.nodeDesc
    // component = this.iconClass = this.capability[_node.data._type]?.icon;
  } else {
    // id.value = null;
    // node.value = null;
    show.value = is;
    graph.value.cleanSelection();
    pannel.value = null;
    iconClass.value = null;
  }

  // 重新赋值颜色
  let nodeItem = JSON.parse(localStorage.getItem('nodeItem')) || {}
  if (_node?.data?.configId) {
    let item = nodeItem[_node?.data?.configId]
    if (item) {
      iconClass.value = item.icon
    }

  }
};
const showEditTitle = () => {
  editTitle.value = true;
  newTitle.value = node.value?.data?._name;
  setTimeout(() => {
    newTitleRef.value.focus();
  }, 10);
};

const changeNewTitle = async () => {
  node.value.data._name = newTitle.value;
  editTitle.value = false;
  node.value.trigger("change:data", {
    cell: node.value,
    current: node.value.data,
  });
};

const handleCancel = () => {
  if (show?.value) {
    save();
    tooglePannel(false);
  }
};

const save = () => {
  const pannelData = pannelRef.value.save();
  pannelData._name = node.value.data._name;
  pannelData.nodeDesc = nodeDesc.value;
  node.value.setData(pannelData, { overwrite: true });
  node.value.trigger("change:data", {
    cell: node.value,
    current: pannelData,
  });
};

const cancel = () => {
  tooglePannel(false);
};

defineExpose({
  tooglePannel,
  showPannel,
  showHelp,
  handleCancel,
});

provide("pannel", {
  editEnable
});
</script>

<style lang="less">
.easyflow-pannel {
  z-index: 5 !important;

  .title-name {
    display: inherit;

    .pannel-title {
      display: flex;
      align-items: center;
      height: 30px;
    }
    .node-name {
      display: initial;
      max-width: 190px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .node-icon {
      margin-right: 5px;
    }

    .arco-icon {
      margin-left: 5px;
      color: lightgray;
      cursor: pointer;
    }
    .node-id {
      color: lightgray;
      font-size: small;
      margin: 0 5px 0 20px;
    }
    .arco-input-wrapper .arco-input.arco-input-size-medium {
      line-height: 16px;
    }
  }

  .arco-drawer {
    z-index: 5 !important;
    border-left: 1px solid var(--color-border);
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 8%);

    .arco-drawer-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }

  .arco-form-item-label {
    font-weight: bold;
  }

  .form-item-radio {
    .arco-form-item-content {
      justify-content: space-between;
    }

    .arco-radio {
      padding: 5px 10px;
      box-sizing: border-box;
      border: 1px solid rgba(0, 0, 0, 0.08);
      border-radius: 2px;
    }
  }

  .node-desc {
    font-weight: bold;
  }
}

.pannel-form {
  color: #625f6e;

  .easyform-label {
    padding: 0;
  }

  .flex-line {
    display: flex;
  }
}

.easyflow-option {
  display: flex;
  justify-content: space-between;

  .desc {
    color: rgba(0, 0, 0, 0.2);
  }
}
</style>
