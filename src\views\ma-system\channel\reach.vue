<template>
  <module main>
    <template #filter></template>
    <template #search></template>
    <template #action>
        <a-button v-permission="['ma_menu.system.communicate-channel.create']" type="primary" @click="create">
          <template #icon>
            <icon-plus />
          </template>
          {{t('global.button.create')}}
        </a-button>
    </template>
    <template #context><span></span></template>
    <template #main>
      <a-table
        ref="table"
        :bordered="false"
        :data="dataSource"
        :pagination="false"
        @page-change="onPageChange"
      >
        <template #columns>
          <a-table-column :title="t('systemSetting.communicateChannel.channelName')" data-index="name" />
          <a-table-column :title="t('systemSetting.communicateChannel.channelType')" data-index="type">
            <template #cell="{ record }">
              {{ filters(capabilityType, record.type, "title", "type") }}
            </template>
          </a-table-column>
          <a-table-column :title="t('systemSetting.communicateChannel.createTime')" data-index="createdTime">
            <template #cell="{ record }">
              {{ $moment(record.createdTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </a-table-column>
          <a-table-column :title="t('systemSetting.communicateChannel.remark')" data-index="summary" />
          <a-table-column :title="t('global.button.operation')" align="center">
            <template #cell="{ record }">
              <a-button  v-permission="['ma_menu.system.communicate-channel.modify']" type="text" size="small" @click="detail(record)"
                >{{t('global.button.edit')}}</a-button
              >
              <a-button  v-permission="['ma_menu.system.communicate-channel.delete']" type="text" size="small" @click="deleteData(record.id)"
                >{{t('global.button.delete')}}</a-button
              >
            </template>
          </a-table-column>
        </template>
      </a-table>
      <capabilitySelectorDlg
        ref="csDlgRef"
        :prefix-path="module.editPath"
        :model-data="modelData"
      />
    </template>
  </module>
</template>

<script>
import { ref, provide, computed,getCurrentInstance } from "vue";

import { useRouter, useRoute } from "vue-router";
import { findPage, deleteItem } from "@/api/channel";
import { Modal } from "@arco-design/web-vue";
import { capabilityType } from "@/constant/capability";
import { filters } from "@/utils/filter";
import CapabilitySelectorDlg from "@/components/modal-dlg/capability-selector-dlg.vue";

import { useUserStore } from "@/store";

export default {
  components: {
    CapabilitySelectorDlg
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const userStore = useUserStore();
    const { userAuthInfo } = userStore;
    const route = useRoute();
    // 路由API
    const router = useRouter();
    // 模块设置
    const module = ref({
      entityIdField: "id",
      entityName: "渠道管理",
      breadcrumb: [
        {
          name: t('menu.system'),
          path: "/system/setting"
        },
        {
          name: t('systemSetting.communicateChannel.communicateManage'),
        }
      ],
      editPath: "/system/reach/",
      mainPath: "/system/setting"
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true
    });

    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    // 查询API
    const bindData = async (expression) => {
      entity.value = await findPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1
        },
        {
          expression: "category eq reach",
          fields: "name,type,createdTime,summary"
        }
      );
      pagination.value.total = entity.value.totalElements;
    };

    // 详情跳转
    const detail = (row) => {
      const { query } = route;
      query[module.value.entityIdField] = row[module.value.entityIdField];
      router.push({ path: module.value.editPath + row.type, query });
    };

    const deleteData = async (id) => {
      Modal.confirm({
        title: t('global.button.delete'),
        content: t('global.tips.warning.delete'),
        onOk: async () => {
          await deleteItem(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1;
          }
          await bindData();
        }
      });
    };
    // 新建跳转
    const csDlgRef = ref(null);
    const modelData = ref([
      // { title: "微信", type: "wechat" },
      // { title: "微信公众号模板消息", type: "wechatTemplate" },
      // { title: "微信公众号客服消息", type: "wechatCustomer" },
      // { title: "微信公众号群发消息", type: "wechatMass" },
      { title: t('systemSetting.communicateChannel.sms'), type: "sms" },
      { title: t('systemSetting.communicateChannel.email'), type: "email" }
    ]);
    const create = () => {
      const permissionList = userAuthInfo.menus;
      if (permissionList.includes('ma_menu.system.communicate-channel.create')) {
       csDlgRef.value.show()

      }else {
        Notification.error({
          title:t('global.tips.error.permission'),
          content: 'No Permission!',
        });
      }
    };

    const quit = async () => {
      await router.push({ path: module.value.mainPath });
    };

    const rowSelection = ref({
      type: "checkbox",
      showCheckedAll: true
    });

    const onPageChange = () => {
      bindData();
    };

    const setup = {
      t,
      module,
      entity,
      detail,
      deleteData,
      csDlgRef,
      modelData,
      create,
      bindData,
      dataSource,
      pagination,
      quit,
      rowSelection,
      onPageChange,
      capabilityType,
      filters
    };
    provide("main", setup);
    return setup;
  }
};
</script>
