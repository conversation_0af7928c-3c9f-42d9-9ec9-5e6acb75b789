export default {
  path: "analysis",
  name: "analysis",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.analysis",
    requiresAuth: true,
    iconFont: "icon-chart",
    order: 45,
    parentMenu:true,
  },
  children: [
    {
      path: "main",
      name: "Analysis",
      component: () => import("@/views/ma-analysis/main.vue"),
      meta: {
        type:'menu',
        locale: "menu.analysis",
        requiresAuth: true,
        roles: ["ma_menu.analysis"],
      },
    },
    {
      path: "analysis-rfm",
      name: "AnalysisRfm",
      component: () => import("@/views/ma-analysis/rfm/main.vue"),
      meta: {
        locale: "AnalysisRfm",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "throughput",
      name: "Throughput",
      component: () => import("@/views/ma-analysis/report/throughput.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
  ],
};
