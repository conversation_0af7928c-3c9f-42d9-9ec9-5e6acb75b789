import { defineStore } from "pinia";
import { getCurrentUserDataRole } from "@/api/user";

const useUserDataRoleStore = defineStore("userdatarole", {
  state: () => ({
    currentUserDataRole: [] as any,
  }),
  actions: {
    async getUserDataRole() {
      try {
        const response = await getCurrentUserDataRole();
        this.currentUserDataRole = response;
      } catch (error) {
        console.error("获取失败", error);
      }
    },
  },
});
export default useUserDataRoleStore;
