import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { Message } from "@arco-design/web-vue";

export default function useLocale() {
  const i18 = useI18n();
  const currentLocale = computed(() => {
    return i18.locale.value;
  });
  const changeLocale = (value: string) => {
    i18.locale.value = value;
    localStorage.setItem("APP_LANG", value);
    Message.success(i18.t("navbar.action.locale"));
  };
  const changeBussinessUnit = (value: any) => {
    Message.success(`业务单位切换为：${value.name}`);
    localStorage.setItem("current-bussiness-unit", value.id);
    window.location.href = "/load";
    window.location.reload();
  };
  return {
    currentLocale,
    changeLocale,
    changeBussinessUnit,
  };
}
