<template>
  <module edit>
    <template #main>
      <div class="template-body">
        <a-form ref="formRef" layout="vertical" class="left-form" :model="entity">
          <a-space class="general-form" direction="vertical" :size="16">
            <a-card class="general-card">
              <a-row :gutter="80">
                <a-col :span="24">
                  <a-form-item label="模板名称" field="name">
                    <a-input v-model="entity.name" placeholder="请输入模板名称" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="描述信息" field="summary">
                    <a-textarea v-model="entity.summary" placeholder="请输入描述信息" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="渠道" field="channelType">
                    <div class="m-t-p m-t-sl">
                      <a-select v-model="entity.channelId" placeholder="请选择渠道" popup-container=".m-t-sl">
                        <a-option v-for="item in channelList" :key="item.id" :value="item.id" :label="item.name" />
                      </a-select>
                    </div>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="模板类型" field="setting.type">
                    <div class="m-t-p m-t-sl">
                      <a-select v-model="entity.setting.type" placeholder="请选择模板类型" popup-container=".m-t-sl">
                        <a-option v-for="item in templateTypes" :key="item.id" :value="item.id" :label="item.name" />
                      </a-select>
                    </div>
                  </a-form-item>
                </a-col>
                <!-- <a-col :span="12">
                  <a-form-item label="沟通单价" field="budget">
                    <a-input v-model="entity.budget" placeholder="请输入沟通单价" />
                  </a-form-item>
                </a-col> -->
                <a-col :span="6">
                  <a-form-item label="反馈结果" field="setting.feedback" tooltip="需要反馈结果的沟通模板在返回结果后才会触发流程继续执行。">
                    <a-switch v-model="entity.setting.feedback" type="round">
                      <template #checked> 启用 </template>
                      <template #unchecked> 禁用 </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="状态" field="status">
                    <a-switch v-model="entity.status" type="round" checked-value="ENABLED" unchecked-value="DISABLED">
                      <template #checked> 启用 </template>
                      <template #unchecked> 禁用 </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col v-if="entity.setting.type && !['template'].includes(entity.setting.type)" :span="12">
                  <a-form-item label="素材类型" field="materialType">
                    <a-select v-model="entity.materialType" placeholder="请选择素材类型">
                      <a-option v-for="item in materialTypes" :key="item.id" :value="item.id" :label="item.name" />
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="模板Id" field="setting.templateId">
                    <a-input v-model="entity.setting.templateId" placeholder="请输入模板Id" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col>
                  <a-form-item label="模板内容" field="setting.template">
                    <a-textarea v-model="entity.setting.template" placeholder="请输入模板内容" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="18">
                  <a-typography-title :heading="6">
                    字段映射配置
                  </a-typography-title>
                  <MappingTemplate v-model:dataList="entity.setting.templateMappings" :customer-model="customerModel" />
                </a-col>
              </a-row>
            </a-card>
          </a-space>
        </a-form>

        <!-- 显示 -->
        <!-- <ModelView ref="modelViewRef" :entity="entity" :wechat-templates="wechatTemplates"
          view-type="WECHAT_CUSTOMER_MSG" /> -->
      </div>
    </template>

    <template #action>
      <a-space>
        <a-button v-if="isEdit" type="primary" @click="simulate">模拟发送</a-button>
      </a-space>
      <simulate-dialog ref="simulateDlg" />
    </template>
  </module>
</template>

<script>
import { ref, provide } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  findCommunicateItem,
  saveCommunicateInfo,
  findChannelList,
  simulateCommunicateSend
} from "@/api/communicate";
import { findCustomerModel } from "@/api/system";
import { formatFields } from "@/utils/field";
import { Message } from "@arco-design/web-vue";
import { wechatMsgTypes } from "@/constant/capability";
import SimulateDialog from "@/components/modal-dlg/customer-selector-dlg.vue";
// import ModelView from "@/components/ma/model-view/index.vue";
import MappingTemplate from "@/components/ma/mapping-template/index.vue";

export default {
  components: {
    SimulateDialog,
    MappingTemplate,
    // ModelView,
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      mainPath: "/reach/communicate",
      breadcrumb: [
        {
          name: "营销云触达",
          path: "/reach/communicate"
        },
        {
          name: "编辑微信消息模板"
        }
      ],
      capabilityType: "wechat",
    });

    let queryValue = route.query.id;
    let isEdit = !!queryValue;

    const entity = ref({
      type: module.value.capabilityType,
      status: "ENABLED",
      category: "reach",
      budgetSetting: {},
      setting: {
        feedback: false,
        templateMappings: [],
      }
    });
    const customerModel = ref({});
    const channelList = ref([]);
    const templateTypes = ref(wechatMsgTypes);

    const materialTypes = [
      { id: "link", name: "外链" },
      { id: "text", name: "文字" },
      { id: "image", name: "图片" },
      { id: "voice", name: "语音" },
      { id: "video", name: "视频" },
      { id: "news", name: "图文" },
      { id: "miniprogram_page", name: "小程序" },
    ];

    const simulateDlg = ref(null);

    const bindData = async () => {
      if (isEdit) {
        entity.value = await findCommunicateItem(queryValue);
      }
      const data = await findCustomerModel();
      if (data) {
        customerModel.value = data;
        customerModel.value.fields = formatFields(data.fields);
      }
      channelList.value = await findChannelList({
        expression: `type eq wechat`,
      });
    };

    const quit = async () => {
      await router.push({ path: module.value.mainPath });
    };
    const formData = ref({});
    const save = async () => {
      const res = await saveCommunicateInfo(entity.value);
      queryValue = res.id;
      isEdit = !!queryValue;
      Message.success("提交成功！");
      await quit();
    };

    const simulate = async () => {
      const customers = await simulateDlg.value.show(customerModel.value);
      if (customers) {
        customers.forEach((customer) => {
          simulateCommunicateSend({
            capabilityId: entity.value.id,
            customerId: customer,
            triggerType: 'simulate',
            setting: {
              limited: false
            },
            budgetSetting: {
              enabled: false
            },
          });
        });
      }
    };

    const modelViewRef = ref(null);

    const setup = {
      module,
      isEdit,
      entity,
      channelList,
      customerModel,
      materialTypes,
      templateTypes,
      formData,
      bindData,
      save,
      quit,
      simulate,
      simulateDlg,
      modelViewRef,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.template-body {
  display: flex;
  height: calc(100vh - 160px);
  background-color: #ffffff;

  .left-form {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}

::v-deep(.general-card) {
  min-height: calc(100vh - 160px);
}

.m-t-p {
  position: relative;
  width: 100%;
}
</style>
