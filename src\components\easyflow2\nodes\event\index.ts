import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import EventNode from "./node.vue";
import EventPannel from "./pannel.vue";

const nodeData = {
  type: "event",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<EventNode />`,
      components: {
        EventNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("EventNode", nodeData.node, true);
};

const Event = {
  type: "event",
  name: "事件",
  shape: "EventNode",
  iconClass: "icon-event",
  color: "#ffffff",
  themebg: "#fff000",
  registerNode: registerNode,
  pannel: EventPannel,
  skippable: true,
};

export default Event;
