/** *by:<EMAIL> on 2022/6/21 0021 */
<template>
  <div class="audience-view">
    <div class="top-title-btn">
      <a-breadcrumb>
        <template v-for="item in module.breadcrumb" :key="item.name">
          <a-breadcrumb-item v-if="item.path">
            <router-link :to="item.path">
              <span style="font-size: 16px; font-weight: normal">{{
                item.name
              }}</span>
            </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-else>
            <span style="font-size: 16px; font-weight: bold">{{
              item.name
            }}</span>
          </a-breadcrumb-item>
        </template>
      </a-breadcrumb>
      <a-radio-group v-model="radioValue" type="button">
        <a-radio value="1">{{t('audience.detail.detail')}}</a-radio>
        <a-radio value="2">{{t('audience.detail.snapshot')}}</a-radio>
      </a-radio-group>
    </div>

    <div class="view-panel">
      <ViewDetails v-if="radioValue === '1'" />
      <ViewSnapshot v-if="radioValue === '2'" />
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, getCurrentInstance } from "vue";
import ViewDetails from "./components/view-details.vue";
import ViewSnapshot from "./components/view-snapshot.vue";

export default defineComponent({
  components: {
    ViewDetails,
    ViewSnapshot,
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const module = ref({
      breadcrumb: [
        {
          // name: "活动人群",
          name: t('audience.title'),
          path: "/audience/main",
        },
        {
          // name: "人群详情",
          name: t('audience.detail.title'),
        },
      ],
    });

    const radioValue = ref("1");
    return {
      module,
      radioValue,
    };
  },
});
</script>

<style lang="less" scoped>
.audience-view {
  margin: 20px 20px 0 20px;
  background-color: #ffffff;
}

.top-title-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 0 20px;
}

.view-panel {
  margin: 20px;
}

:deep(.arco-radio-group-button) {
  margin: 0;
}
</style>
