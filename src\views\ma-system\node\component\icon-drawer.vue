<template>
  <a-drawer
    :width="370"
    :visible="props.visible"
    :footer="false"
    unmount-on-close
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title> {{ props.drawerTitle }} </template>
    <div class="iconbox" style="width: 100%">
      <div
        v-for="(item, index) in iconList.icon"
        :key="index"
        class="iconitem"
        @click="selectIcon(item)"
      >
        <!-- <div class="icon-name">{{ item }}</div> -->
        <div :class="[`iconfont ${item}`]"></div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { iconList } from "@/constant/iocn";
import { Message } from "@arco-design/web-vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  drawerTitle: {
    type: String,
    default: "Title"
  }
});
const emit = defineEmits(["updateDrawerVisible", "selectionIcon"]);

const handleOk = () => {
  emit("updateDrawerVisible", false);
};
const handleCancel = () => {
  emit("updateDrawerVisible", false);
};
const selectIcon = (icon) => {
  emit("updateDrawerVisible", false);
  Message.success(`选中${icon}`);
  emit("selectionIcon", icon);
};
// defineExpose({ showCreate, showEdit });
</script>

<style lang="less" scoped>
.iconbox {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  .iconitem {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 20%;
    height: 50px;
    font-size: 12px;
    margin-bottom: 10px;
    padding: 10px;
    .icon-name {
      width: 100%;
      height: 15px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex-wrap: nowrap;
      overflow: hidden;
    }
    .iconfont {
      font-size: 20px;
    }
  }
  .iconitem:hover {
    cursor: pointer;
    background-color: #f2f3f5;
  }
}
.arco-icon {
  font-size: 100;
}
</style>
