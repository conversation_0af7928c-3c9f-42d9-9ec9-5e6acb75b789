import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findBehaviorModelList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-model/list`, {
    params
  });
}

export function findBehaviorModelItem(id?: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-model/${id}`);
}

export function findBehaviorModelSample(id?: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-model/sample/${id}`);
}

export function getBehaviorModelIdentifyField(id?: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-model/${id}/identify-field`);
}


export function findBehaviorEventList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-event/list`, {
    params
  });
}

export function findBehaviorEventItem(id?: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-event/${id}`);
}

export function sendSimulateEvent(data: any) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/campaign/debug/simulate-event`, data);
}

export function syncBehaviorModelsFromDWH() {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/behavior-model/sync`);
}
