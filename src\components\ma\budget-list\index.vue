<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <span></span>
      </template>
      <template #context></template>
      <template #main>
        <a-table
          ref="table"
          :bordered="false"
          :data="dataSource"
          :pagination="false"
        >
          <template #columns>
            <a-table-column title="活动名称" data-index="budgetId">
              <template #cell="{ record }">
                {{ filters(campaignList, record.budgetId, "name", "id") }}
              </template>
            </a-table-column>
            <a-table-column title="预算条目" data-index="budgetItemId">
              <template #cell="{ record }">
                {{ filters(budgetList, record.budgetItemId, "name", "id") }}
              </template>
            </a-table-column>
            <a-table-column title="操作" align="center" :width="280">
              <template #cell="{ record }">
                <a-button type="text" size="small" @click="openViewPage(record)"
                  >查看
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
  </div>
</template>

<script>
import { ref, provide, computed } from "vue";
import { useRouter } from "vue-router";
import { getBudgetAlertPage, findBudgetItemList } from "@/api/budget";
import { findCampaignList } from "@/api/campaign";
import { filters } from "@/utils/filter";

export default {
  setup() {

    // #region 参数
    // 路由API
    const router = useRouter();

    // 模块设置
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: "预算告警",
        },
      ],
      editPath: "/campaign/campaign/budget",

      showCard: false,
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true,
    });

    // 过滤设置
    const filter = ref([]);

    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    // 获取列表数据
    const budgetList = ref([]);
    const campaignList = ref([]);

    // #endregion

    // #region 方法
    const getListData = () => {
      const budgetIdList = [];
      const budgetItemIdList = [];
      for (let i = 0; i < dataSource.value.length; i++) {
        budgetIdList.push(dataSource.value[i].budgetId);
        budgetItemIdList.push(dataSource.value[i].budgetItemId);
      }
      findBudgetItemList({
        expression: budgetItemIdList.join(",")
          ? `id in ${budgetItemIdList.join(",")}`
          : "",
        fields: "name",
      }).then((res) => {
        budgetList.value = res;
      });
      findCampaignList({
        expression: budgetIdList.join(",")
          ? `id in ${budgetIdList.join(",")}`
          : "",
        fields: "name",
      }).then((res) => {
        campaignList.value = res;
      });
    };

    // #endregion


    // 查询API
    const bindData = async (expression) => {
      entity.value = await getBudgetAlertPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        {
          expression,
        }
      );
      pagination.value.total = entity.value.totalElements;
      getListData();
    };

    // 跳转查看页
    const openViewPage = (row) => {
      router.push({ path: module.value.editPath, query: { id: row.budgetId } });
    };

    const setup = {
      module,
      filter,
      entity,
      bindData,
      dataSource,
      pagination,
      budgetList,
      campaignList,
      getListData,
      openViewPage,
      filters,
    };
    provide("main", setup);
    return setup;
  },
  data() {
    return {
      rowSelection: {
        type: "checkbox",
        showCheckedAll: true,
      },
    };
  },
};
</script>

<style scoped lang="less">
.arco-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.arco-radio {
  width: calc(50% - 10px);
  margin-bottom: 20px;

  &:nth-child(2n) {
    margin-right: 0;
  }
}

.custom-radio-card {
  border: 1px solid var(--color-border-2);
  padding: 10px 15px;
  width: 100%;
  min-height: 90px;
  border-radius: 4px;

  .custom-radio-card-title {
    font-size: 16px;
    color: #333333;
    font-width: bold;
  }

  .custom-radio-card-desc {
    font-size: 12px;
    color: #999999;
  }
}

.arco-radio-checked {
  .custom-radio-card {
    border-color: rgb(var(--primary-6));
  }

  .custom-radio-card-title {
    color: rgb(var(--primary-6));
  }
}
</style>
