<template>
  <div class="easyflow-pannel-timer">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">

      <a-form-item label="数量/批次">
        <a-input-number v-model="entity.batchSize" />
        <template #label>
          数量/批次
          <a-tooltip>
            <template #content>
              <p>在定时器触发的时候下发指定数量，用于下发流转的数量限制</p>
            </template>
            <icon-info-circle-fill />
          </a-tooltip>
        </template>
      </a-form-item>

      <a-form-item label="触发定时器" :content-flex="false">
        <a-range-picker v-model="timeRange" show-time :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
          format="YYYY-MM-DD" value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" @change="handleRangeTime" />
        <!-- <a-input v-model="entity.schedule.cron" class="cron-input" placeholder="请输入CRON表达式">
          <template #prepend> CRON </template>
        </a-input> -->
        <CronSelect v-if="!loading" v-model:cron="entity.schedule.cron" :disabled="!editEnable"/>
        <template #label>
          触发定时器
          <a-tooltip>
            <template #content>
              <p>在指定的时间范围内设置数据下发的时间，支持设置周期触发</p>
            </template>
            <icon-info-circle-fill />
          </a-tooltip>
        </template>
      </a-form-item>

      <a-form-item label="去重">
        <a-space>
          <a-switch v-model="entity.unique" type="round" />
          <a-tree-select v-if="entity.unique" v-model="entity.uniqueFields" :multiple="true" :allow-search="true" :data="flowModel"
            allow-clear :field-names="treeFieldStruct" placeholder="请选择去重字段" />
        </a-space>
        <template #label>
          去重
          <a-tooltip>
            <template #content>
              <p>可以选择数据去重客户字段，去重后的数据置为失败，原因为重复数据</p>
            </template>
            <icon-info-circle-fill />
          </a-tooltip>
        </template>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { treeFieldStruct } from "@/constant/common"
import { getFlowModelsByIds } from "@/api/campaign";
import { formatFields } from "@/utils/field"
import CronSelect from "@/components/ma/cron-select/index.vue";

const { node, connections, easyflow } = defineProps(["node", "connections", "easyflow"]);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;
const entity = ref({
  schedule: {
    type: "CRON"
  },
});

const timeRange = ref([]);
const flowModel = ref(null);
const loading = ref(true);

const handleRangeTime = () => {
  [entity.value.schedule.startTime, entity.value.schedule.endTime] = timeRange.value;
};

const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = formatFields(modelFields.fields, "");
};

const save = () => {
  return entity.value;
};

defineExpose({
  save,
});
onMounted(async () => {
  Object.assign(entity.value, node.data);
  timeRange.value = [
    entity.value.schedule.startTime,
    entity.value.schedule.endTime,
  ];
  await handelFlowModel();
  loading.value = false;
});
</script>

<style lang="less" scoped>
.easyflow-pannel-timer {
  .delay-input {
    margin: 5px 0;
    width: 60%;
  }
}
</style>
