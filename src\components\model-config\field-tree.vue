<template>
  <a-tree
    ref="treeRef"
    block-node
    :show-line="true"
    :default-expand-all="false"
    style="width: 100%"
    :data="treeData"
    :field-names="treeFiledName"
    @select="selectNode"
  >
    <!-- <template #switcher-icon="node, { isLeaf }">
      <IconDown v-if="!isLeaf" />
    </template> -->
    <template #title="nodeData">
      {{ nodeData?.aliasName === "" ? "New Node" : nodeData.aliasName }}
    </template>
    <template #extra="nodeData">
      <span v-if="nodeData.isKey" class="tag-key">{{t('systemSetting.basicSettings.primaryKey')}}</span>
      <span class="opt-btn" title="添加" @click="addNode(nodeData)">
        <IconPlus v-if="nodeData.type === 'struct'" />
      </span>
      <!-- <span class="opt-btn" title="删除" @click="removeNode(nodeData)">
        <IconDelete v-if="!nodeData.isKey" />
      </span> -->
    </template>
  </a-tree>
</template>

<script setup>
import { ref,getCurrentInstance } from "vue";

defineProps({
  treeData: Object,
  selectNode: Function,
  addNode: Function,
  removeNode: Function,
  pkName: String
});
const {
      proxy: { t }
    } = getCurrentInstance()
const emit = defineEmits(["selectTreeNode"]);
const treeRef = ref();

const treeFiledName = {
  key: "path",
  title: "aliasName",
  children: "fields"
};

const expandNode = (node) => {
  treeRef.value.expandNode(node.path, true);
};
const selectTreeNode = (node) => {
  treeRef.value.selectNode(node.path);
};
const selectNode = (key, val) => {
  emit("selectTreeNode", val.node);

  // if (checkField()) {
  // eslint-disable-next-line prefer-destructuring
  // curField.value = node.selectedNodes[0];
  // }
};
defineExpose({
  expandNode,
  selectTreeNode
});
</script>

<style scoped lang="less">
.opt-btn {
  width: 24px;
  font-size: 12px;
  top: 10px;
  color: #3370ff;
}
</style>
