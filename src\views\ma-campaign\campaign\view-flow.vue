<template>
  <module view>
    <template #action>
      <a-button type="outline" @click="showSaveTemplateDlg">{{ t('global.button.saveTemplate') }}</a-button>
      <a-button type="primary" @click="showBasicConfig">{{ t('global.button.baseInfo') }}</a-button>
    </template>
    <template #main>
      <div v-if="entity != null" class="flow-view">
        <easyflow ref="flowRef" :entity="entity" :flow-id="entity.id" :flow_data="entity.setting.flowData"
          :simulate="simulate" :status="entity.status" :monitor="monitor" :started="entity.started"
          :edit-enable="false" />
      </div>
      <task-edit-drawer ref="taskDrawerRef" :editable="false" />
      <save-template-dlg ref="saveTemplateRef" />
    </template>
  </module>
</template>

<script>
import EasyFlow from "@/components/easyflow2";

import { useRoute, onBeforeRouteLeave } from "vue-router";
import { ref, provide, getCurrentInstance } from "vue";
import { filters } from "@/utils/filter";
import { campaignStatus } from "@/constant/campaign";
import {
  getCampaign,
  simulateCampaign,
} from "@/api/campaign";
import { monitorCampaign } from "@/api/monitor"
import TaskEditDrawer from "./component/task-edit-drawer.vue";
import SaveTemplateDlg from "./component/save-template-dlg.vue";

export default {
  components: {
    easyflow: EasyFlow,
    TaskEditDrawer,
    SaveTemplateDlg
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const { id, groupId } = route.query;
    const module = ref({
      entityIdField: "id",
      entityName: "",
      breadcrumb: [
        {
          // name: "营销活动",
          name: t('campaign.title'),
          path: "/campaign/group",
        },
        {
          // name: "活动流程",
          name: t('campaign.flow.title'),
          path: `/campaign/campaign`,
          query: { id: groupId }
        },
        {
          // name: "流程活动",
          name: t('campaign.flow.campaign'),
          path: `/campaign/campaign/view-flow`,
          query: { id, groupId }
        },
      ],
      mainPath: `/campaign/campaign?id=${groupId}`,
      mainQuery: { id: groupId },
    });
    const flowRef = ref();
    const taskDrawerRef = ref();
    const saveTemplateRef = ref();
    const entity = ref(null);

    /**
     * 显示基本信息窗口
     */
    const showBasicConfig = () => {
      taskDrawerRef.value.editTask(entity.value);
    }
    const showSaveTemplateDlg = () => {
      saveTemplateRef.value.show(entity.value.setting.flowData);
    }

    const bindData = async () => {
      entity.value = await getCampaign(id);
      module.value.entityName = `${entity.value.name} - [${filters(
        campaignStatus,
        entity.value.status
      )}]`;
      module.value.breadcrumb[2].name = module.value.entityName;
    };

    const simulate = async () => {
      await simulateCampaign(id);
    };

    const monitor = async (enabled, instanceId, engineType) => {
      let monitorData = { tasks: [] };
      if (enabled) {
        monitorData = await monitorCampaign({ flowId: id, instanceId, engineType});
      }
      monitorData.enabled = enabled;
      return monitorData;
    };

    onBeforeRouteLeave(() => {
      flowRef.value.toogleMonitor(false);
    });
    const setup = {
      module,
      entity,
      bindData,
      flowRef,
      taskDrawerRef,
      showBasicConfig,
      saveTemplateRef,
      showSaveTemplateDlg,
      simulate,
      monitor,
    };
    provide("view", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.flow-view {
  height: calc(100vh - 160px);
  overflow: hidden;
}
</style>
