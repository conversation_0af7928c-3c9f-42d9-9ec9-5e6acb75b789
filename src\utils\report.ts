const getLineNames = (bucket: any) => {
  let names = new Set();
  bucket.line?.buckets.forEach(item => {
    names.add(item.key)
  });
  return names;
}

const getLines = (bucket: any) => {
  const result = {};
  bucket.line?.buckets.forEach(item => {
    result[item.key] = item.doc_count;
  });
  return result;
};

const getBars = (bucket: any) => {
  return {};
};

export const parseCharData = (data: any) => {
  // 访问聚合部分
  const { aggregations } = data;
  // 特定聚合的桶
  const { buckets } = aggregations.group;
  // 生成ECharts兼容的数据格式
  const chartData = buckets.map(bucket => {
    // 桶中可能有多个子桶，这里我们只处理第一个
    // const subBucket = bucket.nodeConfigId.buckets[0];
    return {
      date: bucket.key_as_string, // 日期
      docCount: bucket.doc_count, // 文档计数
      line_names: getLineNames(bucket),
      lines: getLines(bucket),
      bars: getBars(bucket),
      // nodeConfigIds: getSeries(bucket.nodeConfigId),
      // idCount: subBucket ? subBucket.doc_count.value : 0 // ID计数，如果子桶不存在则计数为0
    };
  });
  // ECharts图表数据格式
  const option = {
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      // {
      //   data: chartData.map(item => item.docCount),
      //   type: 'bar',
      //   name: '文档计数'
      // }, {
      //   data: chartData.map(item => item.idCount),
      //   type: 'line',
      //   name: 'ID计数'
      // }
    ]
  };

  chartData.lines.names.forEach(name => {
    option.series.push({
        data: chartData.lines.data[name].map(item => item.docCount),
        type: 'line',
        name: name
      })
  });

  chartData.bars.forEach(it => {
    option.series.push({
        data: it.map(item => item.docCount),
        type: 'bar',
        name: '文档计数'
      })
  });
  return option;
}

export function bar(data: string) {

}

export function line(data: string) {

}

