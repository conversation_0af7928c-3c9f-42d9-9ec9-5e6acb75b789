<template>
  <a-form :model="entity">
    <a-form-item label="提交说明" field="note">
      <a-textarea v-model="entity.note" placeholder="请输入提交审批说明" allow-clear/>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from "vue";

const isShowModel = ref(false);

const entity = ref({
  "note": ""
});
const show = () => {
  isShowModel.value = true;
};
const getData = () => {
  return entity.value;
};

defineExpose({
  show,
  getData
});
</script>
<style  lang="less" scoped>
</style>
