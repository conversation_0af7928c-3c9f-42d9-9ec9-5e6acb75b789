<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <a-button v-permission="['ma_menu.campaign_group.create']" type="primary" @click="addGroup()">
          <template #icon>
            <icon-plus />
          </template>
          {{t('campaign.button.create')}}
        </a-button>
      </template>
      <template #main>
        <a-table ref="table" :bordered="false" :data="dataSource" :pagination="false">
          <template #columns>
            <a-table-column :title="t('campaign.column.code')" data-index="id" />
            <a-table-column :title="t('campaign.column.name')" data-index="name" />
            <a-table-column :title="t('campaign.column.remark')" data-index="summary" :ellipsis="true" :tooltip="{class:'tooltip-content'}"/>
            <a-table-column :title="t('campaign.column.action')" align="center">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.campaign_group.view']" type="text" size="small" @click="view(record)">{{t('campaign.operation.view')}}</a-button>
                <a-button v-permission="['ma_menu.campaign_group.modify']" type="text" size="small"
                  @click="edit(record)">{{t('campaign.operation.edit')}}</a-button>
                <a-button v-permission="['ma_menu.campaign_group.delete']" type="text" size="small"
                  @click="deleteData(record.id)">{{t('campaign.operation.delete')}}</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>

    <!-- 新增分组 -->
    <AddGroupItemDlg ref="addGroupItemDlgRef" @change="bindData" />
  </div>
</template>

<script>
import { provide, ref, getCurrentInstance} from "vue";
import { useRouter } from "vue-router";
import { Modal } from "@arco-design/web-vue";
import { findGroupPage, deleteGroup } from "@/api/group";
import AddGroupItemDlg from "@/components/modal-dlg/add-group-dlg.vue";
import { useUserDataRoleStore } from "@/store";

export default {
  components: {
    AddGroupItemDlg
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      viewPath: "/campaign/campaign",
      showCard: false,
      breadcrumb: [
        {
          // name: "营销活动",
          name: t('campaign.title'),
          path: "/campaign/group"
        }
      ]
    });
    const filter = ref([
      {
        field: "name",
        // label: "活动名称",
        label: t('campaign.name'),
        component: "a-input",
        operate: "like",
        // placeholder: "请输入活动名称",
        placeholder: t('campaign.reminder.input_activity_name'),
        comment: true,
        value: ""
      }
    ]);
    const dataSource = ref([]);
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true
    });
    const formRef = ref({});
    const addGroupItemDlgRef = ref({});

    // 获取列表
    const bindData = async (expression) => {
      // let dataRole = useUserDataRoleStore().currentUserDataRole.map(
      //   (item) => item.id
      // );
      // if (expression) {
      //   expression = `${expression} AND dataRoles in '0,${dataRole.join(",")}'`;
      // } else {
      //   expression = `dataRoles in '0,${dataRole.join(",")}'`;
      // }
      const pageData = await findGroupPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1
        },
        {
          expression
        }
      );
      dataSource.value = pageData.content;
      pagination.value.total = pageData.totalElements;
    };

    // 新增数据
    const addGroup = () => {
      addGroupItemDlgRef.value.createEdit();
    };

    // 新增数据
    const edit = (item) => {
      addGroupItemDlgRef.value.createEdit(item);
    };
    // 新增数据
    const view = (item) => {
      router.push({ path: module.value.viewPath, query: { id: item.id } });
    };

    // 删除
    const deleteData = async (id) => {
      Modal.confirm({
        // title: "删除活动",
        title: t('campaign.reminder.delete_campaign'),
        // content: "删除之后数据不可恢复，请确认是否删除?",
        content: t('campaign.reminder.data_irretrievable_warning'),
        onOk: async () => {
          await deleteGroup(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page--;
          }
          await bindData();
        }
      });
    };

    const setup = {
      t,
      module,
      filter,
      formRef,
      dataSource,
      pagination,
      addGroupItemDlgRef,
      bindData,
      addGroup,
      edit,
      view,
      deleteData
    };
    provide("main", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped></style>
