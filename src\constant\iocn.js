/* eslint-disable import/prefer-default-export */
export const iconList = {
  title: "图标",
  icon: [
"icon-quzhong",
"icon-chaji",
"icon-jiaoji",
"icon-bingji",
"icon-xiaolian",
"icon-zhuangtai1",
"icon-order1",
"icon-dingtalk",
"icon-guanggaoguanli",
"icon-flow-stop",
"icon-kefu",
"icon-duanxin",
"icon-download",
"icon-ABtestshezhi",
"icon-jiaohuan",
"icon-cycle",
"icon-tuozhuai",
"icon-shangpu",
"icon-baobiaoxiazai",
"icon-gold",
"icon-daochujilu",
"icon-minsheng3",
"icon-kucun",
"icon-huodongzhongxin",
"icon-huiyuanquanyiguanli",
"icon-money1",
"icon-qiandaoguanli",
"icon-zhuceguanli",
"icon-xiansuo-daoyin",
"icon-batch",
"icon-fenlei",
"icon-store_dsp",
"icon-zuofei",
"icon-keylinetubiao-27",
"icon-database1",
"icon-chef",
"icon-zhuxiao",
"icon-changshangzengzhi",
"icon-icon-data-audit",
"icon-ribaotongji",
"icon-xinjiantongji",
"icon-aixin",
"icon-haoyou",
"icon-long-arrow",
"icon-try",
"icon-seckill",
"icon-purchase",
"icon-pintuan",
"icon-miaosha",
"icon-liwuhuodong",
"icon-sort-fill",
"icon-fenxi",
"icon-chaji2",
"icon-clock",
"icon-ma-filter",
"icon-ma-branch",
"icon-task-pad",
"icon-task-list",
"icon-mark",
"icon-start",
"icon-merge",
"icon-cross",
"icon-resolve",
"icon-split",
"icon-design",
"icon-elasticsearch",
"icon-debug-step-over",
"icon-light",
"icon-dark",
"icon-loading1",
"icon-money",
"icon--fuzhizujian",
"icon-checked",
"icon-serve",
"icon-full-screen1",
"icon-min-screen1",
"icon-min-screen",
"icon-full-screen",
"icon-more_channel",
"icon-tmall",
"icon-wechart",
"icon-shop",
"icon-jd",
"icon-wechart_mini",
"icon-out",
"icon-in",
"icon-level",
"icon-up-level",
"icon-hold",
"icon-basesalesupgradeSet",
"icon-level_rule",
"icon-point_rule",
"icon-guizeshezhi1",
"icon-coin",
"icon-member",
"icon-points",
"icon-level-LV",
"icon-node",
"icon-initial",
"icon-change-type",
"icon-output",
"icon-enter",
"icon--coupon-2",
"icon-approve",
"icon-rubbish",
"icon-cease",
"icon-process",
"icon-riqi",
"icon-riqi1",
"icon-riqi2",
"icon-text1",
"icon-suffix-ftp",
"icon-marketing",
"icon-intelligent",
"icon-basic",
"icon-probation",
"icon-platform-s",
"icon-easyweb-l",
"icon-mmc-s",
"icon-platform-old",
"icon-cdp-l",
"icon-mail-l",
"icon-job-l",
"icon-phone-l",
"icon-company-l",
"icon-platform",
"icon-flow-l",
"icon-flow-s",
"icon-bdm-l",
"icon-bdm-s",
"icon-saas-l",
"icon-saas-s",
"icon-ad-s",
"icon-ad-l",
"icon-wework-l",
"icon-wework-s",
"icon-think-bi-s",
"icon-report",
"icon-wework",
"icon-ec-l",
"icon-campaign-l",
"icon-campaign-s",
"icon-loyalty-s",
"icon-loyalty-l",
"icon-scrm-l",
"icon-scrm-s",
"icon-yuyin",
"icon-icon-",
"icon-biaoqing",
"icon-phone-signal-full",
"icon-wifi",
"icon-electricity-full",
"icon-icon-more",
"icon-icon-left",
"icon-key",
"icon-control-textbox",
"icon-control-select",
"icon-layer",
"icon-wake",
"icon-mini",
"icon-location",
"icon-template",
"icon-touch",
"icon-xiaoxiduilie",
"icon-shijian",
"icon-xml",
"icon-mongo_db",
"icon-json1",
"icon-huancun",
"icon-date",
"icon-json",
"icon-number",
"icon-code",
"icon-check",
"icon-caret-up",
"icon-caret-down",
"icon-appstore",
"icon-dengchu",
"icon-lishijilu",
"icon-mima",
"icon-home",
"icon-gengduo",
"icon-zanting",
"icon-list",
"icon-cuowu",
"icon-zhengque",
"icon-branch",
"icon-bianliangfuzhi",
"icon-trend",
"icon-zhuti",
"icon-zhinan",
"icon-zhuti1",
"icon-kuaisu",
"icon-zujian",
"icon-yingshe",
"icon-reset",
"icon-peixun",
"icon-RabbitMQ",
"icon-mySql",
"icon-MongoDB1",
"icon-ziliao",
"icon-http",
"icon-ziliucheng",
"icon-api",
"icon-error",
"icon-prompt",
"icon-success",
"icon-warning",
"icon-arrow-double-left",
"icon-arrow-double-right",
"icon-arrow-left-bold",
"icon-arrow-right-bold",
"icon-daochu",
"icon-daoru",
"icon-icon4",
"icon-yanzheng",
"icon-daibandengdaishenhe",
"icon-tuihuo",
"icon-rate",
"icon-to-ship",
"icon-shipped",
"icon-to-pay",
"icon-copy",
"icon-ec-on-sale",
"icon-xiansuojilu",
"icon-zhuangtai",
"icon-jinyong",
"icon-more-action",
"icon-empty",
"icon-folder",
"icon-radio",
"icon-checkbox",
"icon-del",
"icon-attribute",
"icon-struct",
"icon-loyalty-gift",
"icon-coupon",
"icon-order",
"icon-star",
"icon-promotion",
"icon-store",
"icon-inventory",
"icon-goods",
"icon-ec-sales-category",
"icon-ec-promotion",
"icon-feature",
"icon-align-center",
"icon-align-right",
"icon-align-left",
"icon-chart-map",
"icon-chart-scatter",
"icon-chart-metric",
"icon-chart-bar",
"icon-chart-line",
"icon-chart-radar",
"icon-chart-pie",
"icon-chart-gauge",
"icon-chart-area",
"icon-add",
"icon-delete-item",
"icon-metric",
"icon-dimension",
"icon-filter",
"icon-title",
"icon-palette",
"icon-coordinate",
"icon-legend",
"icon-padding",
"icon-delete",
"icon-connect",
"icon-chart",
"icon-tip",
"icon-host",
"icon-server",
"icon-dashboard",
"icon-material",
"icon-fans",
"icon-qrcode",
"icon-wechat-menu",
"icon-auto-reply",
"icon-menu",
"icon-logout",
"icon-campaign",
"icon-message",
"icon-config",
"icon-app-center",
"icon-refresh",
"icon-modify",
"icon-save",
"icon-wechat",
"icon-car",
"icon-organization",
"icon-goback",
"icon-user",
"icon-create",
"icon-app-loyalty",
"icon-app-bi",
"icon-app-campaign",
"icon-app-scrm",
"icon-more",
"icon-app-cdp",
"icon-forward",
"icon-user-info",
"icon-selected",
"icon-management",
"icon-service",
"icon-route",
"icon-disk",
"icon-bag",
"icon-role",
"icon-plug",
"icon-upload",
"icon-manual",
"icon-timing",
"icon-category",
"icon-mass-send",
"icon-tag",
"icon-tags",
"icon-btn-remove",
"icon-btn-add",
"icon-tag-group",
"icon-btn-mod",
"icon-detail",
"icon-license",
"icon-execute",
"icon-client",
"icon-application-license",
"icon-btn-del",
"icon-event",
"icon-password",
"icon-news",
"icon-video",
"icon-text",
"icon-voice",
"icon-image",
"icon-link",
"icon-website",
"icon-microservice",
"icon-arrowdown",
"icon-arrowup",
"icon-commit",
"icon-list-start",
"icon-pause",
"icon-app-ec",
"icon-app-platform",
"icon-import",
"icon-database",
"icon-collection-info",
"icon-table",
"icon-securityGroup",
"icon-flow",
"icon-ad",
"icon-arrowright",
"icon-ec-goods",
"icon-quit",
"icon-reload",
"icon-next",
"icon-prev",
"icon-search",
"icon-first",
"icon-last"
  ]
};
