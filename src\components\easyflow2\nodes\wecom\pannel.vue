<template>
    <div class="easyflow-pannel-wechat">
        <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
        </a-form>
        <a-form-item label="企微沟通模板" class="form-item-select">
            <a-select v-model="entity.capabilityId" class="easyflow-select" placeholder="请选择企微沟通模板" :loading="loading"
                allow-search @change="showContent(entity.capabilityId)">
                <a-option v-for="item of communicates" :key="item.id" :label="item.label" :value="item.id">
                    [{{ item.name }}] <span class="light-text"> {{ item.id }} </span>
                </a-option>
            </a-select>
        </a-form-item>
    </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { wechatMsgTypes } from "@/constant/capability";
import { findCommunicateList, findCommunicateItem } from "@/api/communicate";
import { findBudgetItemList } from "@/api/budget";
import AddBudgetItemDlg from "@/components/modal-dlg/add-budget-item-dlg.vue";

const props = defineProps(["node", "easyflow"]);
const { node } = props;
const { easyflow } = props;
const flowIjt = inject("flow");
const budgetId = ref(flowIjt.flowId);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const entity = ref({
    capabilityId: null,
    budgetSetting: {
        enabled: false,
        budgetId: budgetId.value
    },
    frequencyLimit: true
});
const content = ref("");
const communicates = ref([]);
const budgets = ref([]);
const bindNames = ref([]);
const unitBudget = ref(0);
const addBudgetItemRef = ref(null);
const loading = ref(false);

const handleSearchCommunicate = async (name) => {
    loading.value = true;
    const params = {
        fields: "name,content,budgetSetting",
        expression: `setting.type eq wecom AND status eq ENABLED`
    };
    if (name) {
        params.expression += ` AND name like ${name}`;
    }
    communicates.value = await findCommunicateList(params);
    communicates.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
    loading.value = false;
};

const handleChangeWechatType = async () => {
  entity.value.capabilityId = null;
  handleSearchCommunicate();
};

const save = () => {
    return entity.value;
};

defineExpose({
    save
});
onMounted(() => {
    Object.assign(entity.value, node.data);
    handleSearchCommunicate();
    // handleSearchBudget();
    // getBindNames();
});

</script>

<style lang="less" scoped>
.easyflow-pannel-wechat {
    .wechat-content {
        color: #b3b3b3;
    }

    .budget {
        color: rgb(var(--primary-6));

        .tip {
            color: #b3b3b3;
        }
    }
}
</style>