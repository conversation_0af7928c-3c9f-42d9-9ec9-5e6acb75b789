import i18n from "../locale";

export const taskTypes = [
  // { value: "audience_compare", label: "人群比较任务" },
  // { value: "capability_limit_check", label: "人群投放限制验证任务" },
  // { value: "flow_monitor", label: "活动监控任务" },
  // { value: "flow_append", label: "活动人群追加任务" },
  // { value: "export_task_record", label: "导出流程节点记录" },
  // { value: "export_task_chain_record", label: "导出流程链路触达记录" },
  // { value: "export_campaign_reach_report", label: "导出活动报表记录" },
  // { value: "export_task_reach_record", label: "导出流程节点触达记录" },

  { value: "audience_compare", label: i18n.global.t('global.task.type.audienceComparisonTask') },
  { value: "capability_limit_check", label: i18n.global.t('global.task.type.audienceDeliveryLimitationVerificationTask') },
  { value: "flow_monitor", label: i18n.global.t('global.task.type.activityMonitoringTask') },
  { value: "flow_append", label: i18n.global.t('global.task.type.activityAudienceAppendTask') },
  { value: "export_task_record", label: i18n.global.t('global.task.type.exportProcessNodeRecord') },
  { value: "export_task_chain_record", label: i18n.global.t('global.task.type.exportProcessTouchRecord') },
  { value: "export_campaign_reach_report", label: i18n.global.t('global.task.type.exportActivityReportRecord') },
  { value: "export_task_reach_record", label: i18n.global.t('global.task.type.exportProcessNodeTouchRecord') },
]

export const taskStatus = [
  // { label: "提交", value: "SUBMITTED", color: "blue" },
  // { label: "待运行", value: "READY", color: "green" },
  // { label: "成功", value: "SUCCESS", color: "green" },
  // { label: "运行中", value: "RUNNING", color: "lime" },
  // { label: "失败", value: "ERROR", color: "red" },
  // { label: "已过期", value: "EXPIRED", color: "gold" },
  { label: i18n.global.t("global.task.status.submitted"), value: "SUBMITTED", color: "blue" },
  { label: i18n.global.t("global.task.status.ready"), value: "READY", color: "green" },
  { label: i18n.global.t("global.task.status.success"), value: "SUCCESS", color: "green" },
  { label: i18n.global.t("global.task.status.running"), value: "RUNNING", color: "lime" },
  { label: i18n.global.t("global.task.status.error"), value: "ERROR", color: "red" },
  { label: i18n.global.t("global.task.status.expired"), value: "EXPIRED", color: "gold" },
]
