<script lang="tsx">
import { defineComponent, ref, h, compile, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, RouteRecordRaw, RouteRecordNormalized } from "vue-router";
import { useAppStore, useBussinessUnitStore, useMenuItemState } from "@/store";
import usePermission from "@/hooks/permission";
import { listenerRouteChange } from "@/utils/route-listener";
import axios from "axios";

export default defineComponent({
  emit: ["collapse"],
  setup() {
    const { t } = useI18n();
    const appStore = useAppStore();

    const useBUStore = useBussinessUnitStore();
    const permission = usePermission();
    const router = useRouter();
    const collapsed = computed({
      get() {
        if (appStore.device === "desktop") return appStore.menuCollapse;
        return false;
      },
      set(value: boolean) {
        appStore.updateSettings({ menuCollapse: value });
      },
    });

    const channelData: any = ref([])

    // 活动沟通管理 菜单

    const userBussinessUnitStore = useBussinessUnitStore();
    const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
    const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

    if (useBUStore.marketingCenter.status !== 'ONLINE') {
      router.push({
        path: "/init",
      });
      return
    }

    axios.get(`/api/ma-manage/${tenantId}/${buCode}/common/menu`).then((res: any) => {
      channelData.value = res
    })

    const appRoute = computed(() => {
      const routerData = router
        .getRoutes()
        .find((el) => el.name === "root") as RouteRecordNormalized;
      return routerData
    });
    const menuList = computed(() => {
      const copyRouter = JSON.parse(JSON.stringify(appRoute.value.children));
      copyRouter.sort((a: RouteRecordNormalized, b: RouteRecordNormalized) => {
        return (a.meta.order || 0) - (b.meta.order || 0);
      });
      function travel(_routes: RouteRecordRaw[], layer: number) {
        if (!_routes) return null;
        const collector: any = _routes.map((element) => {
          // no access
          if (!permission.accessRouter(element)) {
            return null;
          }

          // leaf node
          if (!element.children) {
            return element;
          }

          // route filter hideInMenu true
          element.children = element.children.filter(
            (x) => x.meta?.hideInMenu !== true
          );

          // Associated child node
          const subItem = travel(element.children, layer);
          if (subItem.length) {
            element.children = subItem;
            return element;
          }
          // the else logic
          if (layer > 1) {
            element.children = subItem;
            return element;
          }
          if (element.meta?.hideInMenu === false) {
            return element;
          }

          return null;
        });

        return collector.filter(Boolean);
      }
      return travel(copyRouter, 0);
    });

    // 处理数据
    const menuTree = computed(() => {
      const list = []

      menuList.value.forEach((item: any) => {
        if (item.name === 'reach') {
          const userAuthInfo: any = localStorage.getItem('userAuthInfo') || "{}"
          const {authorities, menus } = JSON.parse(userAuthInfo)

          const  isDynamic = item.children.some((x: any) => x.name === "dynamic")

          if (!isDynamic) {
            item.children = item.children.filter((x: any) => x.name !== "dynamic")
          } else {

            channelData.value.forEach((child: any) => {
              item.children = item.children.filter((x: any) => x.name !== "dynamic")
              // if ([...authorities, ...menus].includes(`ma.flow-content.${child?.id}`)) {
              //     item.children.push({
              //     path: `/${item.name}/dynamic/${child?.id}`,
              //     name: `dynamic${child?.id}`,
              //     meta: {
              //       isPath: true,
              //       id: child?.id,
              //       title: child?.name,
              //       locale: child?.name,
              //       requiresAuth: true,
              //       hideInMenu: true,
              //       roles: ["ma.flow-content"],
              //     },
              //   })
              // }
              // console.log(item.children)
              item.children.push({
                  path: `/${item.name}/dynamic/${child?.id}`,
                  name: `dynamic${child?.id}`,
                  meta: {
                    isPath: true,
                    id: child?.id,
                    title: child?.name,
                    locale: child?.name,
                    requiresAuth: true,
                    hideInMenu: true,
                    roles: ["ma.flow-content", "ma_menu.flow-content"],
                  },
                })

            })
          }
        }
        list.push(item)
      });
      return menuList.value
    })

    // In this case only two levels of menus are available
    // You can expand as needed

    const selectedKey = ref<string[]>([]);
    const goto = (item: RouteRecordRaw) => {
      const menuItem = useMenuItemState()
      menuItem.setMenuItem(item)
      if (item?.meta?.isPath) {
        router.push({
          path: item.path,
        });
      } else {
        router.push({
          name: item?.name,
        });
      }
    };
    listenerRouteChange((newRoute) => {
      if (newRoute.meta.requiresAuth && !newRoute.meta.hideInMenu) {
        const key = newRoute.matched[2]?.name as string;
        // 特殊处理 沟通管理
        if (key === 'dynamic') {
          selectedKey.value = [key + newRoute.params.id];
        } else{
          selectedKey.value = [key];
        }

      }
    }, true);
    const setCollapse = (val: boolean) => {
      if (appStore.device === "desktop")
        appStore.updateSettings({ menuCollapse: val });
    };
    const renderSubMenu = () => {
      function travel(_route: RouteRecordRaw[], nodes = []) {
        if (_route) {
          _route.forEach((element) => {
            // This is demo, modify nodes as needed
            const icon = element?.meta?.icon
              ? `<${element?.meta?.icon}/>`
              : element?.meta?.iconFont
              ? `<span class="iconfont ${element?.meta?.iconFont}"></span>`
              : ``;
            let r = (
              <a-sub-menu
                key={element?.name}
                v-slots={{
                  icon: () => h(compile(icon)),
                  title: () => h(compile(t(element?.meta?.locale || ""))),
                }}
              >
                {element?.children?.map((elem) => {
                  return (
                    <a-menu-item key={elem.name} onClick={() => goto(elem)}>
                      {elem?.meta?.title || t(elem?.meta?.locale || "")}
                      {travel(elem.children ?? [])}
                    </a-menu-item>
                  );
                })}
              </a-sub-menu>
            );

            if (element?.children?.length === 1) {
              r = (
                <div>
                  {element?.children?.map((elem) => {
                    return (
                      <a-menu-item
                        key={elem.name}
                        v-slots={{
                          icon: () => h(compile(icon)),
                          title: () => h(compile(element?.meta?.locale || "")),
                        }}
                        onClick={() => goto(elem)}
                      >
                        {elem?.meta?.title || t(elem?.meta?.locale || "")}
                        {travel(elem.children ?? [])}
                      </a-menu-item>
                    );
                  })}
                </div>
              );
            }

            nodes.push(r as never);
          });
        }
        return nodes;
      }
      return travel(menuTree.value);
    };
    return () => (
      <a-menu
        v-model:collapsed={collapsed.value}
        show-collapse-button={appStore.device !== "mobile"}
        auto-open={false}
        selected-keys={selectedKey.value}
        auto-open-selected={true}
        level-indent={34}
        style="height: 100%"
        onCollapse={setCollapse}
      >
        {renderSubMenu()}
      </a-menu>
    );
  },
});
</script>

<style lang="less" scoped>
:deep(.arco-menu-inner) {
  .arco-menu-inline-header {
    display: flex;
    align-items: center;
  }

  .arco-icon {
    &:not(.arco-icon-down) {
      font-size: 18px;
    }
  }
}
</style>
