import { Graph } from "@antv/x6";
import { ref } from "vue";
import { getPorts, rect } from "@/components/easyflow2/components/node";
import DynamicReachNode from "./node.vue";
import DynamicReachPannel from "./pannel.vue";
import Help from "./help.vue";

let nodeItem: any = {};
const nodeData = {
  type: "dynamic_reach",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<DynamicReachNode :nodeItem='nodeItem' />`,
      components: {
        DynamicReachNode,
      },
      data() {
        return {
          nodeItem: nodeItem,
        };
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("DynamicReachNode", nodeData.node, true);
};

const changeItem = (id: any) => {
  let nodelist: any = JSON.parse(localStorage.getItem("nodeItem") || '') || {};
  nodeItem = nodelist[id];
};

const DynamicReach = {
  type: "dynamic_reach",
  name: "动态触达",
  shape: "DynamicReachNode",
  iconClass: "icon-event",
  color: "#ffffff",
  themebg: "#39BCC5",
  skippable: false,
  pannel: DynamicReachPannel,
  help: Help,
  registerNode,
  nodeData,
  changeItem,
  auth: [
    "export_task_reach_record"
  ]
};

export default DynamicReach;
