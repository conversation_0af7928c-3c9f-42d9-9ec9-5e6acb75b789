import { createApp } from 'vue';
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import globalComponents from '@/components';
import VueGridLayout from "vue-grid-layout";
import router from "./router";
import store from "./store";
import i18n from "./locale";
import directive from "./directive";
import moment from "./plugin/moment";
import "./mock";
import App from "./App.vue";
import "@arco-design/web-vue/dist/arco.css";
import "@/assets/style/global.less";
import "@antv/x6-vue-shape";
import "@/api/interceptor";

const app = createApp(App);
app.config.globalProperties.t = i18n.global.t;
app.use(VueGridLayout)
app.use(ArcoVue);
app.use(ArcoVueIcon);
app.use(moment);
app.use(router);
app.use(store);
app.use(i18n);
app.use(globalComponents);
app.use(directive);

app.mount("#app");
