/**
*by:<EMAIL> on 2022/7/4 0004
*/
<template>
  <div class="chart-columnar">
    <Chart height="310px" :option="chartOption" />
  </div>
</template>

<script>
import {defineComponent, ref, computed} from 'vue';
import useChartOption from '@/hooks/chart-option';

export default defineComponent({
  name: 'chart-columnar',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    range: {
      type: Array,
      default: () => [],
    },
    rangeSize: {
      type: Number,
      default: 5,
    },
    avg: {
      type: Number,
      default: 0,
    },
    title: {
      type: String,
      default: "RFM 人群分布",
    },
    flag: {
      type: String,
      default: "LV",
    },
    subtitle: {
      type: String,
      default: "RFM 人群分布",
    },
    icon: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const render = ref(true)
    const chart = ref(null)
    const rangeData = ref({})
    const rangeValue = ref([])

    const rangeList = computed(() => {
      const range = [];
      for (let key in props.range) {
        range.push(props.range[key]);
      }
      return range;
    })

    const { chartOption } = useChartOption((isDark) => {
      const xAxis = [];
      for (let i = 0; i < props.rangeSize; i++) {
        xAxis.push(`${props.flag}${i + 1}${props.range[i] === null ? "计算中" : ""}`);
      }

      const seriesData = []
      rangeList.value.forEach(item => {
        seriesData.push({
          value: item,
          itemStyle: {
            color: item >= props.avg ? "#91cc75" : "#fac858"
          }
        })
      })

      return {
        title: {
          text: props.title,
          subtext: props.subtitle,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        gradientColor: ["#f6efa6", "#d88273", "#bf444c"],
        xAxis: [
          {
            type: "category",
            data: xAxis,
            axisTick: {
              alignWithLabel: true,
            },
            axisLine: {
              lineStyle: {
                type: "dashed",
                color: "#999999",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "#ebebeb",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "#ebebeb",
              },
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#999999",
              },
            },
          },
        ],
        // visualMap: {
        //   orient: 'horizontal',
        //   left: 'center',
        //   min: 0,
        //   max: 1,
        //   text: ['High Score', 'Low Score'],
        //   // Map the score column to color
        //   dimension: 0,
        //   inRange: {
        //     color: ['#65B581', '#FD665F']
        //   }
        // },
        series: [
          {
            name: props.subtitle,
            type: "bar",
            barWidth: "60%",
            data: seriesData,
            // markLine: {
            //   data: [{
            //     type: 'average', name: '平均值',
            //     yAxis: props.avg,
            //     label: {
            //       position: 'insideEndTop', // 表现内容展示的位置
            //       formatter: '{b}: {c}',
            //       backgroundColor: 'rgb(182,177,0.3)',
            //       color: '#fff',
            //       padding: [5, 10, 5, 10],
            //     }
            //   }]
            // },
            itemStyle: {
              normal: {
                color: function (params) {
                  const color = [
                    "#5470c6",
                    "#91cc75",
                    "#fac858",
                    "#ee6666",
                    "#73c0de",
                    "#3ba272",
                    "#fc8452",
                    "#9a60b4",
                    "#ea7ccc",
                  ];
                  return color[params.dataIndex];
                },
              },
            },
          },
        ],
      }
    });
    return {
      rangeList,
      render,
      chart,
      rangeData,
      rangeValue,
      chartOption
    }
  },
});
</script>
<style lang="less" scoped>
.chart-columnar {
}
</style>
