/**
 * 分组管理接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function syncAllTemplate() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/flow-template/sync-all`;
  return axios.post(uri);
}
export function syncOneTemplate(nodeConfigId: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/flow-template/sync/${nodeConfigId}`;
  return axios.post(uri);
}
export function createTemplate(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/flow-template`;
  return axios.post(uri, info);
}

export function modifyTemplate(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/flow-template`;
  return axios.put(uri, info);
}

export function deleteTemplate(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/flow-template/${id}`
  );
}

export function getTemplate(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-template/${id}`);
}

export function findTemplateList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-template/list`, {
    params
  });
}

export function findTemplatePage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-template`, {
    params: {
      ...params,
      ...query
    }
  });
}

export function findTemplateView(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-template/view`, {
    params: {
      ...params,
      ...query
    }
  });
}

export function aiGenerateInfo(info: any) {
  const uri = `/chat/scene`;
  return axios.post(uri, info);
}