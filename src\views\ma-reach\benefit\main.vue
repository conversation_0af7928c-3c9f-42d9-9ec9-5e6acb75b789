<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <a-button v-permission="['ma_menu.benefit.create']" type="primary" @click="showSelectDlg">
          <template #icon>
            <icon-plus />
          </template>
          新建
        </a-button>
      </template>
      <template #main>
        <a-table ref="table" :bordered="false" :data="dataSource" :pagination="false" @page-change="onPageChange"
          @selection-change="onSelect">
          <template #columns>
            <a-table-column title="名称" data-index="name" :sortable="{ sortDirections: ['ascend', 'descend'] }" />
            <a-table-column title="类型" data-index="templateType" :sortable="{ sortDirections: ['ascend', 'descend'] }">
              <template #cell="{ record }">
                {{ typeFilter(record.type) }}
              </template>
            </a-table-column>
            <a-table-column title="渠道" data-index="status">
              <template #cell="{ record }">
                {{ channelFilter(record.channelId) }}
              </template>
            </a-table-column>
            <a-table-column title="状态" data-index="status" :sortable="{ sortDirections: ['ascend', 'descend'] }">
              <template #cell="{ record }">
                {{ statusFilter(record.status) }}
              </template>
            </a-table-column>
            <a-table-column title="创建时间" data-index="createdTime" :sortable="{ sortDirections: ['ascend', 'descend'] }">
              <template #cell="{ record }">
                {{ $moment(record.createdTime).format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </a-table-column>
            <a-table-column title="操作" align="center">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.benefit.modify']" type="text" size="small" @click="detail(record)">编辑</a-button>
                <!-- <a-button type="text" size="small" @click="toStatement(record.id)">报表</a-button> -->
                <a-button v-permission="['ma_menu.benefit.delete']" type="text" size="small" @click="deleteData(record)">删除</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
    <!-- 新增弹窗 -->
    <capabilitySelectorDlg ref="csDlgRef" :prefix-path="module.editPath" :model-data="modelData" />
  </div>
</template>

<script>
import { ref, provide, computed } from "vue";
import { useRouter } from "vue-router";
import Pagination from "@/utils/pagination";
import { findBenefitPage, deleteBenefitItem, findChannelList } from "@/api/benefit";
import { Modal } from "@arco-design/web-vue";
import { filters } from "@/utils/filter"
import { addExp } from "@/utils/common";
import { capabilityStatus } from "@/constant/capability"
import CapabilitySelectorDlg from "@/components/modal-dlg/capability-selector-dlg.vue";
import reference from "@/components/reference/index.vue"

export default {
  components: {
    CapabilitySelectorDlg,
    reference
  },
  setup() {
    // 路由API
    const router = useRouter();
    // 模块设置
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: "权益模板管理",
        },
      ],
      editPath: "/reach/benefit/",
      statementPath: "/reach/benefit/statement",
    });
    // 分页设置
    const pagination = ref(Pagination);
    // 过滤设置
    const filter = ref([
      {
        field: "name",
        label: "权益名称",
        component: "a-input",
        operate: "like",
        placeholder: "请输入权益名称",
        comment: true,
        value: "",
      },
      {
        field: "type",
        label: "权益类型",
        component: "a-select",
        operate: "eq",
        dataSource: [
          { value: "coupon", label: "卡券" },
          { value: "points", label: "积分" },
          { value: "benefit", label: "权益" },
        ],
        placeholder: "请选择数据源",
        value: "",
      },
      {
        field: "status",
        label: "状态",
        component: "a-select",
        operate: "eq",
        dataSource: [
          { value: "true", label: "启用" },
          { value: "false", label: "禁用" },
        ],
        placeholder: "请选择状态",
        value: "",
      },
    ]);

    const typeFilter = (v) => {
      switch (v) {
        case "coupon":
          return "卡券";
        case "points":
          return "积分";
        case "benefit":
          return "权益";
        default:
          return "-";
      }
    };

    const statusFilter = (v) => {
      return filters(capabilityStatus, v)
    };

    const csDlgRef = ref(null);
    // 数据设置
    const entity = ref({});
    const channelList = ref([]);
    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    // 查询API
    const bindData = async (expression) => {
      channelList.value = await findChannelList({ fields: "name" });
      const realExpression = addExp(expression, "category eq benefit");
      entity.value = await findBenefitPage(
        {
          ...pagination.value,
          sort: "createdTime,DESC",
          page: pagination.value.page - 1,
        },
        {
          expression: realExpression,
          fields: "name,code,status,type,createdTime,channelId",
        }
      );
      pagination.value.total = entity.value.totalElements;
    };


    // 详情跳转
    const detail = (row) => {
      const query = {};
      query[module.value.entityIdField] = row[module.value.entityIdField];
      router.push({
        path: module.value.editPath + row.type,
        query,
      });
    };

    // 跳转报表
    const toStatement = (id) => {
      router.push({ path: module.value.statementPath, query: { id } });
    };

    // 新建弹窗
    const showSelectDlg = () => {
      csDlgRef.value.show();
    };

    const modelData = [
      { title: "卡券", type: "coupon" },
      { title: "积分", type: "points" },
      // { title: "权益", type: "benefit" },
    ];

    const deleteData = async (record) => {
      Modal.confirm({
        title: "删除模板",
        content: "删除之后数据不可恢复，请确认是否删除?",
        onOk: async () => {
          await deleteBenefitItem(record.id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1;
          }
          await bindData();
        },
      });
    };
    // 选中回调
    const onSelect = (v) => {
      console.log("当前勾选内容：", v);
    };

    const channelFilter = (id) => {
      const item = channelList.value.find((item) => {
        return item.id === id;
      });
      return item?.name || "";
    };

    const setup = {
      module,
      filter,
      entity,
      detail,
      bindData,
      onSelect,
      dataSource,
      pagination,
      typeFilter,
      statusFilter,
      deleteData,
      showSelectDlg,
      csDlgRef,
      modelData,
      channelList,
      channelFilter,
      toStatement,
    };
    provide("main", setup);
    return setup;
  },
  data() {
    return {
      rowSelection: {
        type: "checkbox",
        showCheckedAll: true,
      },
    };
  },
  methods: {
    onPageChange(current) {
      this.entity = this.bindData();
    },
  },
};
</script>

<style scoped lang="less">
.arco-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.arco-radio {
  width: calc(50% - 10px);
  margin-bottom: 20px;

  &:nth-child(2n) {
    margin-right: 0;
  }
}

.custom-radio-card {
  border: 1px solid var(--color-border-2);
  padding: 10px 15px;
  width: 100%;
  min-height: 30px;
  border-radius: 4px;

  .custom-radio-card-title {
    font-size: 16px;
    color: #333333;
    font-width: bold;
  }

  .custom-radio-card-desc {
    font-size: 12px;
    color: #999999;
  }
}

.arco-radio-checked {
  .custom-radio-card {
    border-color: rgb(var(--primary-6));
  }

  .custom-radio-card-title {
    color: rgb(var(--primary-6));
  }
}
</style>
