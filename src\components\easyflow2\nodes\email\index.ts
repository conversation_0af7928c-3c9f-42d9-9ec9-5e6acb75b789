import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import EmailNode from "./node.vue";
import EmailPannel from "./pannel.vue";

const nodeData = {
  type: "email",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<EmailNode />`,
      components: {
        EmailNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("EmailNode", nodeData.node, true);
};

const Email = {
  type: "email",
  name: "邮件",
  shape: "EmailNode",
  iconClass: "icon-mail-l",
  color: "#ffffff",
  themebg: "#4594f3",
  registerNode: registerNode,
  pannel: EmailPannel,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Email;
