import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";
const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findBenefitPage(query?: QueryInfo, params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/benefit`,
    {
      params: {
        ...params,
        ...query,
      },
    }
  );
}

export function findBenefitList(params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/benefit/list`,
    {
      params: params,
    }
  );
}

export function findBenefitItem(id: String) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/benefit/${id}`
  );
}

export function saveBenefitInfo(info?: any) {
  return info.id
    ? axios.put(
        `/api/ma-manage/${tenantId}/${buCode}/benefit`,
        info
      )
    : axios.post(
        `/api/ma-manage/${tenantId}/${buCode}/benefit`,
        info
      );
}

export function deleteBenefitItem(id: String) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/benefit/${id}`
  );
}

export function findChannelList(params: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/marketing_channel/list`,
    {
      params: params,
    }
  );
}

export function simulateBenefitSend(info?: any) {
  info.tenantId = tenantId;
  info.buCode = buCode;
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/benefit/simulate`,
    info
  );
}
