<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { getFlow, findFlowStarts } from "@/api/campaign";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (data.flowId) {
        const flows = await getFlow(data.flowId);
        if (flows?.length > 0) {
          brief.push({ sort: 0, label: "Flow名称", value: flows[0].name });
        }
      }
      if (data.startId) {
        const starts = await findFlowStarts(data.flowId);
        const task = starts.find((it) => it.taskId === data.startId);
        brief.push({ sort: 1, label: "Flow节点", value: task.name });
      }
      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'flow' })
    const setup = {
      title: item.name || "Flow",
      summary: item.name || "Flow节点",
      iconClass: item.icon || "icon-flow",
      nodeClass: "easyflow-node-end",
      headerColor: item.themeColor|| "#00b42a",
      headerBgColor: item.themeColor || "#00b42a",
      background: item.background || "#e8ffea",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
