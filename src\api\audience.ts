import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

// Audience
export function findAudiencePage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/audience`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function findAudienceList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/audience/list`, {
    params,
  });
}

export function findAudienceItem(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/audience/${id}`);
}

export function saveInfo(info?: any) {
  return axios[info.id ? "put" : "post"](
    `/api/ma-manage/${tenantId}/${buCode}/audience`,
    info
  );
}

export function deleteItem(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/audience/${id}`);
}

export function syncAudienceFromDWH() {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/audience/sync-all`);
}

// 人群数据查询
export function findAudienceCustomerPage(
  query?: QueryInfo,
  params?: Params,
  audienceId?: any
) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/audience/${audienceId}/customers`,
    {
      params: {
        ...params,
        ...query,
      },
    }
  );
}

// 人群数据统计
export function countAudience(id: string) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/audience/${id}/count-customers`
  );
}

export function preCountAudience(data: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/audience/count-customers`,
    data
  );
}

// 人群快照查询
export function findSnapshotCustomerPage(
  query?: QueryInfo,
  snapshotId?: any
) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/audience/snapshot/${snapshotId}`,
    {
      params: {
        ...query,
      },
    }
  );
}

export function getCrowdSnapshot(params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/audience/snapshot`,
    {
      params,
    }
  );
}

export function createCompareJob(data: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/audience/compare`,
    data
  );
}

// Cutomer

export function getCustomerItem(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/customer/${id}`);
}

export function findCustomerPage(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/customer`, {
    params,
  });
}

// Tag
export function findTagPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/customer-tag`, {
    params: {
      ...params,
      ...query,
    },
  });
}
export function findTagList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/customer-tag/list`, {
    params,
  });
}

export function findTagItem(id?: any) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/customer-tag/${id}`);
}

export function findCustomerByPurpose(purpose: string, value: string) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/customer/purpose/${purpose}?value=${value}`
  );
}
