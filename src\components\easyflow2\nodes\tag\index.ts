import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import TagNode from "./node.vue";
import TagPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "tag",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<TagNode />`,
      components: {
        TagNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("TagNode", nodeData.node, true);
};

const Tag = {
  type: "tag",
  name: "客户标签",
  shape: "TagNode",
  iconClass: "icon-tag",
  color: "#00b42a",
  themebg: "#e8ffea",
  registerNode: registerNode,
  pannel: TagPannel,
  help: Help,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Tag;
