<template>
  <div class="easyflow-pannel-flow">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="Flow流程" :content-flex="false">
        <a-select v-model="entity.flowId" style="margin-right: 10px" class="easyflow-select" placeholder="请选择Flow流程"
          :loading="loading" :filter-option="false" @search="handleSearchFlow" @change="handleSearchFlowStarts">
          <a-option v-for="item of flows" :key="item.id" :value="item.id">{{item.name}}</a-option>
        </a-select>

        <a-select v-if="entity.flowId" v-model="entity.startId" style="margin: 10px 10px 0 0" class="easyflow-select"
          placeholder="请选择Flow流程开始节点" :loading="loading" :filter-option="false">
          <a-option v-for="item of starts" :key="item.taskId" :value="item.taskId">{{ item.name }}</a-option>
        </a-select>
        可以将MA模型导入到Flow中
        <i class="iconfont icon-copy copy-btn" @click="copyModel">点此复制模型</i>
      </a-form-item>

      <a-form-item label="Flow流程结果" :content-flex="false">
        <div style="margin-bottom: 10px">
          <a-switch v-model="entity.payloadEnable" type="round"> </a-switch>
          <span style="margin-left: 10px">是否保留Flow流程结果</span>
        </div>
        <a-input v-if="entity.payloadEnable" v-model="entity.payloadFieldName"
          placeholder="请输入Flow流程结果将保存到哪个字段"></a-input>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { Notification } from "@arco-design/web-vue";
import { findFlowList, findFlowStarts, getFlowModelsByIds2 } from "@/api/campaign";

const {node, easyflow} = defineProps(["node", "easyflow"]);
const loading = ref(false);
const pannelInject = inject("pannel");
const {editEnable} = pannelInject;
const entity = ref({
  payloadEnable: false,
});
const flows = ref([]);
const starts = ref([]);
const save = () => {
  return entity.value;
};

const handleSearchFlow = async (name) => {
  loading.value = true;
  const params = { fields: "name" };
  params.expression = "status eq ENABLED";
  if (name) {
    params.expression += `AND name like ${name}`;
  }
  flows.value = await findFlowList(params);
  loading.value = false;
};

const handleSearchFlowStarts = async () => {
  loading.value = true;
  starts.value = await findFlowStarts(entity.value.flowId);
  loading.value = false;
};

const copyModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const modelIds = easyflow.getModels(nodeChain);
  const flowModel = await getFlowModelsByIds2(modelIds);

  const input = document.createElement("input");
  input.value = JSON.stringify(flowModel.fields);
  document.body.appendChild(input);
  input.select(); // 选中输入框中的内容
  document.execCommand("Copy"); // 执行复制操作
  document.body.removeChild(input);
  Notification.info({
    title: "复制成功",
  });
};

defineExpose({
  save,
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  handleSearchFlow();
  if (entity.value.flowId) {
    handleSearchFlowStarts();
  }
});
</script>


<style lang="less" scoped>
.easyflow-pannel-flow {
  .flow-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 10px 0;
  }

  .copy-btn {
    color: rgb(var(--primary-6));
    cursor: pointer;
  }

  .arco-icon-delete {
    cursor: pointer;
  }
}
</style>
