<template>
  <!-- 新增弹窗 -->
  <a-modal v-model:visible="visible" @ok="handleOk">
    <template #title>能力类型</template>
    <a-radio-group v-model="selectType" :default-value="0">
      <template v-for="item in modelData" :key="item.title">
        <a-radio :value="item.type">
          <template #radio>
            <div class="custom-radio-card">
              <div className="custom-radio-card-title">{{ item.title }}</div>
            </div>
          </template>
        </a-radio>
      </template>
    </a-radio-group>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";

const props = defineProps(["prefixPath", "modelData"]);
const router = useRouter();

const visible = ref(false);
const selectType = ref(0);

const handleOk = () => {
  router.push({ path: props.prefixPath + selectType.value });
};

const show = () => {
  visible.value = true;
};
defineExpose({ show });
</script>

<style scoped lang="less">
.custom-radio-card {
  border: 1px solid var(--color-border-2);
  padding: 10px 15px;
  width: 100%;
  min-height: 30px;
  border-radius: 4px;

  .custom-radio-card-title {
    font-size: 16px;
    color: #333333;
    font-weight: bold;
  }

  .custom-radio-card-desc {
    font-size: 12px;
    color: #999999;
  }
}

.arco-radio-checked {
  .custom-radio-card {
    border-color: rgb(var(--primary-6));
  }

  .custom-radio-card-title {
    color: rgb(var(--primary-6));
  }
}
</style>
