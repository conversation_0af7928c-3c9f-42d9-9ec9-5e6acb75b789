<template>
  <module edit>
    <template #main>
      <a-form ref="formRef" layout="vertical" class="general-form" :model="entity">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row :gutter="80">
              <a-col :span="12">
                <a-form-item :label="t('systemSetting.communicateChannel.channelName')" :rules="[{ required: true, message: t('systemSetting.communicateChannel.smsContent.QSRQDMC') }]" field="name">
                  <a-input v-model="entity.name" :placeholder="t('systemSetting.communicateChannel.smsContent.QSRQDMC')" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :label="t('systemSetting.communicateChannel.smsContent.duanXinLeiXing')" :rules="[{ required: true, message: t('systemSetting.communicateChannel.smsContent.QXZDXLX') }]" field="setting.type">
                  <a-select v-model="entity.setting.type" :placeholder="t('systemSetting.communicateChannel.smsContent.QXZDXLX')">
                  <a-option v-for="item of smsTypes" :key="item.id" :label="item.label" :value="item.id" />
                </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :rules="[{ required: true, message: t('systemSetting.communicateChannel.smsContent.QXZYHSJHZD') }]" field="setting.customerField">
                  <template #label>
                    {{t('systemSetting.communicateChannel.smsContent.YHSJHZD')}}
                    <a-tooltip position="right" :content="t('systemSetting.communicateChannel.smsContent.YHZGQDXDWYBSZD')">
                      <icon-info-circle />
                    </a-tooltip>
                  </template>
                  <a-tree-select v-model="entity.setting.customerField" :data="modelFields" allow-clear
                    :field-names="fieldStruct" :placeholder="t('systemSetting.communicateChannel.smsContent.qingXuanZeZiDuan')" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="setting.customerFieldPrefix">
                  <template #label>
                    {{t('systemSetting.communicateChannel.smsContent.shenFenZiDuanQianZhui')}}
                    <a-tooltip position="right" :content="t('systemSetting.communicateChannel.smsContent.yongYu')">
                      <icon-info-circle />
                    </a-tooltip>
                  </template>
                  <a-input v-model="entity.setting.customerFieldPrefix" :placeholder="t('systemSetting.communicateChannel.smsContent.QSRSFZDQZ')" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :rules="[{ required: true, message: t('systemSetting.communicateChannel.smsContent.QXZSFKCDZD')}]" field="setting.reachField">
                  <template #label>
                    {{$t('systemSetting.communicateChannel.smsContent.YHJSGQDXXBJZD')}}
                    <a-tooltip position="right" :content="t('systemSetting.communicateChannel.smsContent.QXZYYBJHYSFJSGQDFSXXDZD')">
                      <icon-info-circle />
                    </a-tooltip>
                  </template>
                  <a-tree-select v-model="entity.setting.reachField" :data="modelFields" allow-clear
                    :field-names="fieldStruct" :placeholder="t('systemSetting.communicateChannel.smsContent.qingXuanZeZiDuan')" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="setting.lengthLimit">
                  <template #label>
                    {{$t('systemSetting.communicateChannel.smsContent.lengthLimit')}}
                  </template>
                  <a-input-number v-model="entity.setting.lengthLimit" >
                  </a-input-number>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item :label="t('systemSetting.communicateChannel.smsContent.beiZhu')" field="summary">
                  <a-textarea v-model="entity.summary" :placeholder="t('systemSetting.communicateChannel.smsContent.QSRBZXX')" />
                </a-form-item>
              </a-col>
            </a-row>

            <template v-if="entity.setting.type === 'sms-aliyun'">
              <h3 class="form-title">{{t('systemSetting.communicateChannel.smsContent.ALYDXPZ')}}</h3>
              <a-row :gutter="80">
                <a-col :span="12">
                  <a-form-item :label="t('systemSetting.communicateChannel.smsContent.fuWuDiZhi')" :rules="[{ required: true, message: t('systemSetting.communicateChannel.smsContent.QSRFWDZ') }]" field="setting.endpoint">
                    <a-input v-model="entity.setting.endpoint" :placeholder="t('systemSetting.communicateChannel.smsContent.QSRFWDZ')" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :label="t('systemSetting.communicateChannel.smsContent.duanXinQianMing')" :rules="[{ required: true, message: t('systemSetting.communicateChannel.smsContent.QSRDXQM') }]" field="setting.smsSign">
                    <a-input v-model="entity.setting.smsSign" :placeholder="t('systemSetting.communicateChannel.smsContent.QSRDXQM')" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :label="t('systemSetting.communicateChannel.smsContent.lianJie')" :rules="[{ required: true, message: t('systemSetting.communicateChannel.smsContent.qingShuRuLianJie') }]" field="setting.accessKeyId">
                    <a-input v-model="entity.setting.accessKeyId" :placeholder="t('systemSetting.communicateChannel.smsContent.qingShuRuLianJie')" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :label="t('systemSetting.communicateChannel.smsContent.lianJieMiYao')" :rules="[{ required: true, message: t('systemSetting.communicateChannel.smsContent.QSRLJMY') }]" field="setting.accessKeySecret">
                    <a-input-password v-model="entity.setting.accessKeySecret" :placeholder="t('systemSetting.communicateChannel.smsContent.QSRLJMY')" />
                  </a-form-item>
                </a-col>
              </a-row>
            </template>

            <limit-setting v-model:limitSetting="entity.setting.limitSetting" :model-fields="modelFields"/>
          </a-card>
        </a-space>
      </a-form>

    </template>
    <template #action></template>
  </module>
</template>

<script>
import { ref, provide,getCurrentInstance } from "vue";
import { useRoute, useRouter } from "vue-router";
import { findItem, saveInfo } from "@/api/channel";
import { findCustomerModel } from "@/api/system";
import { formatFields } from "@/utils/field"
// import LimitSetting from "./limit-setting.vue";
import LimitSetting from '@/components/ma/limit-setting/index.vue'

export default {
  components:{
    LimitSetting
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const route = useRoute();
    const router = useRouter();

    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: t('menu.system'),
          path: "/system/setting",
        },
        {
          name: t('systemSetting.communicateChannel.communicateManage'),
          path: "/system/reach",
        },
        {
          name: t('systemSetting.communicateChannel.smsManage'),
          path: "/system/reach/sms",
        },
      ],
      mainPath: "/system/reach",
    });

    const smsTypes = ref([
    {id: "sms-aliyun", label:  t('systemSetting.communicateChannel.aliYunSms')},
    {id: "sms", label:  t('systemSetting.communicateChannel.simplifiedSms')}
    ]);
    const fieldStruct = ref({
      key: 'path',
      value: 'path',
      title: 'label',
      children: 'fields',
    });

    const formRef = ref(null);
    // {{$t('systemSetting.communicateChannel.smsContent.huoQuZiDuanShuJu')}}
    const modelFields = ref([]);

    const entity = ref({
      name: null,
      type: "sms",
      category: "reach",
      summary: null,
      setting: {
        type: "sms",
        limitSetting: {},
        silenceSetting: {}
      },
    });

    const bindData = async () => {
      if (route.query.id) {
        entity.value = await findItem(route.query.id);
        entity.value = entity.value ? entity.value : {};
      }
      if (entity.value.setting?.limitSetting && !entity.value.setting.limitSetting.dateLimit) {
        entity.value.setting.limitSetting.dateLimit = {}
      }
      findCustomerModel().then((data) => {
        if (data) {
          modelFields.value = formatFields(data.fields);
        }
      });
    };

    const save = async () => {
      formRef.value.validate((err) => {
        if (!err) {
          saveInfo(entity.value).then(() => {
            router.push({ path: module.value.mainPath });
          });
        }
      });
    };

    const setup = {
      t,
      formRef,
      module,
      entity,
      smsTypes,
      fieldStruct,
      modelFields,
      bindData,
      save,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.form-title{
  margin: 0 20px;
}
</style>
