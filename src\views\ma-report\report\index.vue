<template>
  <div>
    <module view>
      <template #action><span></span></template>
      <template #main>
        <div class="top-input-tag">
          <a-range-picker v-model="dateList" style="width: 300px" format="YYYY-MM-DD" :allow-clear="false"
            @change="changeTimeRange" />
          <!-- <div class="tag-list">
            <a-tag :color="workplaceItem.color">{{
              statusFilter(workplaceItem.status)
            }}</a-tag>
            <a-tag>{{ workplaceItem.name }}</a-tag>
          </div> -->
        </div>
        <div class="card-list">
          <div class="card-item">
            <div class="number">{{ countNumber }}</div>
            <div class="desc">{{ t('report.cumulativeReachCount') }}</div>
          </div>
          <div class="card-item">
            <div class="number">{{ peopleNumber }}</div>
            <div class="desc">{{ t('report.cumulativeReachPeople') }}</div>
          </div>
          <div v-for="(item, index) in dataList" :key="index" class="card-item">
            <div class="number-card">
              <div class="number-item">
                <div class="number-item">{{ item.counts.TOTAL_SUCC }}</div>
                <div class="desc">成功</div>
              </div>
              <div class="number-separator"> /</div>
              <div class="number-item">
                <div>{{ item.counts.TOTAL_SEND }}</div>
                <div class="desc">总共</div>
              </div>
            </div>
            <div class="desc">{{ item.name }}</div>
          </div>
        </div>
        <div class="table-list">
          <a-table ref="table" :pagination="false" :data="dataList">
            <template #columns>
              <a-table-column :title="t('report.reachChannel')" data-index="name" />
              <a-table-column :title="t('report.todayReachCount')" data-index="counts.TODAY_SEND" />
              <a-table-column :title="t('report.todayReachSuccessCount')" data-index="counts.TODAY_SUCC" />
              <a-table-column :title="t('report.todayReachSuccessRate')" data-index="counts.TODAY_SUCC">
                <template #cell="{ record }">
                  {{
                    record.counts.TODAY_SEND
                      ? `${(
                        record.counts.TODAY_SUCC / record.counts.TODAY_SEND
                      ).toFixed(2) * 100
                      }%`
                      : "--"
                  }}
                </template>
              </a-table-column>
              <a-table-column :title="t('report.cumulativeReachCount')" data-index="counts.TOTAL_SEND" />
              <a-table-column :title="t('report.cumulativeReachSuccessCount')" data-index="counts.TOTAL_SUCC" />
              <a-table-column :title="t('report.cumulativeReachSuccessRate')" data-index="code">
                <template #cell="{ record }">
                  {{
                    record.counts.TOTAL_SEND
                      ? `${(
                        record.counts.TOTAL_SUCC / record.counts.TOTAL_SEND
                      ).toFixed(2) * 100
                      }%`
                      : "--"
                  }}
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
        <div class="table-list">
          <a-row v-if="filterInfo.length > 0">
            <a-col :flex="1">
              <a-form :model="formModel" auto-label-width label-align="right">
                <a-row :gutter="16">
                  <template v-for="(filterItem, filterIndex) in filterInfo.filter(
                    (i) => !i.isHidden
                  )" :key="filterIndex">
                    <a-col v-if="filterIndex < openFilterNumber" :span="8">
                      <a-form-item :field="filterItem.field" :label="filterItem.label">
                        <component :is="filterItem.component" v-model="filterList[filterItem.field]"
                          :options="filterItem.dataSource" :allow-clear="filterItem.allowClear"
                          :allow-search="filterItem.allowSearch" :placeholder="filterItem.placeholder"
                          :field-names="filterItem.fieldNames" :multiple="filterItem.isMultiple">
                        </component>
                      </a-form-item>
                    </a-col>
                  </template>
                </a-row>
              </a-form>
            </a-col>
            <a-col :flex="'86px'" style="text-align: right; padding-left: 20px">
              <a-space :size="18">
                <a-button type="primary" @click="search(1)">
                  <template #icon>
                    <icon-search />
                  </template>
                  {{ t('global.button.query') }}
                </a-button>
                <a-button @click="reset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  {{ t('global.button.reset') }}
                </a-button>
                <a-button v-if="filterInfo.length > 3" type="text" @click="onOpenFilter">
                  {{ openFilterNumber === 3 ? t('global.button.expand') : t('global.button.retract') }}
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </div>

        <div class="table-list">
          <a-button type="primary" style="margin-bottom: 10px" @click="exportFile()">
            {{ t('global.button.export') }}
          </a-button>
          <a-table ref="recordTable" :pagination="false" :bordered="false" :data="dataSource" :scroll="scrollPercent" :scrollbar="scrollbar">
            <template #columns>
              <a-table-column :title="t('report.channel')" data-index="nodeConfigId" :width="80">
                <template #cell="{ record }">
                  {{ nodeConfigNameFilter(record.nodeConfigId) }}
                </template>
              </a-table-column>
              <a-table-column :title="t('report.operationTime')" data-index="timestamp" :width="120">
                <template #cell="{ record }">
                  {{ $moment(record.timestamp).format("YYYY-MM-DD HH:mm:ss") }}
                </template>
              </a-table-column>
              <a-table-column :title="t('report.campaignId')" data-index="groupId" :width="120" />
              <a-table-column :title="t('report.campaignName')" data-index="groupId" :width="120">
                <template #cell="{ record }">
                  {{ groupNameFilter(record.groupId) }}
                </template>
              </a-table-column>
              <a-table-column :title="t('report.campaignCategory')" data-index="category" :width="120" />
              <!-- <a-table-column title="活动编号" data-index="campaignCode" /> -->
              <a-table-column :title="t('report.canvasId')" data-index="flowId" :width="120" :ellipsis="true"
                :tooltip="true">
              </a-table-column>
              <a-table-column :title="t('report.canvasName')" data-index="flowId" :width="120" :ellipsis="true"
                :tooltip="true">
                <template #cell="{ record }">
                  {{ getFlowName(record.flowId) }}
                </template>
              </a-table-column>
              <a-table-column :title="t('report.executionStatus')" data-index="status" :width="80">
                <template #cell="{ record }">
                  {{ nodeStatusFilter(record.status) }}
                </template>
              </a-table-column>
              <a-table-column :title="t('report.customerId')" data-index="payloadId" :width="120" />
              <a-table-column :title="t('report.customerIdentityField')" data-index="cdp_identity_snapshot" :width="120"
                :ellipsis="true" :tooltip="{ class: 'tooltip-content' }" />
              <a-table-column :title="t('report.reachField')" data-index="reachField" :width="100" />
              <a-table-column :title="t('report.reachFieldValue')" data-index="reachFieldValue" :width="150" />
              <a-table-column :title="t('report.reachContent')" data-index="reachContent" :width="200" :ellipsis="true"
                :tooltip="{ class: 'tooltip-content' }" />
              <a-table-column :title="t('report.reachData')" data-index="reachMapping" :width="200" :ellipsis="true"
                :tooltip="{ class: 'tooltip-content' }" />
              <a-table-column :title="t('report.reachStatus')" data-index="reachStatus" :width="100">
                <template #cell="{ record }">
                  {{ reachStatusFilter(record.reachStatus) }}
                </template>
              </a-table-column>
              <a-table-column :title="t('report.processInstanceId')" data-index="instanceId" :width="120"
                :ellipsis="true" :tooltip="true" />
              <a-table-column :title="t('report.canvasNodeId')" data-index="taskId" :width="120" />
              <a-table-column :title="t('report.reachTemplateId')" data-index="reachTemplate" :width="120" />
              <a-table-column :title="t('report.reachTemplateName')" data-index="reachTemplateName" :width="120" />
              <a-table-column :title="t('report.reachContentId')" data-index="reachContentId" :width="120" />
              <a-table-column :title="t('report.reachContentName')" data-index="reachContentName" :width="120" />
              <a-table-column :title="t('report.acceptanceStatus')" data-index="income" :width="80" />
              <a-table-column :title="t('report.completionStatus')" data-index="outgoing" :width="80" />
              <a-table-column :title="t('report.errorStatus')" data-index="error" :width="80" />
              <a-table-column :title="t('report.nodeType')" data-index="taskType" :width="120" />
              <a-table-column :title="t('report.isTemplateFrequencyLimit')" data-index="limitedTemplate" :width="160"
                :ellipsis="true" :tooltip="true" />
              <a-table-column :title="t('report.isChannelFrequencyLimit')" data-index="limitedChannel" :width="160"
                :ellipsis="true" :tooltip="true" />
              <a-table-column :title="t('report.isGlobalFrequencyLimit')" data-index="limitedGlobal" :width="160"
                :ellipsis="true" :tooltip="true" />
              <a-table-column :title="t('report.message')" data-index="message" :width="150" :ellipsis="true"
                :tooltip="{ class: 'tooltip-content' }" />

              <a-table-column title="扩展字段1" data-index="extended1" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段2" data-index="extended2" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段3" data-index="extended3" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段4" data-index="extended4" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段5" data-index="extended5" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段6" data-index="extended6" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段7" data-index="extended7" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段8" data-index="extended8" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段9" data-index="extended9" :width="100" :ellipsis="true" :tooltip="true" />
              <a-table-column title="扩展字段10" data-index="extended10" :width="110" :ellipsis="true" :tooltip="true" />
            </template>
          </a-table>
          <div class="page-bottom">
            <!-- <span class="total">共{{ pagination.totalElements }}条</span> -->
            <span class="total">{{ t('global.total') }} : {{ pagination.totalElements }}</span>
            <a-pagination size="small" :total="pagination.total" :current="pagination.page" :page-size="pagination.size"
              show-page-size @change="search" @page-size-change="changeSizePage" />
          </div>
        </div>
      </template>
    </module>
  </div>
</template>

<script>
import { provide, ref, onMounted, computed, getCurrentInstance } from "vue";
import moment from "moment";
import { Message } from "@arco-design/web-vue";
import { findNodeLists } from "@/api/node";
import { getAllReachSummary, getMonitorPage } from "@/api/monitor";
import { findGroupList } from "@/api/group";
import { findCampaignList } from "@/api/campaign";
import { findCategoryList } from "@/api/category";
import { filters } from "@/utils/filter";
import { campaignStatus } from "@/constant/campaign";
import { flowNodeStatus } from "@/constant/flow";
import { taskTypes } from "@/constant/task";
import { saveTask2, startTask } from "@/api/task";
import { useUserStore } from "@/store";
import { uuid } from "@/utils/uuid";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const userStore = useUserStore();
    const userInfo = computed(() => {
      return userStore.userAuthInfo;
    });
    const scrollbar = ref(true);
    const scrollPercent = {
      x: 200,
      y: 600
    };
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          // name: `沟通报表`,
          name: t('report.communicationReport')
        },
      ],
      mainPath: "/dashboard/workplace",
      editPath: "/dashboard/report",
    });
    // 数据设置
    const dataSummary = ref([]);
    const nodelist = ref([]);
    const grouplist = ref([]);
    const flowlist = ref([]);
    const dataSource = ref([]);
    const loading = ref(true);
    const entity = ref({});
    const dateList = ref([]);
    const campaignCategories = ref([]);
    const countNumber = ref(0);
    const peopleNumber = ref(0);
    const dataList = ref([]);
    const formModel = ref({});
    // 过滤条件
    const filterList = ref({});
    const reachStatus = ref([
      // { label: "正在发送", value: "SENDING" },
      // { label: "发送成功", value: "SENT" },
      // { label: "发送出错", value: "ERROR" },
      // { label: "推迟发送", value: "DELAY" },
      // { label: "忽略发送", value: "SKIP" },
      // { label: "已接收", value: "RECEIVED" },
      // { label: "已接受", value: "ACCEPTED" },
      // { label: "拒绝", value: "REFUSED" },
      // { label: "拒绝渠道", value: "REFUSE_CHANNEL" },
      { label: t("global.reach.status.sending"), value: "SENDING" },
      { label: t("global.reach.status.sendSuccess"), value: "SENT" },
      { label: t("global.reach.status.sendError"), value: "ERROR" },
      { label: t("global.reach.status.postponeExecution"), value: "DELAY" },
      { label: t("global.reach.status.ignore"), value: "SKIP" },
      { label: t("global.reach.status.received"), value: "RECEIVED" },
      { label: t("global.reach.status.accepted"), value: "ACCEPTED" },
      { label: t("global.reach.status.rejected"), value: "REFUSED" },
      { label: t("global.reach.status.rejectChannelMessages"), value: "REFUSE_CHANNEL" },
    ]);
    const incomeStatus = [
      // { label: "已接收", value: 1 },
      // { label: "拒绝", value: 2 },
      { label: t("global.reach.status.accepted"), value: 1 },
      { label: t("global.reach.status.rejected"), value: 2 },
    ];
    const ioutgoingStatus = [
      // { label: "已完成", value: 1 },
      // { label: "未完成", value: 2 },
      { label: t("global.reach.status.received"), value: 1 },
      { label: t("global.reach.status.rejected"), value: 2 },
    ];
    const errorStatus = [
      { label: "", value: 0 },
      { label: "错误1", value: 1 },
      { label: "错误2", value: 2 },
    ];
    const flowNodeStatus1 = [
      {
        // label: "接收",
        label: t('global.flow.receive'),
        value: "INCOME",
      },
      {
        // label: "成功",
        label: t('global.flow.success'),
        value: "OUTGOING",
      },
      {
        // label: "失败",
        label: t('global.flow.failure'),
        value: "ERROR",
      },
    ];
    // 过滤设置
    const filterInfo = ref([
      {
        field: "nodeConfigId",
        label: t('report.channel'),
        component: "a-select",
        operate: "eq",
        dataSource: nodelist,
        placeholder: t('report.reminder.channel'),
        allowSearch: true,
        comment: true,
        value: "",
      },
      {
        field: "timestamp",
        label: t('report.operationTime'),
        isHidden: true,
        component: "a-range-picker",
        operate: "range",
        value: "",
      },
      {
        field: "groupName",
        label: t('report.campaignName'),
        component: "a-select",
        dataSource: grouplist,
        allowClear: true,
        allowSearch: true,
        operate: "eq",
        placeholder: t('report.reminder.campaignName'),
        comment: true,
        isMultiple: true,
        value: "",
      },
      {
        field: "groupId",
        label: t('report.campaignId'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.campaignId'),
        comment: true,
        allowClear: true,
        isMultiple: true,
        value: "",
      },

      {
        field: "category",
        label: t('report.campaignCategory'),
        component: "a-select",
        dataSource: campaignCategories,
        allowClear: true,
        allowSearch: true,
        operate: "eq",
        placeholder: t('report.reminder.campaignCategory'),
        comment: true,
        value: "",
      },
      {
        field: "flowName",
        label: t('report.canvasName'),
        component: "a-select",
        operate: "eq",
        dataSource: flowlist,
        placeholder: t('report.reminder.canvasName'),
        allowSearch: true,
        isMultiple: true,
        value: "",
      },
      {
        field: "flowId",
        label: t('report.canvasId'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.canvasId'),
        allowSearch: true,
        allowClear: true,
        isMultiple: true,
        value: "",
      },

      {
        field: "status",
        label: t('report.executionStatus'),
        component: "a-select",
        operate: "eq",
        dataSource: flowNodeStatus1,
        placeholder: t('report.reminder.executionStatus'),
        allowSearch: true,
        value: "",
      },
      {
        field: "payloadId",
        label: t('report.customerId'),
        component: "a-input",
        operate: "like",
        placeholder: t('report.reminder.customerId'),
        comment: true,
        allowClear: true,
        value: "",
      },
      {
        field: "reachField",
        label: t('report.reachField'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.reachField'),
        value: "",
      },
      {
        field: "reachFieldValue",
        label: t('report.reachFieldValue'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.reachFieldValue'),
        value: "",
      },
      {
        field: "reachStatus",
        label: t('report.reachStatus'),
        component: "a-select",
        operate: "eq",
        dataSource: reachStatus,
        placeholder: t('report.reminder.reachStatus'),
        allowSearch: true,
        value: "",
      },
      {
        field: "instanceId",
        label: t('report.processInstanceId'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.processInstanceId'),
        value: "",
      },
      {
        field: "taskId",
        label: t('report.canvasNodeId'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.canvasNodeId'),
        value: "",
      },
      {
        field: "reachTemplate",
        label: t('report.reachTemplateId'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.reachTemplateId'),
        value: "",
      },
      {
        field: "reachTemplateName",
        label: t('report.reachTemplateName'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.reachTemplateName'),
        value: "",
      },
      {
        field: "reachContentId",
        label: t('report.reachContentId'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.reachContentId'),
        value: "",
      },
      {
        field: "reachContentName",
        label: t('report.reachContentName'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.reachContentName'),
        value: "",
      },
      {
        field: "limitedTemplate",
        label: t('report.isTemplateFrequencyLimit'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.isTemplateFrequencyLimit'),
        value: "",
      },
      {
        field: "limitedChannel",
        label: t('report.isChannelFrequencyLimit'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.isChannelFrequencyLimit'),
        value: "",
      },

      {
        field: "limitedGlobal",
        label: t('report.isGlobalFrequencyLimit'),
        component: "a-input",
        operate: "eq",
        placeholder: t('report.reminder.isGlobalFrequencyLimit'),
        value: "",
      },
    ]);

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true,
    });
    const buildExp = () => {
      const exp = [];
      filterInfo.value.map((item) => {
        if (filterList.value[item.field] !== undefined && filterList.value[item.field] && (!Array.isArray(filterList.value[item.field]) || filterList.value[item.field].length > 0)) {
          if (item.operate === "range") {
            exp.push(
              `${item.field} ge ${filterList.value[item.field][0]
              }T00:00:00.000+08:00 AND ${item.field} le ${filterList.value[item.field][1]
              }T23:59:59.999+08:00`
            );
          } else {
            let expStr = `${item.field} ${item.operate} ${filterList.value[item.field]
              }`;
            if (item.field === "groupName") {
              expStr = `groupId ${item.operate} ${filterList.value[item.field]
                }`;
            }
            if (item.field === "flowName") {
              expStr = `flowId ${item.operate} ${filterList.value[item.field]
                }`;
            }
            exp.push(expStr);
          }
        }
      });
      return exp.join(" AND ");
    };

    const getQueryExp = (exp) => {
      let expression = `reachStatus not NULL AND engineType ne DEBUG`;
      if (exp) {
        expression = `${expression} AND ${exp}`;
      }
      if (dateList?.value?.length > 0) {
        expression = `${expression} AND timestamp gt ${dateList.value[0]}T00:00:00.000+08:00 AND timestamp lt ${dateList.value[1]}T23:59:59.999+08:00`;
      }
      return expression;
    };
    // 查询API
    const queryDataPage = async (exp) => {
      const expression = getQueryExp(exp);
      entity.value = await getMonitorPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
          sort: `timestamp,DESC`,
        },
        { expression }
      );
      pagination.value.total =
        entity.value.totalElements > 10000 ? 10000 : entity.value.totalElements;
      pagination.value.totalElements = entity.value.totalElements;

      dataSource.value = entity.value.content || [];
      dataSource.value.map((it) => {
        it.ext = JSON.stringify(it.ext);
        return it;
      });
      loading.value = false;
    };
    // 定义高级筛选
    const openFilterNumber = ref(3);
    const onOpenFilter = () => {
      openFilterNumber.value =
        openFilterNumber.value === 3 ? filterInfo.value.length : 3;
    };
    const reachStatusFilter = (val) => {
      switch (val) {
        case "SENDING":
          // return "正在发送";
          return t("global.reach.status.sending")
        case "SENT":
          // return "发送成功";
          return t("global.reach.status.sendSuccess")
        case "LIMITED":
          // return "次数限制";
          return t("global.reach.status.restricted")
        case "ERROR":
          // return "发送出错";
          return t("global.reach.status.sendError")
        case "DELAY":
          // return "推迟发送";
          return t("global.reach.status.postponeExecution")
        case "SKIP":
          // return "忽略发送";
          return t("global.reach.status.ignore")
        case "RECEIVED":
          // return "已接收";
          return t("global.reach.status.received")
        case "ACCEPTED":
          // return "已接受";
          return t("global.reach.status.accepted")
        case "REFUSED":
          // return "拒绝";
          return t("global.reach.status.rejected")
        case "REFUSE_CHANNEL":
          // return "拒绝渠道";
          return t("global.reach.status.rejectChannelMessages")
        default:
          return "loading...";
      }
    };

    const nodeStatusFilter = (val) => {
      return filters(flowNodeStatus, val, "text", "value");
    };
    const nodeConfigNameFilter = (val) => {
      const res = nodelist.value.find((item) => {
        return item.value === val;
      });
      return res ? res.label : "";
    };

    const groupNameFilter = (val) => {
      const res = grouplist.value.find((item) => {
        return item.value === val;
      });
      return res ? res.label : "";
    };
    const flowNameFilter = (val) => {
      const res = flowlist.value.find((item) => {
        return item.value === val;
      });
      return res ? res.label : "";
    };
    const getNodelist = async () => {
      nodelist.value = [];
      const res = await findNodeLists({
        expression: "type in flow_content,sms,wechat,email,coupon,points",
        fields: "name",
      });
      // console.log(nodelist.value )
      res.map((it) => {
        nodelist.value.push({ label: it.name, value: it.id });
        return nodelist.value;
      });
    };
    const getGrouplist = async () => {
      grouplist.value = [];
      const res = await findGroupList();
      res.map((it) => {
        grouplist.value.push({ label: it.name, value: it.id });
        return grouplist.value;
      });
    };

    const getFlowlist = async () => {
      flowlist.value = [];
      const res = await findCampaignList({ fields: "name" });
      res.map((it) => {
        flowlist.value.push({ label: it.name, value: it.id });
        return flowlist.value;
      });
    };
    // 查询活动分类
    const getCampaignCategoriesList = async () => {
      const res = await findCategoryList();
      campaignCategories.value = res.map((it) => ({
        label: it.name,
        value: it.id,
      }));
      // console.log('campaignCategories.value', campaignCategories.value)
    };
    // 获取画布名称
    const getFlowName = (id) => {
      const flow = flowlist.value.find((item) => item.value === id);
      // console.log('flow', flow)
      if (flow) {
        return flow.label;
      }
      return "";
    };
    const statusFilter = (status) => {
      const item = campaignStatus.find((item) => {
        return item.value === status;
      });
      return item.label || "--";
    };
    const search = (page = 1) => {
      loading.value = true;
      pagination.value.page = page;
      queryDataPage(buildExp());
      loading.value = false;
    };

    const reset = async () => {
      filterList.value = {};
      await queryDataPage();
    };

    const exportFile = async () => {
      const expression = getQueryExp(buildExp());
      const job = {
        id: `${uuid(6)}`,
        // name: `全量沟通报表数据导出`,
        name: t('global.tips.success.fullCommunicationReportDataExport'),
        type: "export_campaign_reach_report",
        timestamp: new Date(),
        status: "SUBMITTED",
        userId: userInfo.value.username,
        payload: {
          query: expression,
        },
      };
      const res = await saveTask2(job);
      await startTask(res.id);
      // Message.success("创建全量沟通报表数据导出任务成功,可在任务管理中查看");
      Message.success(t('global.tips.success.createFullCommunicationReportExportTaskSuccess'));
    };
    // 切换条数
    const changeSizePage = async (e) => {
      pagination.value.page = 1;
      pagination.value.size = e;
      await search();
    };

    const getSummary = () => {
      getAllReachSummary({
        startDate: dateList.value[0],
        endDate: dateList.value[1],
      }).then((res) => {
        peopleNumber.value = res.reachPeople;
        dataList.value = res.channels;
        countNumber.value = 0
        dataList.value.forEach((item) => {
          countNumber.value += item.counts.TOTAL_SEND;
        });
      });
    };
    const changeTimeRange = async () => {
      getSummary();
      search();
    };
    onMounted(async () => {
      // eslint-disable-next-line no-use-before-define
      const today = moment().format("YYYY-MM-DD");
      const tenDaysAgo = moment().subtract(10, "days").format("YYYY-MM-DD");
      dateList.value = [tenDaysAgo, today];
      changeTimeRange();
      getNodelist();
      getGrouplist();
      getFlowlist();
      getCampaignCategoriesList();
    });
    const setup = {
      scrollbar,
      scrollPercent,
      formModel,
      filterInfo,
      filterList,
      module,
      dataSummary,
      dataSource,
      pagination,
      statusFilter,
      queryDataPage,
      nodeConfigNameFilter,
      nodeStatusFilter,
      reachStatusFilter,
      search,
      reset,
      openFilterNumber,
      onOpenFilter,
      changeSizePage,
      exportFile,
      groupNameFilter,
      flowNameFilter,
      getFlowName,
      incomeStatus,
      getCampaignCategoriesList,
      countNumber,
      peopleNumber,
      dataList,
      getSummary,
      dateList,
      changeTimeRange,
    };
    provide("view", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.top-input-tag {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tag-list {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.card-list {
  display: flex;
  gap: 20px;
  padding: 0 20px;
  flex-wrap: wrap;

  .card-item {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 300px;
    text-align: center;
    color: #333;
    border: 1px solid #efefef;
    padding: 30px;
    padding-bottom: 10px;

    .number {
      font-size: 40px;
      font-weight: bold;
      line-height: 60px;
    }
    .number-card {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      .number-item {
        font-size: 40px;
        font-weight: bold;
        line-height: 60px;
        .desc {
          font-size: 14px;
          font-weight: normal;
          line-height: 10px;
          padding: 0;
          color: #999;
        }
      }
      .number-separator {
        font-size: 40px;
        font-weight: bold;
        line-height: 60px;
      }
    }
    .desc {
      padding: 10px 0;
      font-size: 20px;
      font-weight: bold;
    }
  }
}

.input-list-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;
}

.input-item {
  display: flex;
  align-items: center;
}

.table-list {
  padding: 20px;
  padding-bottom: 0px;

  .input-list-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
  }

  .input-item {
    display: flex;
    align-items: center;
  }
}

.page-bottom {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  align-items: center;

  .total {
    margin-top: 20px;
  }
}

.status {
  &.DRAFT {
    color: rgba(var(--green-6));
  }

  &.COMMITTED {
    color: rgba(var(--lime-6));
  }

  &.APPROVED {
    color: rgba(var(--cyan-6));
  }

  &.REJECTED {
    color: rgba(var(--gold-6));
  }

  &.RUNNING {
    color: rgba(var(--blue-6));
  }

  &.PAUSED {
    color: rgba(var(--orange-6));
  }

  &.FINISHED {
    color: rgba(var(--blue-6));
  }

  &.STOP {
    color: rgba(var(--gray-6));
  }
}
</style>
