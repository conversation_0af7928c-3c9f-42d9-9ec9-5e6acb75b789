import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";
const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function getDataList(id?: any, params?: Params) {
    return axios.get(`/api/ma-manage/${tenantId}/${buCode}/dashboard_render/${id}`, { params });
}

export function getDataListChart(id?: any, data?: any) {
    return axios.post(`/api/ma-manage/${tenantId}/${buCode}/dashboard_render/${id}/refresh`, data);
}

export function deleteItemChart(id?: [String, Number]) {
    return axios.delete(`/api/bi-manage/${tenantId}/${buCode}/dashboard/${id}`);
}

export function putItemChart(data: any) {
    return axios.put(`/api/bi-manage/${tenantId}/${buCode}/dashboard`, data);
}

export function getChartList(query?: any, params?: Params) {
    return axios.get(`/api/bi-manage/${tenantId}/${buCode}/visualize`, {
        params: {
            ...query,
            ...params
        }
    });
}

export function setDataItemChart(data?: any) {
    return axios.post(`/api/ma-manage/${tenantId}/${buCode}/dashboard/design_mode/render`, data);
}

export function getChartListItem(id?: any) {
    return axios.get(`/api/bi-manage/${tenantId}/${buCode}/visualize/${id}`).then(res => {
        return axios.post(`/api/bi-manage/${tenantId}/${buCode}/visualize/render`, res)
    });
}


export function getMaDataList(id?: any, params?: Params) {
    return axios.get(`/api/bi-manage/${tenantId}/${buCode}/dashboard_render/${id}`, { params });
}


export function getCampaignSummary(params?: Params) {
    return axios.get(`/api/ma-manage/${tenantId}/${buCode}/dashboard/campaign-summary`);
}


export function getDetailsChart(data?: any) {
    return axios.post(`/api/ma-manage/${tenantId}/${buCode}/bi/query_by_template?modelId=lianwei_ma_campaign_fact&templateId=fact_query_template`, {
        tenantId,
        buCode,
        taskType: 'flow_content',
        ...data
    });
}