<template>
  <div class="easyflow-pannel-except">
<a-form
      class="easyflow-pannel-form pannel-form"
      layout="vertical"
      :model="entity"
      :disabled="!editEnable"
    >
      <a-form-item label="选择被减集合" >
        <a-select v-model="entity.minuend" class="easyflow-select" :options="incomings" placeholder="请选择被减集合" />
      </a-form-item>

      <a-form-item label="选择减集合" >
        <a-select v-model="entity.subtrahend" class="easyflow-select" :options="incomings" placeholder="请选择减集合" />
      </a-form-item>
</a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
const props = defineProps(["node", "connections"]);
const node = props.node;
const connections = props.connections;
const loading = ref(false);
const pannelInject = inject("pannel");
const editEnable = pannelInject.editEnable;
const entity = ref({


});
const incomings = ref([])
const save = () => {
  return entity.value;
};

const refreshIncomings = () => {
  if (connections.incomings) {
    incomings.value = connections.incomings.map((it) => {return {value: it.id, label: it.name}});
  }
};

defineExpose({
  save,
});

onMounted(async () => {
  Object.assign(entity.value, node.data);
  await refreshIncomings();
});
</script>


<style lang="less" scoped>

</style>
