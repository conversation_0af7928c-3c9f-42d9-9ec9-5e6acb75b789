import { Graph } from "@antv/x6";
import { getPorts } from "../../components/node";
import ExclusionNode from "./node.vue";
import ExclusionPannel from "./pannel.vue";

const nodeData = {
  type: "exclusion",
  node: {
    inherit: "vue-shape",
    x: 0,
    y: 0,
    width: 232,
    height: 104,
    component: {
      template: `<ExclusionNode />`,
      components: {
        ExclusionNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("ExclusionNode", nodeData.node, true);
};

const Exclusion = {
  type: "exclusion",
  name: "差集",
  shape: "ExclusionNode",
  iconClass: "icon-chaji",
  registerNode,
  pannel: ExclusionPannel,
  skippable: true,
};

export default Exclusion;
