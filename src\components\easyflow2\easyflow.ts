import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>h, <PERSON>, Model, Node } from "@antv/x6";
import EventBus from "./components/eventbus";
import Connectors from "./components/connector";
// import { Nodes } from "./nodes";

interface Flow {
  target: HTMLElement;
  minimap: HTMLElement;
  simulate: object;
  tooglePannel: Function;
  onChange: Function;
  nodes: Array<any>;
}

class EasyFlow {
  minimap: HTMLElement;

  container: HTMLElement;

  simulate: object;

  tooglePannel: Function;

  graph: Graph;

  dnd: object;

  eventbus: any | undefined;

  connector: any;

  onChange: Function;

  editEnable: boolean;

  nodes: Array<any>;

  constructor(options: Flow) {
    this.minimap = options.minimap;
    this.container = options.target;
    this.simulate = options.simulate;
    this.tooglePannel = options.tooglePannel;
    this.graph = this.create(options.target);
    this.eventbus = EventBus;
    this.onChange = options.onChange;
    this.editEnable = true;
    this.nodes = options.nodes;

    this.dnd = new Addon.Dnd({
      target: this.graph,
    });
    try {
      this.register();
      this.listener();
      this.setup();
    } catch (error) {
      console.error(error);
    }
  }

  create(target: any) {
    const _this = this;
    return new Graph({
      container: target,
      autoResize: true,
      background: {
        color: "#ffffff",
      },
      snapline: true,
      selecting: {
        enabled: true,
        multiple: false,
        movable: false,
        showNodeSelectionBox: true,
        showEdgeSelectionBox: false,
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: ["ctrl", "meta"],
        minScale: 0.5,
        maxScale: 3,
      },
      panning: {
        enabled: true,
        eventTypes: ["leftMouseDown", "rightMouseDown", "mouseWheel"],
      },
      minimap: {
        enabled: true,
        container: this.minimap,
        scalable: true,
        width: 200,
        height: 100,
        padding: 10,
      },
      grid: {
        type: "mesh",
        size: 15,
        visible: true,
        args: {
          color: "#ebebeb", // 网格线/点颜色
        },
      },
      interacting: {
        nodeMovable(cellView: any) {
          return _this.editEnable;
        },
      },
      connecting: {
        router: {
          name: "manhattan",
          args: {
            padding: 1,
          },
        },
        connector: {
          name: "rounded",
          args: {
            radius: 8,
          },
        },
        anchor: "center",
        connectionPoint: "anchor",
        allowMulti: false,
        allowLoop: false,
        allowBlank: false,
        snap: {
          radius: 20,
        },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: "#A2B1C3",
                strokeWidth: 2,
                targetMarker: {
                  name: "block",
                  width: 12,
                  height: 8,
                },
              },
            },
            label: true,
            zIndex: 0,
          });
        },
        validateConnection(targetMagnet?: any): boolean {
          return !!targetMagnet;
        },
      },
      // connector: {
      //   name: "rounded",
      //   args: {
      //     radius: 8,
      //   },
      // },
    });
  }

  register() {
    for (let index = 0; index < this.nodes.length; index += 1) {
      this.nodes[index].registerNode(this);
      // const node = this.nodes[index];
      // console.log(this.nodes[index])
      // Graph.registerNode(node.shape, node.nodeData.node, true);
    }
    const newLocal = this;
    newLocal.connector = Connectors.Connector.register();

    Graph.registerConnector(this.connector.type, this.connector.render, true);
  }

  listener() {
    const _this = this;
    this.graph.on(
      "edge:click",
      (args: { e: any; x: any; y: any; edge: any; view: any }) => {}
    );
    // 注册节点选中事件
    this.graph.on(
      "node:selected",
      (args: { cell: Cell; node: Node; options: Model.SetOptions }) => {
        const connections = this.getIncomeAndOutgoing(args.node);
        this.tooglePannel(true, args.node, connections);
      }
    );
    this.graph.on("edge:mouseenter", (cell: any) => {
      if (_this.editEnable) {
        cell.cell.addTools([
          {
            name: "vertices",
          },
          // {
          //   name: 'segments'
          // },
          {
            name: "button-remove",
            args: { distance: 20 },
          },
          {
            name: "target-arrowhead",
            args: {
              attrs: {
                d: "M -10 -8 10 0 -10 8 Z",
                fill: "#333",
                "stroke-width": 2,
                cursor: "move",
              },
            },
          },
        ]);
      }
    });

    this.graph.on("edge:mouseleave", (cell: any) => {
      cell.cell.removeTools();
    });
    if (_this.onChange) {
      this.graph.on(
        "cell:added",
        (args: { cell: Cell; index: any; options: any }) => {
          _this.onChange(args);
        }
      );
      this.graph.on(
        "cell:removed",
        (args: { cell: Cell; index: any; options: any }) => {
          _this.onChange(args);
        }
      );

      this.graph.on(
        "cell:change:*",
        (args: {
          cell: Cell;
          key: string; // 通过 key 来确定改变项
          current: any; // 当前值，类型根据 key 指代的类型确定
          previous: any; // 改变之前的值，类型根据 key 指代的类型确定
          options: any; // 透传的 options
        }) => {
          if (
            args.key !== "tools" &&
            args.key !== "target" &&
            args.key !== "attrs"
          ) {
            _this.onChange(args);
          }
        }
      );

      this.graph.on("cell:mousemove", ({ e, cell, view }) => {
        if (!_this.editEnable) {}
      });
    }
  }

  destroyed() {
    this.eventbus.unlisten("tooglePannel");
  }

  setup() {}

  resizeEasyFlow(rect: any) {
    this.graph.resize(rect);
  }

  public getNodeConfig(shape: string) {
    return this.nodes.find((it: any) => it.shape === shape);
  }

  public getNode(shape: string) {
    const nodes = this.graph.getNodes();
    return nodes.filter((it) => it.data._type === shape);
  }

  public getNodeAttr(shape: string, id: string) {
    const nodes = this.graph.getNodes();
    if (id) {
      return nodes.find((it) => it.id === id)?.getData();
    }
    return nodes.find((it) => it.data._type === shape)?.getData();
  }

  public getAllModels() {
    const nodes = this.graph.getNodes();
    return this.getModels(nodes);
  }

  public getModels(nodes: Array<any>) {
    const modelIds = new Set();
    for (let index = 0; index < nodes.length; index += 1) {
      const node = nodes[index];
      const { getModelId } = this.nodes.find(
        (it: any) => it.shape === node.shape
      );
      if (getModelId) {
        getModelId(node.data).forEach((id: string) => {
          modelIds.add(id);
        });
      }
    }
    return Array.from(modelIds);
  }

  public getBindNames(nodes: Array<any>) {
    const bindNames = new Set();
    for (let index = 0; index < nodes.length; index++) {
      const node = nodes[index];
      const { getBindName } = this.nodes.find(
        (it: any) => it.shape === node.shape
      );
      if (getBindName) {
        const bindName = getBindName(node.data);
        if (bindName) {
          bindNames.add(bindName);
        }
      }
    }

    return Array.from(bindNames);
  }

  public getNodeChain(nodeId: string, nodeChain?: Array<any>) {
    if (!nodeChain) {
      nodeChain = [];
    }
    const node: any = this.findNode(nodeId);
    const incomings = node.model?.getIncomingEdges(node);
    if (incomings) {
      incomings.forEach((incoming: any) => {
        if (incoming.source?.cell) {
          this.getNodeChain(incoming.source.cell, nodeChain);
        }
      });
    }
    nodeChain.push(node);
    return nodeChain;
  }

  public getSkippableNodes() {
    const nodes = this.graph.getNodes();
    nodes.filter((node: any) =>
      this.nodes.find((it: any) => it.type === node.type)
    );
    // Nodes.find((it) => it.type == );
    // return nodes.find((it) => it.id == nodeId);
  }

  public findNode(nodeId: string) {
    const nodes = this.graph.getNodes();
    return nodes.find((it) => it.id === nodeId);
  }

  public getIncomeAndOutgoing(node: Node) {
    const incomings = node.model?.getIncomingEdges(node);
    const outgoings = node.model?.getOutgoingEdges(node);
    const result = {
      incomings: incomings?.map((it) => {
        const source = it.getSourceNode();
        return source
          ? {
              id: it.id,
              name: source?.data._name,
              type: source?.data._type,
              shape: source?.shape,
              iconClass: this.getNodeConfig(source.shape)?.iconClass,
            }
          : null;
      }),
      outgoings: outgoings?.map((it) => {
        const target = it.getTargetNode();
        return target
          ? {
              id: it.id,
              name: target?.data._name,
              type: target?.data._type,
              shape: target?.shape,
              iconClass: this.getNodeConfig(target.shape)?.iconClass,
            }
          : null;
      }),
    };
    return result;
  }

  showRecord(enable: boolean, records: any) {
    const nodes = this.graph.getNodes();
    nodes.forEach((it) => {
      if (enable) {
        it.attr({
          record: { enabled: true, count: records[it.id] ? records[it.id] : 0 },
        });
        // console.log(it)
        // it.getPorts().forEach( (p: any) => {console.log(it.getPortProp(p.id, ['attrs']))})
      } else {
        it.attr({
          record: { enabled: false },
        });
      }
    });
  }

  monitor(enable: boolean, monitorData: any) {
    const nodes = this.graph.getNodes();
    nodes.forEach((it) => {
      if (enable) {
        let data = monitorData.tasks.find((node: any) => {
          return node.taskId === it.id;
        });
        if (data) {
          data.enabled = monitorData.enabled;
          if(it.attrs?.monitor?.reachStatus){
            delete it.attrs.monitor.reachStatus;
          }
          it.attr({
            monitor: data,
          });
        } else {
          data = { income: 0, outgoing: 0, error: 0, enabled: enable };
          it.attr({
            monitor: data,
          });
        }
      } else {
        const data = { enabled: enable };
        it.attr({
          monitor: data,
        });
      }
    });
  }

  toJSON() {
    const json = this.graph.toJSON();
    const data = json.cells.map((i: any) => {
      const node: any = {
        id: i.id,
        shape: i.shape,
        zIndex: i.zIndex,
      };

      i.data ? (node.data = i.data) : null;
      i.target ? (node.target = i.target) : null;
      i.source ? (node.source = i.source) : null;
      i.position ? (node.position = i.position) : null;

      return node;
    });
    return data;
  }

  initFlowData() {
    this.graph.addNode({
      shape: "StartNode",
      id: `start_0`,
      x: this.container.clientWidth / 2 - 116, // 容器的一半扣除节点长度的一半
      y: 100,
      data: {
        _name: "开始",
        _type: "start",
      },
    });
  }

  fromJSON(flow: any) {
    const data = flow.map((i: any) => {
      if (i.shape === "edge") {
        i.attrs = {
          line: {
            stroke: "#A2B1C3",
            strokeWidth: 2,
            targetMarker: {
              name: "block",
              width: 12,
              height: 8,
            },
          },
        };
      }
      return i;
    });
    return this.graph.fromJSON(data);
  }

  toogleEdit(enable: boolean) {
    this.editEnable = enable;
  }

  getNodeMaxIndex(shape: string) {
    const json = this.graph.toJSON();
    const maxIndex = json.cells
      .filter((it) => it.shape !== "edge")
      .map((it) => it.id)
      .map((it) => it?.substring(it.lastIndexOf("_") + 1))
      .map((it) => parseInt(it))
      .sort((a, b) => a - b)
      .reverse()[0];

    if (maxIndex) {
      return maxIndex + 1;
    }
    return 1;
  }

  getEdgeMaxIndex() {
    const json = this.graph.toJSON();
    const maxIndex = json.cells
      .filter((it) => it.shape === "edge")
      .map((it) => it.id)
      .map((it) => it?.substring(it.lastIndexOf("_") + 1))
      .map((it) => parseInt(it))
      .sort((a, b) => a - b)
      .reverse()[0];
    if (maxIndex) {
      return maxIndex + 1;
    }
    return 1;
  }
}

export default EasyFlow;
