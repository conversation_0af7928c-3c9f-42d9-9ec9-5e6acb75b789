<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";
import { joinTypes } from "@/constant/flow";
import { filters } from "@/utils/filter";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];

      brief.push({ sort: 0, label: "数据模型", value: data.modelAliasName });
      brief.push({ sort: 1, label: "关联方式", value: filters(joinTypes, data.joinType, "text", "value") });
      return brief;
    };

    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'extend_data' })
    const setup = {
      title: item.name || "扩展数据",
      summary: item.name || "扩展数据节点",
      iconClass: item.icon || "icon-flow-stop",
      nodeClass: "easyflow-node-extend-data",
      headerColor: item.themeColor || "#ff6d69",
      headerBgColor: item.themeColor || "#ff6d69",
      background: item.background || "#fff8f8",
      ignoreOutgoingMonitor: true,
      notTirgger: true,
      getBrief
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
