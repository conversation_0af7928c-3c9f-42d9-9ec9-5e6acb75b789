/**
*by:<EMAIL> on 2022/7/18 0018
*/
<template>
  <div class="BudgetMain">
    <module main>
      <template #breadcrumb>
        <div class="breadcrumb-list">
          {{ module.entityName }}
          <a-select v-model="budgetAnnual" size="small" style="width: 200px; margin-left: 10px" placeholder="请选择预算年度"
            @change="bindData()">
            <a-option v-for="item in budgetAnnuals" :key="item.annual" :label="item.name" :value="item.annual" />
          </a-select>
        </div>
      </template>
      <template #top>
        <DataPanel :budget-summary="budgetData" />
      </template>
      <template #filter></template>
      <template #search></template>
      <template #main>
        <a-table v-if="loadingTable" ref="tableRef" :bordered="false" :data="dataSource" :pagination="false">
          <template #columns>
            <a-table-column title="预算名称" data-index="name" />
            <a-table-column title="计划预算" data-index="planned" :width="150">
              <template #cell="{ record }">
                {{ formatNumber(record.planned) }}
              </template>
            </a-table-column>
            <a-table-column title="已使用" data-index="spent" :width="150">
              <template #cell="{ record }">
                {{ formatNumber(record.spent) }}
              </template>
            </a-table-column>
            <a-table-column title="已确认" data-index="confirmed" :width="150">
              <template #cell="{ record }">
                {{ formatNumber(record.confirmed) }}
              </template>
            </a-table-column>
            <a-table-column title="结余" data-index="balance" :width="150">
              <template #cell="{ record }">
                {{ formatNumber(record.balance) }}
              </template>
            </a-table-column>
            <a-table-column title="操作" align="left" :width="250">
              <template #cell="{ record }">
                <a-button v-if="record.isParent" type="text" size="small"
                  @click="addBudgetItemRef.create(record.id, budgetAnnual)">新增</a-button>
                <template v-if="!record.isParent">
                  <a-button type="text" size="small" @click="showBudget(record)">查看</a-button>
                  <a-button v-if="!record.campaignId" type="text" size="small"
                    @click="addBudgetItemRef.edit(record)">编辑</a-button>
                  <a-button v-if="!record.campaignId" type="text" size="small"
                    @click="deleteData(record.id)">删除</a-button>
                </template>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <a-skeleton v-else :animation="true">
          <a-space direction="vertical" :style="{ width: '100%' }" size="large">
            <a-skeleton-line :rows="10" :line-height="30" />
          </a-space>
        </a-skeleton>
      </template>
    </module>
    <!--  新增数据  -->
    <AddBudgetIDlg ref="addBudgetItemRef" :bind-data="bindData" />
  </div>
</template>

<script>
import { provide, ref } from "vue";
import { useRouter } from "vue-router";
import { Modal } from "@arco-design/web-vue";
import { formatNumber } from "@/utils/filter";
import {
  deleteItem,
  findAnnualBudgetList,
  getBudgetAnnualGroupItem,
  getAnnualBudget,
} from "@/api/budget";
import { findGroupPage } from "@/api/group";
import AddBudgetIDlg from "@/components/modal-dlg/add-budget-dlg.vue";
import DataPanel from "./components/data-panel.vue";

export default {
  name: "BudgetMain",
  components: {
    DataPanel,
    AddBudgetIDlg,
  },
  setup() {
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      entityName: "预算管理",
      editPath: "/budget/budget/edit",
      viewPath: "/budget/budget/view",
      createPath: "/budget/budget/edit",
      budgetPath: "/campaign/campaign/budget",
      showCard: false,
      showBtn: false,
    });
    const filter = ref([
      {
        field: "name",
        label: "名称",
        component: "a-input",
        operate: "like",
        placeholder: "请输入预算名称",
        comment: true,
        value: "",
      },
    ]);
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true,
    });
    const dataSource = ref([]);
    const addBudgetItemRef = ref(null);

    const getAnnualData = async () => {
      if (budgetAnnual.value) {
        const res = await getAnnualBudget(budgetAnnual.value);
        budgetData.value = res;
      }
    };

    // 查看预算
    const showBudget = (campaign) => {
      const query = { id: campaign.id, isGroup: false };
      router.push({ path: module.value.budgetPath, query });
    };

    // 获取列表
    const loadingTable = ref(false);
    const bindData = async (expression) => {
      loadingTable.value = false;
      dataSource.value = [];
      if (budgetAnnual.value) {
        getAnnualData();
      } else {
        budgetAnnuals.value = await findAnnualBudgetList({
          fields: "name,annual",
        });
        budgetAnnual.value = budgetAnnuals.value[0]?.annual;
        getAnnualData();
      }

      const pageData = await findGroupPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
          annual: budgetAnnual.value,
        },
        {
          expression,
        }
      );

      const ids = [];
      pageData.content.forEach((item) => {
        ids.push(item.id);
      });
      getBudgetAnnualGroupItem(ids, budgetAnnual.value).then((res) => {
        pageData.content.forEach((children) => {
          const item = res.find((x) => {
            return x.groupId === children.id;
          });
          let planned = 0;
          let spent = 0;
          let confirmed = 0;
          let balance = 0;
          if (item) {
            item.budgets.forEach((x) => {
              planned += Number(x.planned || 0);
              spent += Number(x.spent || 0);
              confirmed += Number(x.confirmed || 0);
              balance += Number(x.balance || 0);
            });
          }

          dataSource.value.push({
            ...children,
            key: children.id,
            planned,
            spent,
            confirmed,
            balance,
            isCampaign: item?.campaignId,
            isParent: true,
            children: item?.budgets || null,
          });
        });
        loadingTable.value = true;
      });
      pagination.value.total = pageData.totalElements;
    };

    // 查看详情
    const detail = (data) => {
      const query = {};
      query[module.value.entityIdField] = data.id;
      router.push({ path: module.value.editPath, query });
    };

    // 删除
    const deleteData = async (id) => {
      Modal.confirm({
        title: "删除预算",
        content: "删除之后数据不可恢复，请确认是否删除?",
        onOk: async () => {
          await deleteItem(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page--;
          }
          await bindData();
        },
      });
    };

    const budgetAnnuals = ref([]);
    const budgetData = ref({});
    const budgetAnnual = ref(null);

    const setup = {
      module,
      pagination,
      filter,
      dataSource,
      budgetAnnuals,
      budgetAnnual,
      budgetData,
      addBudgetItemRef,
      loadingTable,
      showBudget,
      detail,
      deleteData,
      bindData,
      formatNumber,
      getAnnualData,
    };
    provide("main", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.breadcrumb-list {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
