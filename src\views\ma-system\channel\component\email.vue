<template>
  <module edit>
    <template #main>
      <a-form ref="formRef" layout="vertical" class="general-form" :model="entity">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item label="渠道名称" :rules="[{ required: true, message: '请输入渠道名称' }]" field="name">
                  <a-input v-model="entity.name" placeholder="请输入渠道名称" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :rules="[{ required: true, message: '请选择用户邮箱字段' }]" field="setting.customerField">
                  <template #label>
                    用户邮箱字段
                    <a-tooltip position="right" content="用户在该渠道下的唯一标识字段">
                      <icon-info-circle />
                    </a-tooltip>
                  </template>
                  <a-tree-select v-model="entity.setting.customerField" :data="modelFields" allow-clear
                    :field-names="fieldStruct" placeholder="请选择字段" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="setting.customerFieldPrefix">
                  <template #label>
                    身份字段前缀
                    <a-tooltip position="right" content="用于CDP身份字段逻辑">
                      <icon-info-circle />
                    </a-tooltip>
                  </template>
                  <a-input v-model="entity.setting.customerFieldPrefix" placeholder="请输入身份字段前缀" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :rules="[{ required: true, message: '请选择是否可触达字段' }]" field="setting.reachField">
                  <template #label>
                    用户接受该渠道消息标记字段
                    <a-tooltip position="right" content="请选择用于标记会员是否接受该渠道发送信息的字段">
                      <icon-info-circle />
                    </a-tooltip>
                  </template>
                  <a-tree-select v-model="entity.setting.reachField" :data="modelFields" allow-clear
                    :field-names="fieldStruct" placeholder="请选择字段" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="setting.lengthLimit">
                  <template #label>
                    {{$t('systemSetting.communicateChannel.smsContent.lengthLimit')}}
                  </template>
                  <a-input-number v-model="entity.setting.lengthLimit" >
                  </a-input-number>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="备注" field="summary">
                  <a-textarea v-model="entity.summary" placeholder="请输入备注信息" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮件协议" :rules="[{ required: true, message: '请选择邮件协议' }]" field="setting.protocol">
                  <a-select v-model="entity.setting.protocol" placeholder="请选择邮件协议">
                    <a-option v-for="item of protocols" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="SSL通信">
                  <a-switch v-model="entity.setting.ssl" type="round" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="开启鉴权">
                  <a-switch v-model="entity.setting.auth" type="round" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮件服务器主机地址" :rules="[{ required: true, message: '请输入邮件服务器主机地址' }]"
                  field="setting.host">
                  <a-input v-model="entity.setting.host" placeholder="请输入邮件服务器主机地址" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮件服务器端口" :rules="[{ required: true, message: '请输入邮件服务器端口' }]" field="setting.port">
                  <a-input v-model="entity.setting.port" placeholder="请输入邮件服务器端口" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮件服务器用户名" :rules="[{ required: true, message: '请输入邮件服务器用户名' }]"
                  field="setting.username">
                  <a-input v-model="entity.setting.username" placeholder="请输入邮件服务器用户名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮件服务器密码" :rules="[{ required: true, message: '请输入邮件服务器密码' }]"
                  field="setting.password">
                  <a-input-password v-model="entity.setting.password" placeholder="请输入邮件服务器密码" />
                </a-form-item>
              </a-col>
            </a-row>

            <limit-setting v-model:limitSetting="entity.setting.limitSetting" :model-fields="modelFields"/>

          </a-card>
        </a-space>
      </a-form>
    </template>
    <template #action></template>
  </module>
</template>

<script>
import { ref, provide } from "vue";
import { useRoute, useRouter } from "vue-router";
import { findItem, saveInfo } from "@/api/channel";
import { findCustomerModel } from "@/api/system";
import { formatFields } from "@/utils/field"
import LimitSetting from '@/components/ma/limit-setting/index.vue'

export default {
  components: {
    LimitSetting
  },
  setup() {
    const route = useRoute();
    const router = useRouter();

    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: "系统设置",
          path: "/system/setting",
        },
        {
          name: "沟通渠道管理",
          path: "/system/reach",
        },
        {
          name: "邮件渠道管理",
          path: "/system/reach/email",
        },
      ],
      mainPath: "/system/reach",
    });

    const protocols = ref([
      { id: 'SMTP', name: 'SMTP' }
    ]);
    const fieldStruct = ref({
      key: 'path',
      value: 'path',
      title: 'label',
      children: 'fields',
    });

    const formRef = ref(null);
    // 获取字段数据
    const modelFields = ref([]);

    const entity = ref({
      type: "email",
      category: "reach",
      setting: {
        type: "email",
        limitSetting: {
          startTime: "00:00:00",
          endTime: "23:59:59"
        }
      },
    });

    const bindData = async () => {
      if (route.query.id) {
        entity.value = await findItem(route.query.id);
        entity.value = entity.value ? entity.value : {};
      }
      if (entity.value.setting?.limitSetting && !entity.value.setting.limitSetting.dateLimit) {
        entity.value.setting.limitSetting.dateLimit = {}
      }
      findCustomerModel().then((data) => {
        if (data) {
          modelFields.value = formatFields(data.fields, "");
        }
      });
    };

    const save = async () => {
      formRef.value.validate((err) => {
        if (!err) {
          saveInfo(entity.value).then(() => {
            router.push({ path: module.value.mainPath });
          });
        }
      });
    };

    const setup = {
      formRef,
      module,
      entity,
      fieldStruct,
      protocols,
      modelFields,
      bindData,
      save,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>
