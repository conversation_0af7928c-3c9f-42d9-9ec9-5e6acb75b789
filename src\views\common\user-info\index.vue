<template>
  <div class="container">
    <a-row style="margin-bottom: 16px">
      <a-col :span="24">
        <a-card :border="false">
          <a-space direction="vertical" size="large" fill>
            <a-descriptions
              :data="data"
              title="用户信息"
              layout="inline-horizontal"
              :align="{ label: 'right' }"
            />
          </a-space>
        </a-card>
      </a-col>
    </a-row>
    <a-row class="wrapper">
      <a-col :span="24">
        <a-tabs default-active-key="1" type="rounded">
          <a-tab-pane key="1" title="基础信息"> </a-tab-pane>
          <a-tab-pane key="2" title="安全设置"> </a-tab-pane>
        </a-tabs>
      </a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  name: "Setting",
  setup() {
    const data = [
      {
        label: "Name",
        value: "Socrates",
      },
      {
        label: "Mobile",
        value: "123-1234-1234",
      },
      {
        label: "Residence",
        value: "Beijing",
      },
      {
        label: "Hometown",
        value: "Beijing",
      },
      {
        label: "Address",
        value: "Yingdu Building, Zhichun Road, Beijing",
      },
    ];

    return {
      data,
    };
  },
};
</script>

<style scoped lang="less"></style>
