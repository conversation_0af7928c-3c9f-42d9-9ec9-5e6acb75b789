<template>
  <div class="easyflow-pannel-wechat">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="微信沟通类型" class="form-item-select">
        <a-select v-model="entity.wechatType" class="easyflow-select" placeholder="请选择微信沟通类型"
          @change="handleChangeWechatType()">
          <a-option v-for="item of wechatMsgTypes" :key="item.id" :value="item.id">{{ item.name }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="微信沟通模板" class="form-item-select">
        <a-select v-model="entity.capabilityId" class="easyflow-select" placeholder="请选择微信沟通模板" :loading="loading"
          allow-search @change="showContent(entity.capabilityId)">
          <a-option v-for="item of communicates" :key="item.id" :label="item.label" :value="item.id">
            [{{ item.name }}] <span class="light-text"> {{ item.id }} </span>
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="模板示例">
        <span class="wechat-content">
          {{ content }}
        </span>
      </a-form-item>
      <!-- <a-form-item label="动态绑定数据">
        <a-select
          v-model="entity.bindName"
          :disabled="bindNames.length == 0"
          placeholder="请选择动态绑定数据"
        >
          <a-option v-for="item of bindNames" :key="item" :value="item">{{
            item
          }}</a-option>
        </a-select>
      </a-form-item> -->
      <!-- <a-form-item label="预算设置" :content-flex="false">
        <a-checkbox v-model="entity.budgetSetting.enabled" class="budget" @change="toogleBudeget">
          参与计算费用 ( 单价: <span> {{ unitBudget }}</span>元 )
        </a-checkbox>
        <div v-if="entity.budgetSetting.enabled">
          <div class="flex-line">
            <a-select v-model="entity.budgetSetting.budgetItemId" class="easyflow-select" placeholder="请选择预算条目"
              :loading="loading" :filter-option="false" @search="handleSearchBudget">
              <a-option v-for="item of budgets" :key="item.id" :value="item.id">{{ item.name }}</a-option>
            </a-select>
            <a-button class="btn" type="outline" @click="addBudgetItemRef.show()">新增</a-button>
          </div>
        </div>
        <template #extra>
          <div class="tip">预算不足时停止发送</div>
        </template>
</a-form-item> -->
      <a-form-item label="沟通限制" :content-flex="false">
        <a-checkbox v-model="entity.frequencyLimit" class="budget">限制沟通次数</a-checkbox>
        <template #extra>
          <div class="tip">
            超出客户的沟通频次限制时，沟通消息不会发送，并且该客户会停在当前节点不再继续执行
          </div>
        </template>
      </a-form-item>
      <a-form-item label="勿扰策略">
        <SilenceSelect v-model:silenceRuleId="entity.silenceRuleId" />
      </a-form-item>
    </a-form>

    <AddBudgetItemDlg ref="addBudgetItemRef" :budget-id="budgetId" :bind-data="handleSearchBudget" />
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { wechatMsgTypes } from "@/constant/capability";
import { findCommunicateList, findCommunicateItem } from "@/api/communicate";
import { findBudgetItemList } from "@/api/budget";
import AddBudgetItemDlg from "@/components/modal-dlg/add-budget-item-dlg.vue";
import SilenceSelect from "@/components/ma/silence-select/index.vue"

const props = defineProps(["node", "easyflow"]);
const { node } = props;
const { easyflow } = props;
const flowIjt = inject("flow");
const budgetId = ref(flowIjt.flowId);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const entity = ref({
  capabilityId: null,
  budgetSetting: {
    enabled: false,
    budgetId: budgetId.value
  },
  frequencyLimit: true
});
const content = ref("");
const communicates = ref([]);
const budgets = ref([]);
const bindNames = ref([]);
const unitBudget = ref(0);
const addBudgetItemRef = ref(null);
const loading = ref(false);

const handleSearchCommunicate = async (name) => {
  loading.value = true;
  const params = {
    fields: "name,content,budgetSetting",
    expression: `setting.type eq ${entity.value.wechatType} AND status eq ENABLED`
  };
  if (name) {
    params.expression += ` AND name like ${name}`;
  }
  communicates.value = await findCommunicateList(params);
  communicates.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
  loading.value = false;
};

const handleChangeWechatType = async () => {
  entity.value.capabilityId = null;
  handleSearchCommunicate();
};

const handleSearchBudget = async (name) => {
  loading.value = true;
  const params = {
    fields: "name",
    expression: `budgetId eq ${budgetId.value}`
  };
  if (name) {
    params.expression += ` AND name like ${name}`;
  }
  budgets.value = await findBudgetItemList(params);
  loading.value = false;
};

const showContent = async (capabilityId) => {
  if (capabilityId) {
    const communicate = await findCommunicateItem(capabilityId);
    content.value = communicate.content;
    unitBudget.value = communicate.budget;
    entity.value.budgetSetting.budgetValue = communicate.budget;
  }
};

const toogleBudeget = () => {
  if (entity.value.budgetSetting.enabled) {
    entity.value.budgetSetting.budgetId = budgetId.value;
  }
};
const getBindNames = () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  bindNames.value = easyflow.getBindNames(nodeChain);
};

const save = () => {
  return entity.value;
};

defineExpose({
  save
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  if (entity.value.wechatType) {
    handleSearchCommunicate();
  }
  showContent(entity.value.capabilityId);
  // handleSearchBudget();
  // getBindNames();
});
</script>

<style lang="less" scoped>
.easyflow-pannel-wechat {
  .wechat-content {
    color: #b3b3b3;
  }

  .budget {
    color: rgb(var(--primary-6));

    .tip {
      color: #b3b3b3;
    }
  }
}
</style>
