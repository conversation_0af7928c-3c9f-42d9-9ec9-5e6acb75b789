<template>
  <module main>
    <template #filter> </template>
    <template #search> </template>
    <template #action>
        <a-button v-permission="['ma_menu.system.behavior-event.create']" type="primary" @click="editItem()">
          <template #icon>
            <icon-plus />
          </template>
          {{t('global.button.create')}}
        </a-button>
      </template>
    <template #context><span /></template>
    <template #main>
      <a-table ref="table" row-key="id" :bordered="false" :data="dataSource" :pagination="false">
        <template #columns>
          <a-table-column :title="t('systemSetting.behaviorEvent.id')" data-index="id" :width="30" />
          <a-table-column :title="t('systemSetting.behaviorEvent.name')" data-index="name" :width="60" />
          <a-table-column :title="t('systemSetting.behaviorEvent.modelId')" data-index="modelId" :width="60">
            <template #cell="{ record }">
              <a-link :href="'#/system/behavior-model/edit?id=' + record.modelId">{{ selectItem(record.modelId) }}</a-link>
            </template>
          </a-table-column>
          <a-table-column :title="t('systemSetting.behaviorEvent.status')"  data-index="status" :width="30" >
            <template #cell="{ record }">
              <a-tag v-if="record.status === 'ENABLED'" color="#165dff"> {{t('global.button.enable')}}</a-tag>
              <a-tag v-if="record.status === 'DISABLED'" color="#86909c"> {{t('global.button.disable')}}</a-tag>
            </template>
          </a-table-column>
          <a-table-column :title="t('systemSetting.behaviorEvent.summary')"  data-index="summary" :width="80" :ellipsis="true" :tooltip="{class:'tooltip-content'}" />
          <a-table-column :title="t('systemSetting.behaviorEvent.createdTime')"  data-index="createdTime" :width="30">
            <template #cell="{ record }">
              {{ $moment(record.createdTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </a-table-column>
          <a-table-column :title="t('global.button.operation')"  align="center" :width="30">
            <template #cell="{ record }">
              <a-button v-permission="['ma_menu.system.behavior-event.modify']" type="text" size="small" @click="detail(record)">{{t('global.button.edit')}}</a-button>
              <a-button v-permission="['ma_menu.system.behavior-event.delete']" type="text" size="small" @click="deleteData(record)">{{t('global.button.delete')}}</a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </template>
  </module>
</template>

<script>
import {
  ref,
  provide,
  computed,
  onMounted,
  getCurrentInstance
} from "vue";

import { useRouter } from "vue-router";
import {
  findBehaviorEventPage,
  deleteBehaviorEvent,
  findBehaviorModelList,
} from "@/api/system";
import { Modal } from "@arco-design/web-vue";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    // 路由API
    const router = useRouter();

    // 模块设置
    const module = ref({
      entityIdField: "id",
      entityName: t('systemSetting.basic.behaviorEvent'),
      breadcrumb: [
        {
          name:  t('menu.system'),
          path: "/system/setting",
        },
        {
          name:  t('systemSetting.basic.behaviorEvent'),
        },
      ],
      editPath: "/system/behavior-event/edit",
      createPath: "/system/behavior-event/edit",
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true,
    });

    // 过滤设置
    const filter = ref([
      {
        field: "name",
        label: t('systemSetting.behaviorEvent.name'),
        component: "a-input",
        operate: "like",
        placeholder:  t('systemSetting.behaviorEvent.name'),
        comment: true,
        value: "",
      },
    ]);

    // 数据设置
    const entity = ref({

    });

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    const editItem = async (id) => {
      const query = { id };
      router.push({ path: module.value.editPath, query });
    };

    // 查询API
    const bindData = async (expression) => {
      entity.value = await findBehaviorEventPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        { expression, fields: "name,code,status,modelId,createdTime" }
      );
      pagination.value.total = entity.value.totalElements;
    };
    // 详情跳转
    const detail = (row) => {
      const query = {};
      query[module.value.entityIdField] = row[module.value.entityIdField];
      router.push({ path: module.value.editPath, query });
    };

    // 新建跳转
    const create = () => router.push({ path: module.value.createPath });

    const deleteData = async (record) => {
      Modal.confirm({
        title: t('global.button.delete'),
        content: t('global.tips.warning.delete'),
        onOk: async () => {
          await deleteBehaviorEvent(record.id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page--;
          }
          await bindData();
        },
      });
    };
    const modelList = ref([]);
    onMounted(() => {
      findBehaviorModelList().then((res) => {
        modelList.value = res;
      });
    });
    // 筛选行为模型
    const selectItem = (modelId) => {
      const item = modelList.value.find((x) => {
        return x.id === modelId;
      });
      return item?.aliasName || "";
    };

    const setup = {
      t,
      module,
      filter,
      entity,
      detail,
      create,
      editItem,
      bindData,
      dataSource,
      pagination,
      modelList,
      selectItem,
      deleteData,
    };
    provide("main", setup);
    return setup;
  },
};
</script>
