<template>
  <div class="easyflow-pannel-unique">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item key="reachField" label="触达字段">
        <a-tree-select v-model="entity.reachField" :allow-search="true" :data="flowModel" allow-clear
          :field-names="treeFieldStruct" placeholder="请选择字段">
        </a-tree-select>
      </a-form-item>

      <a-form-item label="批次大小">
        <a-input-number v-model="entity.number" />
      </a-form-item>

    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
import { treeFieldStruct } from "@/constant/common"
import { getFlowModelsByIds } from "@/api/campaign";
import { formatFields } from "@/utils/field"

const props = defineProps(["node", "connections", "easyflow"]);
const { node } = props;
const { connections } = props;
const { easyflow } = props;

const loading = ref(false);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;
const entity = ref({});
const flowModel = ref(null);

const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = formatFields(modelFields.fields, "");
};

const changeTreeSelect = (val, _item) => {

};

const save = () => {
  return entity.value;
};

defineExpose({
  save,
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  handelFlowModel();
});
</script>


<style lang="less" scoped></style>
