import i18n from "../locale";

export const stringStatus = [
    {
        // label: "启用",
        label: i18n.global.t("global.button.enable"),
        value: "ENABLED",
    },
    {
        // label: "禁用",
        label: i18n.global.t("global.button.disable"),
        value: "DISABLED",
    },
];

export const booleanStatus = [
    {
        // label: "启用",
        label: i18n.global.t("global.button.enable"),
        value: true,
    },
    {
        // label: "禁用",
        label: i18n.global.t("global.button.disable"),
        value: false,
    },
];

export const treeFieldStruct = {
    key: "path",
    value: "path",
    title: "label",
    children: "fields",
};

export const timePatterns = [
    { label: "yyyy-MM-dd HH:mm:ss", value: "yyyy-MM-dd HH:mm:ss" },
    { label: "yyyy-MM-dd HH:mm", value: "yyyy-MM-dd HH:mm" },
    { label: "yyyy-MM-dd", value: "yyyy-MM-dd" },
    { label: "yyyy", value: "yyyy" },
    { label: "MM", value: "M" },
    { label: "dd", value: "d" },
    { label: "HH", value: "H" },
];

