/**
 * 排序节点
 */
<template>
  <div class="easyflow-pannel-sort">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="排序方式" class="form-item-select">
        <a-radio-group v-model="entity.sortType" type="button" size="mini">
          <a-radio value="DESC">从大到小</a-radio>
          <a-radio value="ASC">从小到大</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="排序字段" class="form-item-select">
        <a-tree-select v-model="entity.sortField" :data="flowModel.fields" :field-names="treeFieldStruct" placeholder="请选择排序字段" @change="changeTreeSelect">
        </a-tree-select>
      </a-form-item>

      <a-form-item label="限制数量">
        <a-input-number v-model="entity.limit" />
      </a-form-item>
      <a-form-item label="限制数量分支" :content-flex="false" class="form-item-select">
        <template v-for="(item, index) in entity.branches" :key="index">
          <a-row class="branch-item" :gutter="24">
            <a-col :span="10" class="item-label">
              {{ item.name }}
            </a-col>
            <a-col :span="10">
              <a-radio-group v-model="item.type" type="button">
                <a-radio value="LIMIT">限制数量</a-radio>
                <a-radio value="OTHER">其余</a-radio>
              </a-radio-group>
            </a-col>
          </a-row>
        </template>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { treeFieldStruct } from "@/constant/common"
import { getFlowModelsByIds } from "@/api/campaign";
import { formatFields } from "@/utils/field"

const props = defineProps(["node", "easyflow", "connections"]);
const { node } = props;
const { easyflow } = props;
const { connections } = props;
const loading = ref(false);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;
const entity = ref({
  sortType: "DESC",
  sorts: [],
  branches: [],
});
const flowModel = ref({});

const save = () => {
  return entity.value;
};

// 获取字段数据
const getCurItem = (data, val) => {
  let result = null;
  if (!data) return; // return; 中断执行
  for (const i in data) {
    if (result !== null) break;
    const item = data[i];
    if (item.path === val) {
      result = item;
      break;
    } else if (item?.fields?.length) {
      result = getCurItem(item.fields, val);
    }
  }
  return result;
};

const changeTreeSelect = () => {
  const x = getCurItem(flowModel.value.fields, entity.value.sortField);
  entity.value.sortFieldAliasName = x.aliasName;
};

const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = {
    ...modelFields,
    allowNull: true,
    arrayType: false,
    type: "NESTED",
  };
  flowModel.value.fields = formatFields(flowModel.value.fields, "");
};
const refreshBranchs = () => {
  // 去除无效分支
  if (connections.outgoings) {
    entity.value.branches = connections.outgoings.map((it) => {
      const found = entity.value.branches.find((item, index) => {
        return item.outgoing === it.id;
      });
      if (found) {
        found.name = it.name;
        return found;
      }
      return {
        name: it.name,
        outgoing: it.id,
        type: "EVENT",
      };

    });
  } else {
    entity.value.branches = [];
  }
};
defineExpose({
  save,
});
onMounted(async () => {
  Object.assign(entity.value, node.data);
  refreshBranchs();
  await handelFlowModel();
});
</script>


<style lang="less" scoped>
.sort-line {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 10px 0;
}

.branch-item {
  margin: 10px;
}

.arco-icon-delete {
  cursor: pointer;
}
</style>
