<template>
  <div class="ma-marketing-taskpad">
    <div class="group-container">
      <vue-drag v-model="groupList" animation="300" item-key="id" @end="onGroupEnd">
        <template #item="{ element }">
          <span v-if="!hidenEmptyGroup ||
            (campaignGroups[element.id] != null &&
              campaignGroups[element.id].length > 0)
            ">
            <div class="group">
              <div class="group-title">
                <span class="title-name theme-color">
                  <span v-if="element.id !== editGroup">{{
                    element.name
                  }}</span>
                  <span v-if="element.id == editGroup">
                    <a-input v-model="element.name" @blur="onBlur($event, element)"></a-input>
                  </span>
                </span>
                <a-dropdown>
                  <a-button size="mini" type="text">
                    <template #icon>
                      <icon-more-vertical />
                    </template>
                  </a-button>
                  <template #content>
                    <a-doption @click="handleEditGroup($event, element)">编辑</a-doption>
                    <a-doption @click="handleDeleteGroup($event, element)">删除</a-doption>
                  </template>
                </a-dropdown>
              </div>
              <div class="group-summary"></div>
              <div class="group-task-list">
                <vue-drag v-model="campaignGroups[element.id]" group="site" animation="300" item-key="id"
                  @add="onAdd(element.id)" @remove="onRemove(element.id)" @update="onUpdate(element.id)">
                  <template #item="{ element }">
                    <task-card ref="task" :key="element.id" :task="element"
                      :handle-modify-task="handleModifyTask"></task-card>
                  </template>
                </vue-drag>
                <div class="task-create" @click="handleCreateTask($event, element)">
                  <span class="iconfont icon-create"></span>
                  <span>创建新的任务</span>
                </div>
              </div>
            </div>
          </span>
        </template>
      </vue-drag>
      <task-Group-create :group-change="getGroupList" />
      <task-edit-drawer ref="taskDrawer" :task="task" :save-task="handleSaveTask" />
    </div>
  </div>
</template>

<script>
import { Modal, Notification, Message } from "@arco-design/web-vue";
import { getCurrentInstance } from "vue";
import vuedraggable from "vuedraggable";
import {
  createCampaign,
  modifyCampaign,
  saveCampaignList,
} from "@/api/campaign";
import { saveGroup, deleteGroup, saveGroupList } from "@/api/group";
import taskCard from "./taskcard.vue";
import taskEditDrawer from "./task-edit-drawer.vue";
import taskGroupCreate from "./task-group-create.vue";


export default {
  components: {
    "vue-drag": vuedraggable,
    "task-card": taskCard,
    "task-Group-create": taskGroupCreate,
    "task-edit-drawer": taskEditDrawer,
  },

  props: {
    hidenEmptyGroup: {
      type: Boolean,
      default: () => true,
    },
    groupList: {
      type: Array,
      default: () => [],
    },
    campaignList: {
      type: [Array, Object],
      default: () => { },
    },
    campaignStatus: {
      type: [Array, Object],
      default: () => { },
    },
    campaignGroups: {
      type: [Array, Object],
      default: () => { },
    },
    getGroupList: Function,
    getCampaignList: Function,
    modifyTask: Function,
  },
  setup(props) {
    const {
      proxy: { t }
    } = getCurrentInstance();

    return {t};
  },
  data() {
    return {
      editGroup: null,
      actived: false,
      loading: false,
      drawerVisible: false,
      editTitle: null,
      createGroup: {
        name: null,
        summary: null,
      },
      task: {
        tags: [],
        name: "",
        flowData: "",
        groupId: null,
        type: "EVNET",
        endTime: new Date(),
        startTime: new Date(),
        createdBy: null,
        updatedBy: null,
        status: "DRAFT",
        updatedName: null,
        updatedTime: null,
        createdName: null,
        createdTime: null,
        campaignCode: null,
        setting: {
          repeatAllow: true,
        },
      },
    };
  },
  computed: {},
  methods: {
    async onAdd(groupId) {
      await this.updateCampaignOrder(groupId);
    },
    async onRemove(groupId) {
      await this.updateCampaignOrder(groupId);
    },
    async onUpdate(groupId) {
      await this.updateCampaignOrder(groupId);
    },
    async updateCampaignOrder(groupId) {
      const campaigns = [];
      for (
        let index = 0;
        index < this.campaignGroups[groupId].length;
        index++
      ) {
        const element = this.campaignGroups[groupId][index];
        if (element.groupId != groupId || element.priority != index) {
          element.priority = index;
          campaigns.push(element);
        }
      }
      const newPriority = campaigns.map((i) => {
        return {
          id: i.id,
          priority: i.priority,
        };
      });
      await saveCampaignList(groupId, newPriority);
    },
    async onGroupEnd(e) {
      this.groupList.forEach((i, index) => (i.priority = index));
      const newPriority = this.groupList.map((i) => {
        return {
          id: i.id,
          priority: i.priority,
        };
      });
      await saveGroupList(newPriority);
    },
    async onBlur(e, group) {
      this.editGroup = null;
      this.loading = true;
      await saveGroup(group);
      this.loading = false;
    },

    handleEditGroup(e, group) {
      this.editGroup = group.id;
    },

    handleCreateGroup() {
      this.actived = true;
    },

    handleCreateTask(e, group) {
      e.stopPropagation();
      this.$refs.taskDrawer.createTask(group);
    },

    handleModifyTask(e, task) {
      e.stopPropagation();
      this.$refs.taskDrawer.editTask(task);
    },

    handleDeleteGroup(e, group) {
      if (this.campaignGroups[group.id].length > 0) {
        Message.warning("请先移除出分组下的活动！");
        return false;
      }
      Modal.confirm({
        title: "删除营销活动分组",
        content: `是否确认删除营销活动分组 ${group.name}？`,
        onOk: async () => {
          await deleteGroup(group.id);
          Notification.info({
            title: "删除营销活动分组成功。",
          });
          await this.getGroupList();
        },
      });
    },
    async handleSaveTask(task) {
      if (task.id) {
        await modifyCampaign(task);
      } else {
        await createCampaign(task);
      }
      Notification.info({
        title: "保存营销活动",
        content: "营销活动保存成功。",
      });
      await this.getCampaignList();
    },
  },
};
</script>

<style lang="less" scoped>
.ma-marketing-taskpad {
  width: 100%;

  .group-container {
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;

    >div {
      display: flex;
    }

    .group {
      width: 270px;
      min-width: 270px;
      border-radius: 4px;
      margin: 0 20px 20px 0;
      box-sizing: border-box;

      .group-title {
        height: 35px;
        display: flex;
        padding: 0 10px;
        font-size: 14px;
        align-items: center;
        box-sizing: border-box;
        cursor: move;
        justify-content: space-between;
        background: rgba(0, 0, 0, 0.02);

        .title-name {
          font-weight: bold;
          cursor: pointer;

          &:hover {
            opacity: 0.7;
          }
        }

        .group-action-menu {
          box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.07);

          ul {
            list-style: none;
            padding: 5px 0;

            li {
              cursor: pointer;
              font-size: 12px;
              color: #666666;
              padding: 2px 10px;
              transition: all ease 0.3s;

              &:hover {
                color: #333333;
                background: rgba(0, 0, 0, 0.05);
              }
            }
          }
        }
      }

      .group-summary {
        font-size: 12px;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }

      .group-task-list {
        padding: 10px;
        min-height: 50px;
        background: rgba(0, 0, 0, 0.02);

        .task-create {
          width: 260px;
          height: 30px;
          font-size: 14px;
          cursor: pointer;
          color: #999999;
          margin-top: 10px;
          line-height: 30px;
          box-sizing: border-box;

          &:hover {
            color: #000000;
          }

          .iconfont {
            margin: 2px;
          }
        }
      }
    }
  }
}
</style>
