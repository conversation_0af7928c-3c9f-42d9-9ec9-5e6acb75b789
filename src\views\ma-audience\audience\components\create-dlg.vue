/** * 选择人群创建类型 */
<template>
  <template v-if="showVisible">
    <a-modal v-model:visible="showVisible" :title="t('audience.popup.create_title')" @cancel="showVisible = false" @before-ok="handleOk">
      <a-radio-group v-model="modelChecked" :default-value="0">
        <template v-for="item in modelData" :key="item.title">
          <a-radio :value="item.type">
            <template #radio="{ modelChecked }">
              <div class="custom-radio-card">
                <div className="custom-radio-card-title">{{ item.title }}</div>
                <div className="custom-radio-card-desc">{{ item.desc }}</div>
              </div>
            </template>
          </a-radio>
        </template>
      </a-radio-group>
    </a-modal>
  </template>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";

const {
      proxy: { t }
    } = getCurrentInstance()

const router = useRouter();
const showVisible = ref(false);

const module = ref({
  createConditionPath: "/audience/edit-condition",
  createImportPath: "/audience/edit-manual",
});

const modelChecked = ref(0);
const modelData = [
  {
    // title: "条件创建人群",
    title: t('audience.popup.condtion_create_title'),
    // desc: "通过条件过滤生成新的人群",
    desc: t('audience.reminder.generate_new_audience_by_filtering'),
    type: "CONDITION",
  },
  /* {
        title: "交并差创建人群",
        desc: "使用已有的人群包，通过逻辑交互生成新的人群",
        type: "SET",
      }, */
  // { title: "导入人群", desc: "从外部导入人群", type: "MANUAL" },
];

const handleOk = () => {
  if (modelChecked.value === "MANUAL") {
    router.push({ path: module.value.createImportPath });
  } else {
    router.push({ path: module.value.createConditionPath });
  }
};

const show = () => {
  showVisible.value = true;
};

defineExpose({ show });
</script>

<style lang="less" scoped>
.arco-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.arco-radio {
  width: calc(50% - 10px);
  margin-bottom: 20px;

  &:nth-child(2n) {
    margin-right: 0;
  }
}

.custom-radio-card {
  border: 1px solid var(--color-border-2);
  padding: 10px 15px;
  width: 100%;
  min-height: 90px;
  border-radius: 4px;

  .custom-radio-card-title {
    font-size: 16px;
    color: #333333;
    font-weight: bold;
  }

  .custom-radio-card-desc {
    font-size: 12px;
    color: #999999;
  }
}

.arco-radio-checked {
  .custom-radio-card {
    border-color: rgb(var(--primary-6));
  }

  .custom-radio-card-title {
    color: rgb(var(--primary-6));
  }
}
</style>
