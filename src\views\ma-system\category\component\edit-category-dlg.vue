<template>
  <template v-if="visible">
    <a-modal v-model:visible="visible" :title="isEdit ? '编辑分类' : '新增分类'" @cancel="visible = false"
      @before-ok="saveEntity">
      <a-form ref="dataFormRef" :model="entity">
        <a-form-item field="id" :label="t('systemSetting.activityCategory.id')" :disabled="isEdit" :rules="[{ required: true, message: t('systemSetting.activityCategory.enterId') }]" label-col-flex="70px">
          <a-input v-model="entity.id" :placeholder="t('systemSetting.activityCategory.enterId')" />
        </a-form-item>
        <a-form-item field="name" :label="t('systemSetting.activityCategory.name')" :rules="[{ required: true, message: t('systemSetting.activityCategory.enterName') }]" label-col-flex="70px">
          <a-input v-model="entity.name" :placeholder="t('systemSetting.activityCategory.enterName')" />
        </a-form-item>
        <a-form-item field="summary" :label="t('systemSetting.activityCategory.summary')" label-col-flex="70px">
          <a-textarea v-model="entity.summary" type="textarea" :placeholder="t('systemSetting.activityCategory.summary')" />
        </a-form-item>
      </a-form>
    </a-modal>
  </template>
</template>

<script setup>
import { ref ,getCurrentInstance} from "vue";
import { modalCommit } from "@/utils/modal";
import { createCategory, modifyCategory } from "@/api/category";

const {
      proxy: { t }
    } = getCurrentInstance()
const visible = ref(false);
const isEdit = ref(false);
const entity = ref({});
const dataFormRef = ref({});
let callback;

const saveEntity = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    if(isEdit.value){
      await modifyCategory(entity.value);
    } else {
      await createCategory(entity.value);
    }
    callback();
  });
};

const showCreate = () => {
  entity.value = {};
  isEdit.value = false;
  visible.value = true;
  return new Promise((resolve, reject) => {
    callback = resolve;
  });
};

const showEdit = (data) => {
  entity.value = data ? JSON.parse(JSON.stringify(data)) : {};
  isEdit.value = true;
  visible.value = true;
  return new Promise((resolve, reject) => {
    callback = resolve;
  });
};

defineExpose({ showCreate, showEdit });
</script>

<style lang="less" scoped></style>
