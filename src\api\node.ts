/**
 * 分组管理接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function createNode(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/node-config`;
  return axios.post(uri, info) ;
}

export function modifyNode(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/node-config`;
  return axios.put(uri, info);
}

export function deleteNode(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/node-config/${id}`
  );
}

export function getNode(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/node-config/${id}`);
}

export function findNodeLists(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/node-config/list`, {
    params,
  });
}

export function findNodeList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/node-config/list/view`, {
    params,
  });
}

export function findNodePage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/node-config`, {
    params: {
      ...params,
      ...query,
    },
  });
}

