<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { findAudienceList } from "@/api/audience";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    const item = nodeApi.find(item => { return item.type === 'intersect' })
    const setup = {
      title: item.name || "交集",
      summary: item.name || "交集节点",
      iconClass: item.icon || "icon-cross",
      nodeClass: "easyflow-node-end",
      headerColor: item.themeColor|| "#fba980",
      headerBgColor: item.themeColor || "#fba980",
      background: item.background || "#fff9ef",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
