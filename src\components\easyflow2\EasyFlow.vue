<template>
  <div id="flow" class="easyflow">
    <div id="easyflow-content" ref="easyflowRef" class="easyflow-content">
      <flow-menu v-if="visible && editEnable" />
      <flow-toolbar :graph="graph" />
      <flow-main ref="flowMainRef" />
      <flow-minimap ref="minimapRef" :graph="graph" />
      <node-pannel ref="nodePannelRef" />
      <monitor-pannel ref="monitorPannelRef" />
    </div>
  </div>
</template>

<script setup>
import { ref, provide, onMounted } from "vue";
import { onBeforeRouteLeave } from "vue-router";
import { addEventListen } from "@/utils/event";
import { Graph } from "@antv/x6";
import { findNodeList } from "@/api/node"
import FlowMain from "./components/main/Main.vue";
import FlowMenu from "./components/menu/Menu.vue";
import { NodePannel, MonitorPannel } from "./components/pannel";
import FlowToolbar from "./components/toolbar/Toolbar.vue";
import FlowMinimap from "./components/minimap/Minimap.vue";
import EasyFlow from "./easyflow";
import { Nodes, NodeGroups, getPannel, getHelp } from "./nodes";

// PROPS
const props = defineProps([
  "entity",
  "flowId",
  "flow_data",
  "simulate",
  "simulateEvent",
  "validate",
  "monitor",
  "started",
  "onChange",
  "editEnable",
  "refreshDebugRecord",
]);

const visible = ref(false);
const editEnable = ref(props.editEnable);

const easyflow = ref(null);
const graph = ref(null);
const flowdata = ref(null);
const isMonitor = ref(false);
const isDebug = ref(false);
const monitorData = ref(null);

const nodeGroups = ref(NodeGroups);

// REF
const flowMainRef = ref(null);
const nodePannelRef = ref(null);
const monitorPannelRef = ref(null);
const minimapRef = ref(null);
const easyflowRef = ref(null);
const instanceId = ref(props.flowId);

// let simInterval;
let monitorInterval;

/**
 * 切换配置面板
 */
const toogleConfigPannel = (is, node, connections) => {
  const pannel = getPannel(node.data._type);
  nodePannelRef.value.showPannel(pannel);
  const help = getHelp(node.data._type);
  if (help) {
    nodePannelRef.value.showHelp(help);
  }
  nodePannelRef.value.tooglePannel(is, node, connections);
};

/**
 * 切换监控面板
 */
const toogleMonitorPannel = (is, node, connections) => {
  const taskMonitorData = monitorData.value.tasks.find(
    (it) => it.taskId === node.id
  );
  const help = getHelp(node.data._type);
  if (help) {
    monitorPannelRef.value.showHelp(help);
  }
  monitorPannelRef.value.tooglePannel(is, node, connections, taskMonitorData);
};

// 切换监控状态
const toogleMonitor = async (enable, _instanceId, engineType) => {
  if (enable) {
    instanceId.value = _instanceId;
    monitorData.value = await props.monitor(enable, _instanceId, engineType);
    easyflow.value.monitor(enable, monitorData.value);
    isMonitor.value = enable;
    if (props.entity.status === "RUNNING") {
      monitorInterval = setInterval(async () => {
        if (!isMonitor.value) {
          clearInterval(monitorInterval);
        } else {
          monitorData.value = await props.monitor(enable, _instanceId, engineType);
          easyflow.value.monitor(enable, monitorData.value);
        }
      }, 10000);
    }
  } else {
    isMonitor.value = enable;
    clearInterval(monitorInterval);
    easyflow.value.monitor(enable);
    instanceId.value = props.flowId;
    // easyflow.value.showRecord(enable);
  }
};
/**
 * 切换面板
 */
const tooglePannel = (is = false, node, connections) => {
  if (isMonitor.value) {
    toogleMonitorPannel(is, node, connections);
  } else {
    toogleConfigPannel(is, node, connections);
  }
};

// 保存流程
const save = () => {
  nodePannelRef.value.handleCancel();
};
// 获取流程数据
const getData = () => {
  return easyflow.value.toJSON();
};
// 切换模拟状态
const toogleSimulate = async (value) => {
  isDebug.value = value;
  if (value) {
    // const records = await props.refreshDebugRecord();
    // easyflow.value.showRecord(value, records);
    editEnable.value = false;
    props.entity.status = "RUNNING";
    // 判断是否有正在运行的调试实例
    // simInterval = setInterval(async () => {
    //   const sRecords = await props.refreshDebugRecord();
    //   easyflow.value.showRecord(value, sRecords);
    // }, 10000);
  } else {
    // clearInterval(simInterval);
    // easyflow.value.showRecord(value);
    props.entity.status = "DRAFT";
    editEnable.value = true;
  }
  easyflow.value.toogleEdit(!value);
};

const getNode = (shape) => {
  return easyflow.value.getNode(shape);
};

const getNodeAttr = (shape, id, attrName) => {
  return easyflow.value.getNodeAttr(shape, id, attrName);
};

const categorizeByGroupId = (data) => {
  const categorizedData = [];
  data = data.sort((a, b) => a.index - b.index)
  data.forEach(item => {
    const existingCategory = categorizedData.find(category => category.category === item.groupName);

    if (existingCategory) {
      existingCategory.nodes.push(item);
    } else {
      categorizedData.push({
        groupIndex: item.groupIndex,
        category: item.groupName,
        color: item.themeColor,
        iconClass: item.icon,
        themebg: item.background,
        nodes: [item]
      });
    }
  });

  return categorizedData.sort((a, b) => a.groupIndex - b.groupIndex);
}


onMounted(async () => {

  let nodes = [];

  // 动态获取节点
  await findNodeList().then(res => {
    localStorage.setItem('nodeApi', JSON.stringify(res))
    const list = []
    res.forEach(item => {
      const node = Nodes.find(x => { return x.type === item.type })
      if (node && item.enabled) {
        list.push({
          ...node,
          ...item,
          color: item.themeColor,
          iconClass: item.icon,
          themebg: item.background,
        })

        const { themeColor, id, icon, background, name } = item

        const nodeItem = JSON.parse(localStorage.getItem('nodeItem')) || {}
        nodeItem[id] = { themeColor, id, icon, background, name }
        localStorage.setItem('nodeItem', JSON.stringify(nodeItem))
      }
    })

    // 初始化开始
    const startNode = Nodes.find(x => { return x.type === 'start' })
    if (startNode) {
      startNode.registerNode()
    }
    nodes = nodes.concat(list);
    nodeGroups.value = categorizeByGroupId(list)
  })

  visible.value = true;

  easyflow.value = new EasyFlow({
    simulate: props.simulate,
    target: flowMainRef.value.$el,
    minimap: minimapRef.value.$el,
    tooglePannel,
    onChange: props.onChange,
    nodes
  });

  // 加载Flow数据
  if (props.flow_data) {
    easyflow.value.fromJSON(JSON.parse(props.flow_data));
  } else {
    easyflow.value.initFlowData();
  }
  graph.value = easyflow.value.graph;

  addEventListen(window, "resize", ($e) => {
    easyflow.value.resizeEasyFlow({
      width: easyflowRef.value.offsetWidth,
      height: easyflowRef.value.offsetHeight,
    });
  });
});

onBeforeRouteLeave((to, from, next) => {
  easyflow.value.destroyed();
  next();
});

// 组件方法
defineExpose({
  save,
  getData,
  toogleSimulate,
  toogleMonitor,
  getNode,
  getNodeAttr,
  // monitor,
});

provide("flow", {
  visible,
  nodeGroups,
  entity: props.entity,
  easyflow,
  graph,
  flowId: props.flowId,
  instanceId,
  flowdata,
  simulate: props.simulate,
  simulateEvent: props.simulateEvent,
  validate: props.validate,
  toogleMonitor,
  // monitor,
  tooglePannel,
  getData,
  started: props.started,
  isDebug,
  isSimulate: true,
  editEnable,
  onChange: props.onChange,
});
</script>

<style lang="less" scoped>
.easyflow {
  width: 100%;
  height: 100%;

  .easyflow-content {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
  }

  .easyflow-main {
    flex: 1;
    height: 100% !important;
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
  }

  .flow-parent {
    display: flex;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
}

::deep(.x6-graph-svg-decorator) {
  position: relative;

  &:hover {
    content: "点击或拖动左侧的节点到此处";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
