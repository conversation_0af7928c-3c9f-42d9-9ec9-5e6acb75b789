import axios from "axios";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function saveMarketingCenter(data: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/zone`;
  return axios.put(uri, data);
}
export function getAllChannelLimit() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/limit-setting`;
  return axios.get(uri);
}
export function saveAllChannelLimit(data: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/limit-setting`;
  return axios.put(uri, data);
}
export function getOmniChannelLimit() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/limit-setting`;
  return axios.get(uri);
}
export function getMarketingCenter() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/view`;
  return axios.get(uri);
}

export function checkDependencies() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/dependence`;
  return axios.get(uri);
}

export function initEsTemplate() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/init-es-templates`;
  return axios.put(uri);
}

export function initBI() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/init-bi`;
  return axios.put(uri);
}

export function initDwh() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/init-dwh`;
  return axios.put(uri);
}

export function initFactModel() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/init-fact-model`;
  return axios.put(uri);
}

export function initStorage(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/init-storage`;
  return axios.put(uri, info);
}

export function initNodeGroups() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/init-node-groups`;
  return axios.put(uri);
}

export function initSystemNodeConfigs() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/init-system-node-configs`;
  return axios.put(uri);
}


export function checkConfig() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/check-config`;
  return axios.get(uri);
}

export function onlineMarketingCenter() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/online`;
  return axios.put(uri);
}

export function dwhList() {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/dwh/list`;
  return axios.get(uri);
}

export function dwhModelList(collectionId: string) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/dwh/${collectionId}/model/list`;
  return axios.get(uri);
}
