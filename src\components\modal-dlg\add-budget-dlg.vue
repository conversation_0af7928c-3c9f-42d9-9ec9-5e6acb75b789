/**
* 添加预算
*/
<template>
  <a-modal v-model:visible="addItemVisible" @before-ok="handleOk">
    <template v-if="!dataForm.type" #title>{{ dataForm.id ? "编辑预算" : "新增预算" }}</template>
    <template v-else #title>{{ dataForm.type === 'PLAN' ? "追加预算" : "确认预算" }}</template>
    <a-form ref="dataFormRef" :model="dataForm">
      <a-form-item
          v-if="dataForm.status === 'PLANNED'"
        field="name"
        label="预算名称"
        :rules="[{ required: true, message: '不能为空' }]"
        label-col-flex="70px">
        <a-input v-model="dataForm.name" placeholder="请输入预算名称" />
      </a-form-item>
<!--      <a-form-item
        field="planned"
        label="预算金额"
        v-if="!dataForm.type"
        :rules="[{ required: true, message: '不能为空' }]"
        label-col-flex="70px">
        <a-input-number
          v-model="dataForm.planned"
          placeholder="请输入金额"
          :min="0"/>
      </a-form-item>
      <a-form-item
          v-else
          field="amount"
          label="金额"
          :rules="[{ required: true, message: '不能为空' }]"
          label-col-flex="70px">
        <a-input-number
            v-model="dataForm.amount"
            placeholder="请输入确认金额"
            :min="0"
        />
      </a-form-item>-->
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { Modal } from "@arco-design/web-vue";
import { saveInfo, changeBudgetItem } from "@/api/budget";
import { modalCommit } from "@/utils/modal";

const props = defineProps(["budgetId", "bindData"]);
const bindData = props.bindData;
const addItemVisible = ref(false);
const dataForm = ref({});
const dataFormRef = ref({});
const typeList = () => {
  const item = [
    { value: 'PLAN', label: '追加' },
    { value: 'RESERVE', label: '预留' },
    { value: 'CONFIRM', label: '确认' },
    { value: 'LOCK', label: '锁定' },
    { value: 'SPEND', label: '消耗' },
  ].find(x => {
    return x.value === dataForm.value.status
  })
  return item.label
}

const handleOk = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    if(dataForm.value.type) {
      await changeBudgetItem(dataForm.value);
    } else {
      await saveInfo(dataForm.value);
    }

    await bindData();
  });
};

const show = () => {
  addItemVisible.value = true;
};

const create = (group, annual) => {
  dataForm.value = {
    groupId: group,
    annual: annual,
    status: "PLANNED"
  };
  addItemVisible.value = true;
};

const edit = (data, type) => {
  dataFormRef.value.clearValidate()
  let item = JSON.parse(JSON.stringify(data))
  dataForm.value = item
  addItemVisible.value = true;
};

defineExpose({ show, create, edit });
</script>
<style  lang="less" scoped>
</style>
