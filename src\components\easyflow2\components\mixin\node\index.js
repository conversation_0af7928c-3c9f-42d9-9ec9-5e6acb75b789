// import useFlowStore from '../../store/index'
// const FlowStore = useFlowStore()
export default {
    data() {
        return {};
    },
    created() {},
    computed: {},
    methods: {
        // 监听
        listener() {},

        // 获取节点对象
        getNode() {
            return this.node.getNode();
        },
        // 单击节点事件
        handleClick(e) {
            e.stopPropagation();

            // 这边也能走useFlowStore统一处理所有节点的点击事件，但不推荐！
            // 交由下游组件执行各自的点击事件
            // FlowStore.tooglePannel()
            this.node.handleClick();
        },

        // 模拟流程
        simulate() {
            console.log("模拟运行了", this.title);
        },
        toogleMonitor() {
            console.log("toogleMonitor");
        },
    },
};
