/**
 * 过滤图形数据
 * @param data
 * @param type
 */
export function filterOption(data: any, type: any ) {
    let chartOption = {}

    // 漏斗图
    if (type === 'Funnel') {
        let itemData: any = []
        data.dataSet.tables[0]?.headers.forEach((item: any, index: any) => {
            itemData.push({value: data.dataSet.tables[0]?.rows[0]?.values[index], name: item})
        })

        chartOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c}%'
            },
            legend: {
                orient: 'vertical',
                top: '40px',
                left: 'left',
                data: data.dataSet.tables[0]?.headers
            },
            series: [
                {
                    name: data.dataSet.tables[0]?.metricName,
                    type: 'funnel',
                    right: '5%',
                    bottom: '1%',
                    left: '25%',
                    top: '5%',
                    data: itemData
                }
            ]
        }
    }

    // 条形图
    if (type === 'Bar') {
        let legendData: any = []
        let seriesData: any = []
        data.dataSet.tables[0]?.rows.forEach((item: any, index: any) => {
            seriesData.push({
                name: item?.name,
                type: 'bar',
                data: item.values
            })
            legendData.push(item?.name)
        })

        chartOption = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: legendData,
                right: '10'
            },
            calculable: true,
            grid: [{
                right: '5%',
                bottom: '10%',
                left: '10%',
                top: '20%',
            }],
            xAxis: [
                {
                    type: 'category',
                    data: data.dataSet.tables[0]?.headers
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            series: seriesData
        }
    }

    // 饼状图
    if (type === 'Pie') {
        let legendData: any = []
        let seriesData: any = []
        let itemData: any = []
        data.dataSet.tables[0]?.headers.forEach((item: any, index: any) => {
            itemData.push({value: data.dataSet.tables[0]?.rows[0]?.values[index], name: item})
        })
        data.dataSet.tables.forEach((item: any, index: any) => {
            seriesData.push({
                name: item.metricName,
                type: 'pie',
                data: itemData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            })
            legendData.push(item.metricName)
        })

        chartOption = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                right: '10'
            },
            series: seriesData
        }
    }

    // 折线图
    if (type === 'Line') {

        let legendData: any = []
        let seriesData: any = []
        data.dataSet.tables.forEach((item: any, index: any) => {
            seriesData.push({
                name: item.metricName,
                type: 'line',
                data: item.rows[0]?.values
            })
            legendData.push(item.metricName)
        })
        chartOption = {
            xAxis: {
                type: 'category',
                data: data.dataSet.tables[0]?.headers
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: legendData
            },
            yAxis: {
                type: 'value'
            },
            grid: [{
                right: '5%',
                bottom: '10%',
                left: '10%',
                top: '20%',
            }],
            series: seriesData
        }
    }


    return chartOption
}

/**
 * 坐标系排序
 * @param data
 */
export function getSortList (data: any) {
    // 坐标排序
    function SetSortRule(p1: any, p2: any) {
        if (p1.y > p2.y) {
            return true;
        }
        else if (p1.y == p2.y) {
            return (p1.x > p2.x);
        }
        else {
            return false;
        }
    }
    function SetSortPoint(arry: any) {
        let len = arry.length;
        for (let i = 0; i < len - 1; i++) {
            for (let j = 0; j < len - 1 - i; j++) {
                if (SetSortRule(arry[j], arry[j + 1])) {
                    let tmp = arry[j];
                    arry[j] = arry[j + 1];
                    arry[j + 1] = tmp;
                }
            }
        }
        return arry
    }

    return SetSortPoint(data)
}
