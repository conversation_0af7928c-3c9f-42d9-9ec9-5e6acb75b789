<template>
  <a-modal v-model:visible="modelVisible" title="选择时间范围" @before-ok="handleOk" @before-cancel="handleCancel">
    <div>{{title}}</div>
    <a-form ref="dataFormRef" :model="dataForm">
      <a-form-item
        label="时间范围"
        field="rangeValue"
        :rules="[{ required: true, message: '时间范围不能为空' }]"
      >
        <a-range-picker
          show-time
          v-model="dataForm.rangeValue"
          :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DDTHH:mm:ss.000ZZ"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { Modal } from "@arco-design/web-vue";
import { modalCommit } from "@/utils/modal";

let callback;
const modelVisible = ref(false);
const dataForm = ref({});
const dataFormRef = ref({});
const title = ref();

const handleOk = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    callback(dataForm.value.rangeValue);
  });
};
const handleCancel = async () => {
  callback();
}

const show = (_title, _timeRange) => {
  title.value = _title
  if(_timeRange){
    dataForm.value.rangeValue = _timeRange;
  }
  modelVisible.value = true;
  return new Promise(async (resolve, reject) => {
    callback = resolve;
  });
};

defineExpose({ show });
</script>
<style  lang="less" scoped>
</style>
