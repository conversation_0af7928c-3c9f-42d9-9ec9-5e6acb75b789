<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { shortDatetime } from "@/utils/date";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";
import { findBehaviorEventItem } from "@/api/behavior";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (data.behaviorId) {
        if(!data.eventName){
          const behavior = await findBehaviorEventItem(data.behaviorId);
          data.eventName = behavior.name;
        }
        brief.push({ sort: 0, label: "事件", value: data.eventName });
      } else {
        brief.push({ sort: 0, label: "事件" });
      }
      brief.push({
        sort: 1,
        label: "关联客户",
        value: data.mappingCustomer ? "是" : "否",
      });
      brief.push({
        sort: 2,
        label: "开始时间",
        value: shortDatetime(data.eventStartTime),
      });
      brief.push({
        sort: 3,
        label: "结束时间",
        value: shortDatetime(data.eventEndTime),
      });

      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'event_receive' })
    const config = {
      title: item.name || "接收事件",
      summary: item.name || "接收事件节点",
      iconClass: item.icon || "icon-start",
      nodeClass: "easyflow-node-event-receive",
      headerColor: item.themeColor || "#39bcc5",
      headerBgColor: item.themeColor || "#39bcc5",
      background: item.background || "#f2fcfa",
      getBrief,
    };

    provide("node", config);
    return config;
  },
  data() {
    return {
      monitor: false,
    };
  },
};
</script>
