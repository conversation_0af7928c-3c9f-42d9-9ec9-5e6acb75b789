<template>
  <a-col class="banner">
    <a-col :span="8">
      <a-typography-title :heading="5" style="margin-top: 0">
        {{ userInfo.username }}, {{ $t("workplace.welcome") }}
      </a-typography-title>
    </a-col>
    <!-- <div class="right-col">
      <a-select
        v-model="budgetAnnual"
        size="small"
        @change="bindData()"
        style="width: 200px; margin-left: 10px"
        placeholder="请选择预算年度"
      >
        <a-option
          v-for="item in budgetAnnuals"
          :key="item.annual"
          :label="item.name"
          :value="item.annual"
        />
      </a-select>
    </div> -->
    <a-divider class="panel-border" />
  </a-col>
</template>

<script>
import { computed, ref, onMounted } from "vue";
import { useUserStore } from "@/store";
import { findAnnualBudgetList } from "@/api/budget";

export default {
  props: {
    value: {
      type: String,
      default: ""
    }
  },
  emits: ["update:budget"],
  setup(props, { emit }) {
    const budgetAnnuals = ref([]);
    const budgetData = ref({});
    const budgetAnnual = ref(null);
    onMounted(async () => {
      budgetAnnuals.value = await findAnnualBudgetList({
        fields: "name,annual"
      });
      budgetAnnual.value = budgetAnnuals.value[0]?.annual;
      emit("update:vm", budgetAnnual.value);
    });
    const bindData = () => {
      emit("update:vm", budgetAnnual.value);
    };

    const userStore = useUserStore();
    const userInfo = computed(() => {
      return userStore.userAuthInfo;
    });

    return {
      userInfo,
      budgetData,
      budgetAnnual,
      bindData,
      budgetAnnuals
    };
  }
};
</script>

<style scoped lang="less">
.banner {
  width: 100%;
  padding: 20px 20px 0 20px;
  background-color: var(--color-bg-2);
  border-radius: 4px 4px 0 0;
  position: relative;

  .right-col {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
  }
}

:deep(.arco-icon-home) {
  margin-right: 6px;
}
</style>
