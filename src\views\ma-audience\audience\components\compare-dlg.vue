/** 比较节点人群差异 */
<template>
  <a-modal v-model:visible="visible" @before-ok="handleOk">
    <template v-if="!dataForm.type" #title>{{t('audience.popup.compare_audience_title')}}</template>

    <a-form ref="dataFormRef" layout="vertical" :model="dataForm">
      <div class="compare">
        <div class="col col-left">
          <p class="title">{{t('audience.popup.leftAudience')}}</p>
          <a-form-item field="leftType" :rules="[{ required: true, message: t('audience.reminder.selectAudienceType') }]"
            :validate-trigger="['change', 'input']" :label="t('audience.popup.type')">
            <a-select v-model="dataForm.leftType" :placeholder="t('audience.reminder.selectAudienceType')" @change="leftTypeChange">
              <a-option v-for="item in type" :key="item.id" :value="item.id" :label="item.name"></a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="leftMasterId" :rules="[{ required: true, message: t('audience.reminder.selectAudience') }]"
            :validate-trigger="['change', 'input']" :label="t('audience.popup.audience')">
            <a-select v-model="dataForm.leftMasterId" :placeholder="t('audience.reminder.selectAudience')" @change="leftMasterChange">
              <a-option v-for="item in leftMasterDataList" :key="item.id" :value="item.id" :label="item.name"></a-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="dataForm.leftType == 'SNAPSHOT'" field="leftSubId"
            :rules="[{ required: true, message: t('audience.reminder.selectAudienceSnapshot') }]" :validate-trigger="['change', 'input']" :label="t('audience.popup.audienceSnapshot')">
            <a-select v-model="dataForm.leftSubId" :placeholder="t('audience.reminder.selectAudienceSnapshot')">
              <a-option v-for="item in leftSubDataList" :key="item.id" :value="item.id" :label="item.name"></a-option>
            </a-select>
          </a-form-item>
        </div>
        <div class="col col-left">
          <p class="title">{{t('audience.popup.rightAudience')}}</p>
          <a-form-item field="rightType" :rules="[{ required: true, message: t('audience.reminder.selectAudienceType') }]"
            :validate-trigger="['change', 'input']" :label="t('audience.popup.type')">
            <a-select v-model="dataForm.rightType" :placeholder="t('audience.reminder.selectAudienceType')" @change="rightTypeChange">
              <a-option v-for="item in type" :key="item.id" :value="item.id" :label="item.name"></a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="rightMasterId" :rules="[{ required: true, message: t('audience.reminder.selectAudience') }]"
            :validate-trigger="['change', 'input']" :label="t('audience.popup.audience')">
            <a-select v-model="dataForm.rightMasterId" :placeholder="t('audience.reminder.selectAudience')" @change="rightMasterChange">
              <a-option v-for="item in rightMasterDataList" :key="item.id" :value="item.id" :label="item.name"></a-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="dataForm.rightType == 'SNAPSHOT'" field="rightSubId"
            :rules="[{ required: true, message: t('audience.reminder.selectAudienceSnapshot') }]" :validate-trigger="['change', 'input']" :label="t('audience.popup.audienceSnapshot')">
            <a-select v-model="dataForm.rightSubId" :placeholder="t('audience.reminder.selectAudienceSnapshot')">
              <a-option v-for="item in rightSubDataList" :key="item.id" :value="item.id" :label="item.name"></a-option>
            </a-select>
          </a-form-item>
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref , getCurrentInstance} from "vue";
import { Message } from "@arco-design/web-vue";
import {
  findAudienceList,
  getCrowdSnapshot,
  createCompareJob,
} from "@/api/audience";
import moment from "moment";

const {
      proxy: { t }
    } = getCurrentInstance()

const props = defineProps(["budgetId", "bindData"]);
const { bindData } = props;
const visible = ref(false);
const dataForm = ref({});
const dataFormRef = ref({});
const leftMasterDataList = ref([]);
const leftSubDataList = ref([]);
const rightMasterDataList = ref([]);
const rightSubDataList = ref([]);
const type = [
  { id: "AUDIENCE", name: t('audience.popup.audience') },
  { id: "SNAPSHOT", name: t('audience.popup.snapshot') },
];

const handleOk = async () => {
  const res = await dataFormRef.value.validate();
  if (res) {
    return false;
  }

  await createCompareJob(dataForm.value);
  // Message.success("创建人群比较任务成功,可在任务管理中查看");
  Message.success(t('audience.reminder.createAudienceSuccess'));

  visible.value = false;
};

const show = () => {
  visible.value = true;
  dataForm.value = {};
};

const create = (group, annual) => {
  visible.value = true;
};
const leftTypeChange = async () => {
  leftMasterDataList.value = await findAudienceList({ fields: "name" });
  await leftMasterChange();
};

const leftMasterChange = async () => {
  if (dataForm.value.leftType === "SNAPSHOT" && dataForm.value.leftMasterId) {
    const data = await getCrowdSnapshot({
      expression: `audienceId eq ${  dataForm.value.leftMasterId}`,
    });
    data.content.forEach((item) => {
      leftSubDataList.value.push({
        id: item.id,
        name: moment(item.createDate).format("YYYY-MM-DD HH:mm:ss"),
      });
    });
  }
};
const rightTypeChange = async () => {
  rightMasterDataList.value = await findAudienceList({ fields: "name" });
  await rightMasterChange();
};
const rightMasterChange = async () => {
  if (dataForm.value.rightType === "SNAPSHOT" && dataForm.value.rightMasterId) {
    const data = await getCrowdSnapshot({
      expression: `audienceId eq ${  dataForm.value.rightMasterId}`,
    });
    data.content.forEach((item) => {
      rightSubDataList.value.push({
        id: item.id,
        name: moment(item.createDate).format("YYYY-MM-DD HH:mm:ss"),
      });
    });
  }
};
const edit = (data, type) => {
  dataFormRef.value.clearValidate();
  const item = JSON.parse(JSON.stringify(data));
  dataForm.value = item;
  visible.value = true;
};

defineExpose({ show, create, edit });
</script>

<style lang="less" scoped>
.compare {
  display: flex;
  width: 100%;

  .title {
    font-weight: bold;
    font-size: 20px;
    text-align: center;
  }

  .col {
    width: 45%;
    background-color: #fafafa;
    margin: 10px;
    padding: 10px;
  }
}
</style>
