<template>
  <a-list-item>
    <a-list-item-meta :title="props.title" />
    <template #actions>
      <a-button v-if="itemStatus == 'FAILED'" type="primary" @click="onClick">配置</a-button>
      <div class="ico">
        <icon-check-circle v-if="itemStatus == 'SUCCESS'" :style="{ fontSize: '32px', color: 'lightgreen' }" />
        <icon-refresh v-if="itemStatus == 'RUNNING'" :style="{ fontSize: '32px' }" spin />
        <icon-close-circle v-if="itemStatus == 'ERROR'" :style="{ fontSize: '32px', color: 'red' }" />
      </div>
    </template>
  </a-list-item>
</template>

<script setup>
import { ref, computed } from "vue";

const props = defineProps(["title", "status", "onClick"]);

const itemStatus = ref(props.status);

const onClick = async () => {
  itemStatus.value = "RUNNING";
  if (await props.onClick()) {
    itemStatus.value = "SUCCESS";
  } else {
    itemStatus.value = "FAILED";
  }
}

</script>
<style lang="less" scoped>
.ico {

}
</style>
