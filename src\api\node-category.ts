/**
 * 触点分类接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findNodeCategoryPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-category`, {
    params: {
      ...params,
      ...query
    }
  });
}

export function findNodeCategoryList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-category/list`, {
    params
  });
}

export function saveNodeCategory(isEdit: any, info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/flow-category`;
  return !isEdit ? axios.post(uri, info) : axios.put(uri, info);
}

// export function deleteNode(id: string) {
//   return axios.delete(
//     `/api/ma-manage/${tenantId}/${buCode}/flow-category/${id}`
//   );
// }

// export function getCategory(id: string) {
//   return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-category/${id}`);
// }
