import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import FilterNode from "./node.vue";
import FilterPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "filter",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<FilterNode />`,
      components: {
        FilterNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("FilterNode", nodeData.node, true);
};

const Filter = {
  type: "filter",
  name: "过滤",
  shape: "FilterNode",
  iconClass: "icon-ma-filter",
  registerNode,
  pannel: FilterPannel,
  help: Help,
  skippable: false,
};

export default Filter;
