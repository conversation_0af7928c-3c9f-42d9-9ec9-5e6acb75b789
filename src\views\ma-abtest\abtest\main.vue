<template>
  <module main>
    <template #filter></template>
    <template #search></template>
    <template #action><span></span></template>
    <template #context><span></span></template>
    <template #main>
      <a-table ref="table" :bordered="false" :data="dataSource">
        <template #columns>
          <a-table-column :title="t('ab.name')" data-index="name" :width="200" />
          <a-table-column :title="t('ab.relatedCampaign')" data-index="campaignId" :width="200">
            <template #cell="{ record }">
              {{ filters(campaignList, record.flowId, 'name', 'id') }}
            </template>
          </a-table-column>
          <a-table-column :title="t('ab.startDate')" data-index="startTime" :width="170">
            <template #cell="{ record }">
              {{ $moment(record.startTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </a-table-column>
          <a-table-column :title="t('ab.endDate')" data-index="endTime" :width="170">
            <template #cell="{ record }">
              {{ $moment(record.endTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </a-table-column>
          <a-table-column :title="t('ab.action')" align="center" :width="300">
            <template #cell="{ record }">
              <a-button v-permission="['ma_menu.abtest.view']" type="text" size="small" @click="detail(record)">{{t('global.button.view')}}</a-button>
              <!-- <a-button type="text" size="small" @click="analyseDetail(record)">分析</a-button> -->
              <a-button v-permission="['ma_menu.abtest.delete']" type="text" size="small" @click="deleteData(record)">{{t('global.button.delete')}}</a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </template>
  </module>
</template>

<script>
import { ref, provide, computed, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import { Modal } from "@arco-design/web-vue";
import { findAbtestPage, deleteAbtest } from "@/api/analysis";
import { findCampaignList } from "@/api/campaign.ts";
import { filters } from "@/utils/filter";

export default {
  components: {},

  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()

    // 路由API
    const router = useRouter();

    const module = ref({
      entityIdField: "id",
      // entityName: "A/B测试管理",
      entityName: t('ab.abTestManagement'),
      // breadcrumb: [{ name: "A/B测试管理" }],
      breadcrumb: [{ name: t('ab.abTestManagement') }],
      viewPath: "/ab/abtest/view",
      analysePath: "/ab/abtest/analyse",
    });
    // 分页设置
    const pagination = ref({
      page: 0,
      size: 20,
      total: 0,
      showPageSize: true,
    });
    const filter = ref([]);

    const group = ref([]);

    const show = ref(true);

    // 数据设置
    const entity = ref({});

    const campaignList = ref([])

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    const detail = (item) => {
      const query = { id: item.id };
      router.push({ path: module.value.viewPath, query });
    };

    // 分析
    const analyseDetail = (item) => {
      const query = { id: item.id };
      router.push({ path: module.value.analysePath, query });
    };

    const getListData = () => {
      const campaignIdList = []
      for (let i = 0; i < dataSource.value.length; i++) {
        campaignIdList.push(dataSource.value[i].flowId)
      }
      findCampaignList({
        expression: campaignIdList.join(",") ? `id in ${campaignIdList.join(",")}` : "",
        fields: "name",
      }).then(res => {
        campaignList.value = res
      })
    }

    const deleteData = async (record) => {
      Modal.confirm({
        // title: "删除A/B测试",
        title: t('ab.deleteABTitle'),
        // content: "删除之后数据不可恢复，请确认是否删除?",
        content: t('ab.reminder.delete'),
        onOk: async () => {
          await deleteAbtest(record.id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page--;
          }
          await bindData();
        },
      });
    };

    // 查询API
    const bindData = async (expression) => {
      entity.value = await findAbtestPage(
        {
          ...pagination.value,
          page: pagination.value.page,
        },
        {
          expression,
          fields: "name,flowId,startTime,endTime,createdTime,summary",
        }
      );
      pagination.value.total = entity.value.totalElements;
      getListData()
    };

    const setup = {
      t,
      show,
      group,
      filter,
      module,
      dataSource,
      bindData,
      detail,
      campaignList,
      analyseDetail,
      deleteData,
      filters
    };
    provide("main", setup);
    return setup;
  },

  data() {
    return {};
  },
};
</script>
