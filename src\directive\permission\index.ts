import { DirectiveBinding } from "vue";
import { useUserStore } from "@/store";

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const { value } = binding;
  const userStore = useUserStore();
  const { userAuthInfo } = userStore;

  if (value) {
    if (value.length > 0) {
      // const permissionList = userAuthInfo.authorities;
      const permissionList = userAuthInfo.menus;

      const hasPermission = value.some(item => permissionList.includes(item))
      if (!hasPermission && el.parentNode) {
        el.parentNode.removeChild(el);
      }
    }
  // } else {
  //   throw new Error(`need roles! Like v-permission="xxx-xxx-xxx"`);
  }
}

export default {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding);
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding);
  },
};
