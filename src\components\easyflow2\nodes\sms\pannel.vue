<template>
  <div class="easyflow-pannel-sms">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="短信沟通模板" class="form-item-select">
        <a-select v-model="entity.capabilityId" class="easyflow-select" placeholder="请选择短信沟通模板" :loading="loading"
          allow-search @change="showContent(entity.capabilityId)">
          <a-option v-for="item of communicates" :key="item.id" :label="item.label" :value="item.id">
            [{{ item.name }}] <span class="light-text"> {{ item.id }} </span>
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="模板示例">
        <span class="sms-content">
          {{ content }}
        </span>
      </a-form-item>
      <!-- <a-form-item
        label="动态绑定数据"
        help="当前面的流程配置了Flow节点并接受返回值，则这里可以使用返回值中的参数"
      >
        <a-select
          v-model="entity.bindName"
          :disabled="bindNames.length == 0"
          placeholder="请选择动态绑定数据"
        >
          <a-option v-for="item of bindNames" :key="item" :value="item">{{
            item
          }}</a-option>
        </a-select>
      </a-form-item> -->
      <!-- <a-form-item label="预算设置" :content-flex="false">
        <a-checkbox v-model="entity.budgetSetting.enabled" class="budget" value="1" @change="toogleBudeget">参与计算费用 ( 单价:
          <span> {{ unitBudget }}</span>元 )</a-checkbox>
        <div v-if="entity.budgetSetting.enabled">
          <div class="flex-line">
            <a-select v-model="entity.budgetSetting.budgetItemId" class="easyflow-select" placeholder="请选择预算条目"
              :loading="loading" :filter-option="false" @search="handleSearchBudget">
              <a-option v-for="item of budgets" :key="item.id" :value="item.id">{{ item.name }}</a-option>
            </a-select>
            <a-button class="btn" type="outline" @click="addBudgetItemRef.show()">新增</a-button>
          </div>
        </div>
        <template #extra>
          <div class="tip">预算不足时停止发送</div>
        </template>
</a-form-item> -->
      <a-form-item key="reachField" :label="t('campaign.pannel.reachField')">
        <a-tree-select v-model="entity.reachField" :allow-search="true" :data="flowModel" allow-clear
          :field-names="treeFieldStruct" placeholder="请选择字段">
        </a-tree-select>
      </a-form-item>
      <a-form-item label="沟通限制" :content-flex="false">
        <a-checkbox v-model="entity.frequencyLimit" class="budget">限制沟通次数</a-checkbox>
        <template #extra>
          <div class="tip">
            超出客户的沟通频次限制时，沟通消息不会发送，并且该客户会停在当前节点不再继续执行
          </div>
        </template>
      </a-form-item>
      <a-form-item :label="t('campaign.pannel.deduplication')">
        <a-space>
          <a-switch v-model="entity.unique" type="round" />
          <a-tree-select v-if="entity.unique" v-model="entity.uniqueFields" :multiple="true" :allow-search="true" :data="flowModel"
            allow-clear :field-names="treeFieldStruct" placeholder="请选择去重字段" />
        </a-space>
      </a-form-item>
      <a-form-item label="勿扰策略">
        <SilenceSelect v-model:silenceRuleId="entity.silenceRuleId" />
      </a-form-item>
    </a-form>

    <AddBudgetItemDlg ref="addBudgetItemRef" :budget-id="budgetId" :bind-data="handleSearchBudget" />
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { treeFieldStruct } from "@/constant/common"
import { getFlowModelsByIds } from "@/api/campaign";
import { findCommunicateList, findCommunicateItem, findChannelList } from "@/api/communicate";
import { findBudgetItemList } from "@/api/budget";
import { getFieldByPath, formatFields } from "@/utils/field"
import AddBudgetItemDlg from "@/components/modal-dlg/add-budget-item-dlg.vue";
import SilenceSelect from "@/components/ma/silence-select/index.vue"

const { node, easyflow } = defineProps(["node", "easyflow"]);
const flowIjt = inject("flow");
const budgetId = ref(flowIjt.flowId);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const entity = ref({
  capabilityId: null,
  budgetSetting: {
    enabled: false,
    budgetId: budgetId.value
  },
  frequencyLimit: true
});
const content = ref("");
const communicates = ref([]);
const loading = ref(false);
const budgets = ref([]);
const bindNames = ref([]);
const unitBudget = ref(0);
const addBudgetItemRef = ref(null);
const flowModel = ref(null);
const channels = ref([]);

const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = formatFields(modelFields.fields, "");
};

const handleSearchCommunicate = async (name) => {
  loading.value = true;
  const params = { fields: "name,channelId,budgetSetting", expression: "" };
  params.expression = "type eq sms AND status eq ENABLED";
  if (name) {
    params.expression += " AND name like value";
  }
  communicates.value = await findCommunicateList(params);
  communicates.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
  loading.value = false;
};

const setDefaultReachField = (capabilityId) => {
  if (!entity.value.reachField && entity.value.capabilityId) {
    const comm = communicates.value.find((it) => it.id === capabilityId)
    const channel = channels.value.find((it) => it.id === comm.channelId)
    entity.value.reachField = `Customer.${channel.setting.customerField}`;
  }
}

const showContent = async (capabilityId) => {
  if (capabilityId) {
    const communicate = await findCommunicateItem(capabilityId);
    content.value = communicate.setting.template;
    unitBudget.value = communicate.budget;
    entity.value.budgetSetting.budgetValue = communicate.budget;
    setDefaultReachField(capabilityId);
  }
};
const handleSearchBudget = async (name) => {
  loading.value = true;
  const params = {
    fields: "name",
    expression: `budgetId eq ${budgetId.value}`
  };
  if (name) {
    params.expression += ` AND name like ${name}`;
  }
  budgets.value = await findBudgetItemList(params);
  loading.value = false;
};
const getBindNames = () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  bindNames.value = easyflow.getBindNames(nodeChain);
};

const toogleBudeget = () => {
  if (entity.value.budgetSetting.enabled) {
    entity.value.budgetSetting.budgetId = budgetId.value;
  }
};

const save = () => {
  return entity.value;
};

defineExpose({
  save
});
onMounted(async () => {
  Object.assign(entity.value, node.data);
  channels.value = await findChannelList({
    fields: "name,summary,setting",
    expression: "type eq sms"
  });
  handleSearchCommunicate();
  await handelFlowModel();
  showContent(entity.value.capabilityId);
});
</script>

<style lang="less" scoped>
.easyflow-pannel-sms {
  .sms-content {
    color: #b3b3b3;
  }

  .budget {
    color: rgb(var(--primary-6));

    .tip {
      color: #b3b3b3;
    }
  }
}
</style>
