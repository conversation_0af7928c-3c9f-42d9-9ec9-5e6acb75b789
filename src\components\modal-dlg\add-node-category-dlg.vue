<template>
  <template v-if="showGroupVisible">
    <a-modal v-model:visible="showGroupVisible" :title="isEdit ? t('systemSetting.touchpointCategoryManagement.editCategory') : t('systemSetting.touchpointCategoryManagement.addCategory')" @cancel="showGroupVisible = false"
      @before-ok="addGroup">
      <a-form ref="dataFormRef" :model="dataForm">
        <a-form-item field="id" :label="t('systemSetting.touchpointCategoryManagement.id')" :rules="[{ required: true, message: t('systemSetting.touchpointCategoryManagement.enterId') }]" label-col-flex="70px"
          :disabled="isEdit">
          <a-input v-model="dataForm.id" :placeholder="t('systemSetting.touchpointCategoryManagement.id')" />
        </a-form-item>
        <a-form-item field="name" :label="t('systemSetting.touchpointCategoryManagement.name')" :rules="[{ required: true, message: t('systemSetting.touchpointCategoryManagement.enterName') }]" label-col-flex="70px">
          <a-input v-model="dataForm.name" :placeholder="t('systemSetting.touchpointCategoryManagement.name')" />
        </a-form-item>
        <a-form-item field="summary" :label="t('systemSetting.touchpointCategoryManagement.summary')" label-col-flex="70px">
          <a-textarea v-model="dataForm.summary" type="textarea" :placeholder="t('systemSetting.touchpointCategoryManagement.enterSummary')" />
        </a-form-item>
      </a-form>
    </a-modal>
  </template>
</template>

<script setup>
import { ref,getCurrentInstance } from "vue";
import { saveNodeCategory } from "@/api/node-category";
import { modalCommit } from "@/utils/modal";

const {
  proxy: { t }
} = getCurrentInstance();
const showGroupVisible = ref(false);
const dataForm = ref({});
const dataFormRef = ref({});
const emit = defineEmits(["change"]);
const isEdit = ref(null);

const addGroup = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    await saveNodeCategory(isEdit.value, dataForm.value);
    emit("change");
  });
};

const show = () => {
  showGroupVisible.value = true;
};

const createEdit = (data) => {
  dataForm.value = data ? JSON.parse(JSON.stringify(data)) : {};
  isEdit.value = !!dataForm.value.id;
  showGroupVisible.value = true;
};

defineExpose({ show, createEdit });
</script>

<style lang="less" scoped></style>
