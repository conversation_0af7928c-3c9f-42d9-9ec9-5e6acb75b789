/**
*by:<EMAIL> on 2022/7/14 0014
*/
<template>
  <div class="CronSelect">
    <a-row :gutter="24">
      <a-col :span="18">
        <a-input v-model="showInput" style="width: 100%" placeholder="cron时间" :disabled="disabled" @change="changeCron"/>
      </a-col>
      <a-col :span="6">
        <a-popconfirm ok-text="确定" position="tr" @ok="changeOk" :ok-button-props="{disabled: disabled}">
          <template #icon>&nbsp;</template>
          <template #content>
            <div class="select-model">
              <a-select v-model="setData.curType" style="margin-right: 5px; width: 78px" @change="changeInt">
                <a-option :value="3">每天</a-option>
                <a-option :value="4">每月</a-option>
                <a-option :value="5">每周</a-option>
              </a-select>

              <a-select v-if="setData.curType === 4" v-model="setData.day" :max-tag-count="1"
                style="margin-right: 5px; width: 180px" placeholder="日期" allow-clear multiple @change="changeDay">
                <a-option value="L">最后一天</a-option>
                <a-option v-for="item in 31" :key="item" :value="item" :disabled="setData.day.indexOf('L') !== -1">{{ item }}</a-option>
              </a-select>

              <a-select v-if="setData.curType === 5" v-model="setData.weeks" :max-tag-count="1"
                style="margin-right: 5px; width: 180px" placeholder="星期" allow-clear multiple @change="changeWeeks">
                <a-option value="1" :disabled="setData.weeks.indexOf('?') !== -1">周日</a-option>
                <a-option value="2" :disabled="setData.weeks.indexOf('?') !== -1">周一</a-option>
                <a-option value="3" :disabled="setData.weeks.indexOf('?') !== -1">周二</a-option>
                <a-option value="4" :disabled="setData.weeks.indexOf('?') !== -1">周三</a-option>
                <a-option value="5" :disabled="setData.weeks.indexOf('?') !== -1">周四</a-option>
                <a-option value="6" :disabled="setData.weeks.indexOf('?') !== -1">周五</a-option>
                <a-option value="7" :disabled="setData.weeks.indexOf('?') !== -1">周六</a-option>
              </a-select>
              <a-time-picker v-model="setData.timeData" style="width: 100px" placeholder="时分秒" format="HH:mm:ss" />
            </div>
          </template>
          <a-button>选择</a-button>
        </a-popconfirm>
      </a-col>
    </a-row>
  </div>
</template>

<script>
/**
 *  秒 分  时 日 月 周
 *  *
 */
import { defineComponent, ref, computed } from 'vue';

export default defineComponent({
  name: 'CronSelect',
  props: {
    disabled: Boolean,
    cron: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    const dataList = computed(() => {
      return props.cron ? props.cron.split(' ') : ['*', '*', '*', '*', '*', '?']
    })
    const showInput = ref(props.cron)

    const changeInt = (val) => {
      switch (val) {
        case 3:
          dataList.value[3] = '*'
          dataList.value[4] = '*'
          dataList.value[5] = '*'
          break
        case 4:
          dataList.value[4] = '*'
          dataList.value[5] = '*'
          break
        case 5:
          dataList.value[5] = '?'
          break
      }
      setData.value.day = []
      setData.value.weeks = []
      setData.value.month = []
      setData.value.timeData = ''
    }

    const setData = ref({
      curType: dataList.value[4] !== '*' ? 4 : dataList.value[5] === '?' ? 3 : 5,
      day: dataList.value[3] === '*' ? [] : dataList.value[3]?.split(','),
      month: dataList.value[4] === '*' ? [] : dataList.value[4]?.split(','),
      weeks: dataList.value[5] === '?' ? [] : dataList.value[5]?.split(','),
      timeData: dataList.value[0] === '*' ? '' : `${dataList.value[2]}:${dataList.value[1]}:${dataList.value[0]}`
    })



    // 选择日期
    const changeDay = (val) => {
      if (val.indexOf('L') !== -1) {
        setData.value.day = ['L']
      }
    }
    // 选择日期
    const changeWeeks = (val) => {
      if (val.indexOf('?') !== -1) {
        setData.value.weeks = ['?']
      }
    }

    const changeCron = () => {
      emit('update:cron', showInput.value)
    }

    // 确定选择
    const changeOk = () => {
      const time = setData.value.timeData ? setData.value.timeData.split(':') : ['*', '*', '*']
      dataList.value[0] = time[2] === "*" ? time[2] : parseInt(time[2], 10).toString()
      dataList.value[1] = time[1] === "*" ? time[1] : parseInt(time[1], 10).toString()
      dataList.value[2] = time[0] === "*" ? time[0] : parseInt(time[0], 10).toString()

      dataList.value[3] = setData.value.day.length > 0 ? setData.value.day.toString() : '*'
      dataList.value[4] = setData.value.month.length > 0 ? setData.value.month.toString() : '*'
      dataList.value[5] = setData.value.weeks.length > 0 ? setData.value.weeks.toString() : '?'

      if (dataList.value[3] === '*' && dataList.value[5] !== '?') {
        dataList.value[3] = '?'
      }

      showInput.value = `${dataList.value[0]} ${dataList.value[1]} ${dataList.value[2]} ${dataList.value[3]} ${dataList.value[4]} ${dataList.value[5]}`

      emit('update:cron', showInput.value)
    }

    return {
      setData,
      showInput,
      changeInt,
      changeOk,
      changeCron,
      changeDay,
      changeWeeks
    }
  }
});
</script>

<style lang="less" scoped>
.CronSelect {
  margin: 5px 0;
  width: 100%;
}

.select-model {
  display: flex;
  align-items: center;
  min-width: 350px;

  .item-select {
    margin-right: 5px;
  }
}

:deep(.arco-popconfirm-icon) {
  display: none;
}
</style>
