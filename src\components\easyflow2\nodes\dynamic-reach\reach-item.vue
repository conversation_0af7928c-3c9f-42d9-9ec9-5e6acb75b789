<template>
    <div class="reach-list">
      <div class="reach-item">
        <a-form-item key="reachType" label="触达类型"
        :rules="[{ match: /one/, message: 'must select one' }]">
        <a-select v-model="entity.reachType" placeholder="触达类型" @change="onChangeReachType()">
          <a-option v-for="item of contentList" :key="item.id" :value="item.id">
            [{{ item.name }}] <span class="light-text"> {{ item.id }} </span>
          </a-option>
        </a-select>
      </a-form-item>


      <a-form-item key="flowContentId" :label="t('campaign.pannel.reachContent')"
        :rules="[{ match: /one/, message: 'must select one' }]">
        <a-select v-model="entity.flowContentId" placeholder="触达内容" allow-search :loading="loading"
          :show-extra-options="false" allow-clear @change="onChangeContent(entity.flowContentId)">
          <a-option v-for="item of contentList" :key="item.id" :value="item.id">
            [{{ item.name }}] <span class="light-text"> {{ item.id }} </span>
          </a-option>
        </a-select>
      </a-form-item>

      </div>
    </div>
</template>

<script setup>
import { ref, onMounted, inject, watch, getCurrentInstance } from "vue";

const { node, easyflow } = defineProps(["node", "easyflow"]);

const entity = ref({
});


</script>
