<!-- eslint-disable no-restricted-syntax -->
<template>
  <a-layout class="layout">
    <div v-if="navbar" class="layout-navbar">
      <NavBar />
    </div>
    <a-layout class="layout-content">
      <a-layout-content class="initialize-data">
        <div class="load-alert">
          <a-alert type="error"
            >当前用户没有营销中心权限，请联系技术人员处理。</a-alert
          >
        </div>
      </a-layout-content>
      <Footer v-if="footer" />
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useUserStore, useAppStore, useBussinessUnitStore } from "@/store";
import NavBar from "@/components/navbar/index.vue";
import Footer from "@/components/footer/index.vue";
import axios from "axios";

const appStore = useAppStore();
const userStore = useUserStore();
const { userAuthInfo } = userStore;
const useBUStore = useBussinessUnitStore();
const router = useRouter();
const firstHasPermissioPath = ref("");
const firstRoute = ref();
const navbar = computed(() => appStore.navbar);
const footer = computed(() => appStore.footer);

const buStatus = ref(useBUStore.marketingCenter?.status);

const buCode = useBUStore.currentBussinessUnit?.code;
const tenantId = useBUStore.currentBussinessUnit?.tenantId;

if (buStatus.value == null) {
  buStatus.value = "NONE";
}

const filter = (val) => {
  if (val) {
    const permissionList = userAuthInfo.menus;
    return !!val.find((it) => permissionList.includes(it));
  }
};

if (useBUStore.marketingCenter.status === "ONLINE") {
  axios
    .get(`/api/ma-manage/${tenantId}/${buCode}/common/menu`)
    .then((menus) => {
      const firstMenu = menus[0];
      // console.log("%c [ menus ]-50", "font-size:13px; background:pink; color:#bf2c9f;", menus);
      const temp = [];
      const parentMenuList = router
        .getRoutes()
        .filter((route) => !!route.meta.parentMenu)
        .sort((a, b) => a.meta.order - b.meta.order);

      parentMenuList.forEach((menu) => {
        menu.children.forEach((child) => {
          child.meta.fullPath = `${menu.path}/${child.path}`;
          temp.push(child);
        });
      });

      parentMenuList.forEach((item) => {
        const res = item.children.find((menu) => {
          return (
            menu.meta.type === "menu" &&
            (JSON.stringify(menu.meta.roles) === '["*"]' ||
              filter(menu.meta.roles))
          );
        });

        if (
          (res?.path === "setting" || res?.path === "workplace") &&
          !firstRoute.value
        ) {
          firstRoute.value = res;
          return true;
        }
      });

      const isDynamic =
        firstRoute.value?.meta?.fullPath?.split("/")[
          firstRoute.value.meta.fullPath.split("/").length - 1
        ] === ":id";
      const path = isDynamic
        ? firstRoute.value.meta.fullPath.replace(":id", firstMenu.id)
        : firstRoute.value?.meta?.fullPath;

      if (isDynamic) {
        localStorage.setItem(
          "app-menu-item",
          JSON.stringify({
            path,
            name: `dynamic${firstMenu.id}`,
            meta: {
              id: firstMenu.id,
              isPath: true,
              locale: firstMenu.name,
              title: firstMenu.name,
              roles: firstRoute.value.meta.roles,
              requiresAuth: true,
            },
          })
        );
      }

      firstHasPermissioPath.value = path;

      router.push({
        path: firstHasPermissioPath.value,
      });
    });
} else if (useBUStore.marketingCenter.status === "ERROR") {
  console.log("ERROR");
} else {
  router.push({
    path: "/init",
  });
}
</script>

<style lang="less" scoped>
.load-alert {
  width: 500px;
  margin: 100px auto;
  text-align: center;
}
</style>
