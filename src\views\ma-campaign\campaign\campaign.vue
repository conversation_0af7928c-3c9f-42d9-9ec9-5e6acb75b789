<template>
  <module ref="mainRef" main>
    <!-- <template #top> 1 </template> -->
    <template #action>
      <a-button v-permission="['ma_menu.campaign.create']" type="primary" @click="openModel">
        <template #icon><icon-plus /></template> {{ $t('campaign.button.create') }}
      </a-button>
    </template>
    <template #main>
      <tasklist :sorter-change="sorterChange" :campaign-list="campaignList" :handle-modify-task="modifyTask" />
      <!-- 选择活动模板 -->
      <AddCamapignDlg ref="templateListRef" @onChangeItem="routeToEdit" />
    </template>
    <template #top>
      <group-info :group-info="groupItemInfo" />
      <task-edit-drawer ref="taskDrawerRef" :group-id="queryValue" :save-task="handleSaveTask" :editable="true" />
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Notification } from "@arco-design/web-vue";
import {
  deleteCampaign,
  findCampaignPage,
  findCampaignList,
  createCampaign,
  createCampaignByTemplate
} from "@/api/campaign";
import { getGroup } from "@/api/group";
import { findCategoryList } from "@/api/category";
import { campaignType, campaignStatus } from "@/constant/campaign";

import { addExp } from "@/utils/common";
import tasklist from "./component/tasklist.vue";
import AddCamapignDlg from "./component/add-campaign-dlg.vue";
import taskEditDrawer from "./component/task-edit-drawer.vue";
import groupInfo from "./component/group-info-view.vue";

export default {
  components: {
    tasklist,
    taskEditDrawer,
    AddCamapignDlg,
    groupInfo
  },

  setup() {
    // 路由API
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const queryValue = route.query.id || route.query.groupId;

    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true
    });
    const campaignCategories = ref([]);
    const module = ref({
      entityIdField: "id",
      // entityName: "营销活动",
      entityName: t('campaign.title'),
      breadcrumb: [
        {
          // name: "营销活动",
          name: t('campaign.title'),
          path: "/campaign/group"
        },
        {
          // name: "活动画布",
          name: t('campaign.canvas.title'),
          path: `/campaign/campaign?id=${queryValue}`
        }
      ],
      mainPath: "/campaign/group",
      createTaskPath: "/campaign/Task",
      editPath: "/campaign/campaign/edit",
      budgetPath: "/campaign/campaign/budget",
      reportPath: "/campaign/report",
      viewPath: "/campaign/campaign/view-flow",
      createPath: "/campaign/campaign/edit",
      defaultView: "table",
      showCard: false,
      showBtn: true
    });
    const groupListOpts = ref([]);
    const groupItemInfo = ref([]);
    const filter = ref([
      {
        field: "id",
        // label: "编码",
        label: t('campaign.canvas.code'),
        component: "a-input",
        operate: "eq",
        // placeholder: "请输入编码",
        placeholder: t('campaign.reminder.inputCanvasCode'),
        allowClear: true,
        comment: true,
        value: ""
      },
      {
        field: "name",
        // label: "名称",
        label: t('campaign.canvas.name'),
        component: "a-input",
        operate: "like",
        // placeholder: "请输入名称",
        placeholder: t('campaign.reminder.inputCanvasName'),
        allowClear: true,
        comment: false,
        value: ""
      },
      {
        field: "category",
        // label: "分类",
        label: t('campaign.canvas.category'),
        component: "a-select",
        operate: "eq",
        // placeholder: "请选择分类",
        placeholder: t('campaign.reminder.selectCanvasCategory'),
        dataSource: campaignCategories,
        allowClear: true,
        value: "",
        fieldNames: {
          label: "name",
          value: "id"
        }
      },
      {
        field: "status",
        // label: "状态",
        label: t('campaign.canvas.status'),
        component: "a-select",
        operate: "eq",
        // placeholder: "请选择状态",
        placeholder: t('campaign.reminder.selectCanvasStatus'),
        dataSource: campaignStatus,
        allowClear: true,
        value: ""
      },
      {
        field: "type",
        // label: "活动类型",
        label: t('campaign.canvas.type'),
        component: "a-select",
        operate: "eq",
        // placeholder: "请选择活动类型",
        placeholder: t('campaign.reminder.selectCampaignType'),
        dataSource: campaignType,
        allowClear: true,
        value: ""
      }
    ]);

    const hidenEmptyGroup = ref(false);
    const campaignList = ref([]);
    const campaignGroups = ref([]);
    const templateListRef = ref(null);
    const taskDrawerRef = ref(null);
    const mainRef = ref(null);
    const sort = ref("name,ASC");

    // 排序
    const sorterChange = (s) => {
      sort.value = s;

      mainRef.value.modelRef.search();
    };

    const bindData = async (expression) => {
      campaignList.value = [];
      // pagination.value.total = 0;
      const pageData = await findCampaignPage({
        ...pagination.value,
        page: pagination.value.page - 1,
        expression: addExp(expression, `groupId eq ${queryValue}`),
        sort: sort.value
      });
      campaignList.value = pageData.content;
      pagination.value.total = pageData.totalElements;
    };

    const refresh = async () => {
      mainRef.value.modelRef.search();
    };

    const openModel = async () => {
      templateListRef.value.visible = true;
    };

    // 跳转
    const routeToEdit = (type, templateId) => {
      // 修改条件判断，同时处理GROUP类型
      if (type === 'GROUP') {
        const query = { groupId: queryValue };
        if (templateId) {
          query.templateId = templateId;
        }
        router.push({ path: `${module.value.editPath}-wecom`, query });
      } else {
        // 对于其他类型保持原有逻辑
        taskDrawerRef.value.createTask(type, templateId);
      }
    };

    const getCampaignList = async (expression = null) => {
      const params = {
        expression
      };

      campaignList.value = await findCampaignList(params);
    };

    const handleSaveTask = async (task) => {
      if(task.id === '') delete task.id
      if(task.templateId){
        await createCampaignByTemplate(task.templateId, task);
      }else{
        await createCampaign(task);
      }
      Notification.info({
        // title: "保存营销活动",
        title: t('campaign.popup.save_title'),
        content: "营销活动保存成功。"
      });
      refresh();
    };

    const deleteTask = async (campaign) => {
      await deleteCampaign(campaign.id);
      // 根据删除任务的groupId局部查询、更新面板列
      campaignGroups.value[campaign.groupId] = [];
      mainRef.value.modelRef.search();
    };

    const getGroupItem = async () => {
      groupItemInfo.value = await getGroup(queryValue);
    };

    const getCampaignCategoriesList = async () => {
      campaignCategories.value = await findCategoryList();
    };

    const modifyTask = (campaign) => {
      const query = { id: campaign.id, groupId: queryValue };
      let editPath = `${module.value.editPath}-${campaign.type.toLowerCase()}`;
      
      // Handle special routing for GROUP type
      if (campaign.type === 'GROUP') {
        editPath = `${module.value.editPath}-wecom`;
      }
      
      router.push({ path: editPath, query });
    };

    const viewFlow = (campaign) => {
      const query = { id: campaign.id, groupId: queryValue };
      router.push({ path: module.value.viewPath, query });
    };

    const viewAudienceCampaign = (campaign) => {
      const query = { id: campaign.id, groupId: queryValue };
      router.push({ path: `/campaign/campaign/edit-audience`, query });
    };

    const viewGroupCampaign = (campaign) => {
      const query = { id: campaign.id, groupId: queryValue };
      router.push({ path: `/campaign/campaign/edit-wecom`, query });
    };

    const showBudget = (campaign) => {
      const query = {
        id: campaign.id,
        groupId: queryValue,
        status: campaign.status
      };
      router.push({ path: module.value.budgetPath, query });
    };

    const showReport = (campaign) => {
      localStorage.setItem(
        "workplaceItem",
        JSON.stringify({
          ...campaign,
          module: {
            mainPath: `/campaign/campaign`,
            // name: "活动画布",
            name: t('campaign.canvas.title'),
            mainQuery: {
              id: queryValue
            }
          }
        })
      );
      const query = { id: campaign.id, groupId: queryValue };
      router.push({ path: module.value.reportPath, query });
    };
    // 列表显示方式

    const setup = {
      queryValue,
      hidenEmptyGroup,
      groupListOpts,
      filter,
      module,
      sort,
      pagination,
      campaignList,
      groupItemInfo,
      sorterChange,
      bindData,
      refresh,
      modifyTask,
      deleteTask,
      viewAudienceCampaign,
      viewGroupCampaign,
      viewFlow,
      showBudget,
      showReport,
      campaignGroups,
      getCampaignList,
      getCampaignCategoriesList,
      getGroupItem,
      taskDrawerRef,
      mainRef,
      handleSaveTask,
      templateListRef,
      openModel,
      routeToEdit
    };
    provide("main", setup);
    return setup;
  },

  created() {
    this.getGroupItem();
    this.getCampaignCategoriesList();
  }
};
</script>

<style lang="less" scoped>
.current-card {
  border-bottom: 1px solid var(--color-neutral-3);
  margin-bottom: 15px;
}

.show-empty {
  margin: 5px 20px 5px 0;
}
</style>
