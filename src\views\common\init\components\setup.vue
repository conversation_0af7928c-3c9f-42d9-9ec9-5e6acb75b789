<template>
  <!--  初始化弹窗  -->
  <div class="initialize-data">
    <div style="width: 400px; margin: 0 auto; text-align: center">
      <i class="iconfont icon-initial"></i>
      <p>当前业务单元尚未初始化，点击下方按钮开始初始化</p>
      <!-- <p>{{ t(init.reminder.start) }}</p> -->
      <a-button type="primary" @click="props.initBu">
        开始初始化
        <!-- {{ t(init.button.start) }} -->
      </a-button>
    </div>
  </div>
</template>


<script setup>
import { ref, getCurrentInstance} from "vue";
const props = defineProps(["initBu"]);  
const {
      proxy: { t }
    } = getCurrentInstance()
</script>
