<template>
  <a-alert type="warning">
    如您需要分库，可选择配置独立存储信息，本产品运行时产生的定时任务数据将为您单独存储。
  </a-alert>
  <a-form ref="formRef" :model="formData">
    <a-row class="grid-demo" style="marginbottom: 16px">
      <a-col :span="4">
        <h3>数据源</h3>
      </a-col>
      <a-col :span="4">
        <h3>分库</h3>
      </a-col>
      <a-col :span="8">
        <h3>连接字符串</h3>
      </a-col>
    </a-row>

    <a-row class="grid-demo" style="marginbottom: 16px">
      <a-col :span="4">MongoDB</a-col>
      <a-col :span="4">
        <a-switch v-model="formData.MongoDB.zoned" type="round">
          <template #checked>是</template>
          <template #unchecked>否</template>
        </a-switch>
      </a-col>
      <a-col :span="8">
        <a-input v-model="formData.MongoDB.uri" :disabled="!formData.MongoDB.zoned" placeholder="请输入MongoDB连接字符串" />
      </a-col>
    </a-row>

    <a-row class="grid-demo" style="marginbottom: 16px">
      <a-col :span="4">ElasticSearch</a-col>
      <a-col :span="4">
        <a-switch v-model="formData.ElasticSearch.zoned" type="round">
          <template #checked>是</template>
          <template #unchecked>否</template>
        </a-switch>
      </a-col>
      <a-col :span="8">
        <a-input v-model="formData.ElasticSearch.uri" :disabled="!formData.ElasticSearch.zoned"
          placeholder="请输入ElasticSearch连接字符串" />
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps(["setting"]);

const formData = ref(props.setting);
const formRef = ref(null);

const commit = async (entity) => {
  if (await formRef.value.validate()) {
    return false;
  }
  entity.value.setting.devices = formData.value;
  return true;
};

defineExpose({
  commit
});
</script>

<style lang="less" scoped>
.arco-alert {
  margin: 10px;
}

.grid-demo {
  margin-top: 30px;
}
</style>
