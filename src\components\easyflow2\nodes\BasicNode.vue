<template>
  <div class="easyflow-node" :class="nodeClass" :style="{ '--bgColor': headerColor }">
    <node-top :is-debug="isDebug" />
    <node-left />
    <node-right />
    <node-bottom />
    <div class="node-head" :style="{ background: background }">
      <div>
        <span class="node-icon iconfont" :class="iconClass" :style="{ color: headerColor }" />
        <span>{{ title }}</span>
      </div>
      <!-- 暂时屏蔽 -->
      <div v-if="!monitorData.isMonitor">
        <a-popconfirm content="确认删除节点?" type="info" @ok="removeNode">
          <span v-if="!fixed" class="iconfont card-btn icon-rubbish" @mousedown="stopEvent" />
        </a-popconfirm>
      </div>
    </div>
    <div class="node-body">
      <div v-if="!monitorData.isMonitor" class="node-brief">
        <table v-if="!monitorData.isMonitor" class="brief-table">
          <template v-for="(item, index) in brief.slice(0, 2)" :key="index">
            <tr class="brief-item">
              <td class="label">{{ item.label }}:</td>
              <td class="value">{{ item.value }}</td>
            </tr>
          </template>
        </table>
        <div class="node-bottom">
          <a-popover v-if="!monitorData.isMonitor" position="right">
            <div><span v-if="brief.length > 2" class="more">更多</span></div>
            <template #content>
              <div class="brief">
                <table class="brief-table">
                  <template v-for="(item, index) in brief" :key="index">
                    <tr class="brief-item">
                      <td class="label">{{ item.label }}:</td>
                      <td class="value">{{ item.value }}</td>
                    </tr>
                  </template>
                </table>
              </div>
            </template>
          </a-popover>
        </div>
      </div>
      <div v-if="!ignoreMonitor && monitorData.isMonitor" class="monitor">
        <div class="basic">
          <div>
            <span class="iconfont icon-in" title="入站" />
            <i>{{ monitorData.incoming }}</i>
          </div>
          <div v-if="!ignoreOutgoingMonitor">
            <span class="iconfont icon-out" title="出站" />
            <i>{{ monitorData.outgoing }}</i>
          </div>
          <div v-if="monitorData.error > 0" style="color: red">
            <span class="iconfont icon-cuowu" style="color: red" title="失败" />
            <i>{{ monitorData.error }}</i>
          </div>
        </div>
        <div class="reach">
          <template v-for="(item, key, index) in monitorData.reachStatus" :key="index">
            <div>
              <span>{{ filters(reachStatus, key) }} :</span>
              <i>{{ item }}</i>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ref,
  provide,
  inject,
  onMounted,
} from "vue";
import {
  Popover,
  Dropdown,
  Button,
  Doption,
  Popconfirm,
} from "@arco-design/web-vue";
import mixin from "../components/mixin/node";
import { top, left, right, bottom } from "../components/node";
import { filters } from "@/utils/filter";
import { reachStatus } from "@/constant/reach";

export default {
  components: {
    "node-top": top,
    "node-left": left,
    "node-right": right,
    "node-bottom": bottom,
    "a-popover": Popover,
    "a-dropdown": Dropdown,
    "a-button": Button,
    "a-doption": Doption,
    "a-popconfirm": Popconfirm,
  },
  mixins: [mixin],
  setup() {
    const node = inject("node");
    const nodeRef = ref(null);
    const graph = ref(null);


    const info = node;
    const title = ref(node.title);
    const summary = ref(node.summary);
    const iconClass = ref(node.iconClass);
    const nodeClass = ref(node.nodeClass);
    let headerColor = ref(node.headerColor);
    let headerBgColor = ref(node.headerBgColor);
    let background = ref(node.background);
    const fixed = ref(!!node.fixed);
    const ignoreMonitor = ref(!!node.ignoreMonitor);
    const ignoreOutgoingMonitor = ref(!!node.ignoreOutgoingMonitor);
    const mounted = ref(false);

    const isDebug = ref(false);
    const monitorData = ref({
      isMonitor: false,
      incoming: 0,
      outgoing: 0,
      error: 0
    });
    const getNode = inject("getNode");
    const getGraph = inject("getGraph");
    const brief = ref([]);
    const record = ref(0);
    const nodeId = ref(null);

    onMounted(async () => {
      if (mounted.value) {
        return;
      }
      mounted.value = true;
      const _node = getNode();
      nodeId.value = _node.id;
      brief.value = node.getBrief ? await node.getBrief(_node.data) : [];

      // 重新赋值颜色
      let nodeItem = JSON.parse(localStorage.getItem('nodeItem')) || {}
      if (_node?.data?.configId) {
        let item = nodeItem[_node?.data?.configId]
        if (item) {
          headerColor.value = item.themeColor
          headerBgColor.value = item.themeColor
          background.value = item.background
          iconClass.value = item.icon
        }

      }


      title.value = _node.data._name;
      _node.on("change:data", changeNodeData);
      _node.on("change:attrs", (args) => {
        if (_node.attrs.monitor) {
          monitorData.value.isMonitor = _node.attrs.monitor.enabled;
          monitorData.value.incoming = _node.attrs.monitor.income;
          monitorData.value.outgoing = _node.attrs.monitor.outgoing;
          monitorData.value.error = _node.attrs.monitor.error;
          monitorData.value.reachStatus = _node.attrs.monitor.reachStatus;
        } else if (_node.attrs.record) {
          isDebug.value = _node.attrs.record.enabled;
          record.value = _node.attrs.record.count;
        }
      });
    });

    const changeNodeData = async ({ cell, current }) => {
      title.value = current._name;
      brief.value = node.getBrief ? await node.getBrief(current) : [];
    };

    const removeNode = () => {
      getGraph().removeNode(getNode());
    };

    const setup = {
      // type,
      nodeId,
      info,
      title,
      summary,
      iconClass,
      nodeClass,
      headerColor,
      headerBgColor,
      background,
      fixed,
      nodeRef,
      graph,
      getNode,
      getGraph,
      removeNode,
      isDebug,
      record,
      monitorData,
      brief,
      ignoreMonitor,
      ignoreOutgoingMonitor,
      flow: ref(node.flow),
      filters,
      reachStatus
    };

    provide("node", setup);
    return setup;
  },
  methods: {
    stopEvent(e) {
      e.stopPropagation();
    },
  },
};
</script>

<style lang="less" scoped>
.easyflow-node {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #ffffff;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.07);

  &::before {
    display: block;
    content: "";
    height: 6px;
    width: 100%;
    border-radius: 5px 5px 0 0;
    background: var(--bgColor);
  }

  &:hover {
    .node-action {
      display: block;
    }

    .flow-node-top,
    .flow-node-left,
    .flow-node-right,
    .flow-node-bottom {
      display: flex;
    }
  }

  .node-head {
    height: 30px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    align-items: center;
    // background: #fff9ef;
    font-family: PingFang SC;

    .node-head-left{
      display: flex;
      align-items: center;
      height: 30px;
    }
    .node-icon {
      font-size: 14px;
      margin-left: 10px;
      margin-right: 9px;
      font-weight: bolder;
    }
    .node-name {
      width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .card-btn {
      margin: 4px;
      color: lightgray;
      cursor: pointer;

      &:hover {
        color: rgb(var(--arcoblue-6));
      }
    }

    .icon-gengduo {
      margin: 5px;
    }
  }

  .node-body {
    height: 68px;
    padding: 6px;
    font-size: 14px;
    color: #333333;
    font-weight: 300;
    box-sizing: border-box;
    font-family: PingFang SC;

    .monitor {
      .iconfont {
        font-size: 10px;
        margin-right: 10px;
        color: gray;
        cursor: pointer;

        &:hover {
          color: rgb(var(--arcoblue-6));
        }
      }

      .basic {
        width: 50%;
        float: left;
      }

      .reach {
        width: 50%;
        float: right;
      }
    }
  }
}

.node-brief {
  overflow: hidden;

  .brief-table {
    .brief-item {
      .label {
        min-width: 62px;
        font-weight: bold;
        margin-right: 5px;
        text-align: right;
      }

      .value {
        text-align: left;
        overflow: hidden;
        padding-left: 10px;
        white-space: nowrap;
      }
    }
  }

  .more {
    font-weight: bold;
    float: right;
    color: lightgray;
    cursor: pointer;

    &:hover {
      color: rgb(var(--arcoblue-6));
    }
  }
}

.node-record {
  position: absolute;
}
</style>
