import i18n from "../locale";

export const viewTypes = [
  {
    value: "NONE",
    // label: "无",
    title: i18n.global.t("global.reach.none"),
  },
  {
    value: "MMS",
    // label: "彩信",
    title: i18n.global.t("global.reach.mms"),
  },
  {
    value: "SMS",
    // label: "短信",
    title: i18n.global.t("global.reach.sms"),
  },
];

export const reachStatus = [
  {
    // label: "发送中",
    label: i18n.global.t("global.reach.status.sending"),
    value: "SENDING"
  },
  {
    // label: "发送成功",
    label: i18n.global.t("global.reach.status.sendSuccess"),
    value: "SENT"
  },
  {
    // label: "被限制",
    label: i18n.global.t("global.reach.status.restricted"),
    value: "LIMITED"
  },
  {
    // label: "发送出错",
    label: i18n.global.t("global.reach.status.sendError"),
    value: "ERROR"
  },
  {
    // label: "推迟执行",
    label: i18n.global.t("global.reach.status.postponeExecution"),
    value: "DELAY"
  },
  {
    // label: "勿扰时段",
    label: i18n.global.t("global.reach.status.silence"),
    value: "SILENCE"
  },
  {
    // label: "忽略",
    label: i18n.global.t("global.reach.status.ignore"),
    value: "SKIP"
  },
  {
    // label: "已接收",
    label: i18n.global.t("global.reach.status.received"),
    value: "RECEIVED"
  },
  {
    // label: "接受",
    label: i18n.global.t("global.reach.status.accepted"),
    value: "ACCEPTED"
  },
  {
    // label: "拒绝",
    label: i18n.global.t("global.reach.status.rejected"),
    value: "REFUSED"
  },
  {
    // label: "拒绝该渠道消息",
    label: i18n.global.t("global.reach.status.rejectChannelMessages"),
    value: "REFUSE_CHANNEL"
  },
  {
    // label: "黑名单",
    label: i18n.global.t("global.reach.status.blacklist"),
    value: "BLACKLIST"
  },
  {
    // label: "触达超时",
    label: i18n.global.t("global.reach.status.touchTimeout"),
    value: "TIMEOUT"
  }
]
