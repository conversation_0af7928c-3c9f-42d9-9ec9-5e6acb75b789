<template>
  <div class="easyflow-pannel-intersect">
    <a-form
      class="easyflow-pannel-form pannel-form"
      layout="vertical"
      :model="entity"
      :disabled="!editEnable"
    >
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { findAudienceList } from "@/api/audience";
const props = defineProps(["node"]);
const node = props.node;
const loading = ref(false);
const pannelInject = inject("pannel");
const editEnable = pannelInject.editEnable;
const entity = ref({
  audiences: [],
});
const audiences = ref([]);
const save = () => {
  return entity.value;
};

const addAudience = async () => {
  if(audiences.value.length == 0){
    await handleSearchAudience();
  }
  entity.value.audiences.push({});
};
const delAudience = (index) => {
  entity.value.audiences.splice(index, 1);
};
const handleSearchAudience = async (value) => {
    let params = { fields: "name" };
  if (value) {
    params.expression = `name like ${value}`;
  }
  audiences.value = await findAudienceList(params);
};

defineExpose({
  save,
});
onMounted(() => {
  Object.assign(entity.value, node.data);
});
</script>


<style lang="less" scoped>
.audience-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 10px 0;
}
.arco-icon-delete {
  cursor: pointer;
}
</style>
