export default {
  "menu.benefit": "营销云权益",
  "menu.benefit.main": "活动模板",
  "menu.flow": "Flow",
  "menu.flow.template": "触达模板",
  "menu.communicate.template": "沟通模板",
  "menu.reach.template": "触达管理",
  "menu.communicate": "营销云触达",
  "menu.communicate.channel": "触达管理",
  reach: {
    title: {
      template: "触达模板",
      communicationTouchpoint: "沟通触点",
      communicationList: "沟通列表",
      templateCode: "模板编码",
      templateName: "模板名称",
      status: "状态",
      reachTemplateManagement: "触达模板管理",
      editReachTemplate: "编辑触达模板",
      aiGenerated: "AI生成",
      communicationCode: "沟通编码",
      communicationName: "沟通名称",
      communicationTemplate: "沟通模板",
      communicationGroup: "沟通分组",
      deleteReachTemplate: "删除沟通模板"
    },
    popup: {
      deleteTitle: "删除流程模板",
    },
    edit: {
      communicationLimitModel: "沟通限制模型",
      limitFrequencyModel: "在该渠道下限制频次的模型",
      remark: "描述",
      frequencyLimit: "频次限制",
      communicationLimitIdentificationField: "沟通限制识别字段",
      annualCount: "每年次数",
      monthlyCount: "每月次数",
      dailyCount: "每天次数",
    },
    column: {
      templateCode: "模板编码",
      templateName: "模板名称",
      touchpoint: "触点",
      templateContent: "模板内容",
      category: "分组",
      status: "状态",
      signature: "签名",
      sync: "同步",
      updateTime: "更新时间",
      action: "操作",
    },
    status: {
      sync: "同步",
      selfBuilt: "自建",
      yes: "是",
      no: "否",
    },
    type: {
      sms: "短信",
      mms: "彩信",
      wechatTemplate: "微信模板",
      wechatNotification: "微信通知",
      app: "App",
    },
    sms: {
      contentName: "内容编码",
      touchpoint: "触点",
      communicationId: "沟通Id",
      communicationName: "沟通名称",
      templateCode: "模板编码",
      templateName: "模板名称",
      communicationGroup: "沟通分组",
      content: "内容",
      status: "状态",
      createTime: "创建时间",
      template: "触达模板",
      templateContent: "模板内容",
      description: "描述信息",
      fieldMappingConfiguration: "字段映射配置",
      templateField: "模板字段",
      mappingRelationship: "映射关系",
      mappedField: "映射字段",
      defaultValue: "默认值",
      fieldLengthLimit: "字段长度限制",
      mappingType: "映射类型",
      syncTemplateField: "同步模板字段",
      customerField: "客户字段",
      behaviorField: "行为字段",
      fixedValue: "固定值",
      replacementValue: "替换值",
      flow: "FLOW",
      currentTime: "当前时间",
      currentDate: "当前日期",
    },
    reminder: {
      touchpoint: "请选择触点",
      templateCode: "请输入模板编码",
      templateName: "请输入模板名称",
      templateContent: "请输入模板内容",
      category: "请输入分组",
      status: "请选择状态",
      signature: "请输入签名",
      remark: "请输入描述",
      description: "请输入描述信息",
      frequencyLimit: "当前渠道是否需要频次限制",
      communicationLimitIdentificationField: "用户在该渠道下限制频次的识别字段",
      annualCount: "用户在该渠道下每年限制触达次数",
      monthlyCount: "用户在该渠道下每月限制触达次数",
      dailyCount: "用户在该渠道下每天限制触达次数",
      field: "请选择字段",
      communicationCode: "请输入沟通编码",
      communicationName: "请输入沟通名称",
      communicationTemplate: "请输入沟通模板",
      communicationGroup: "请输入沟通分组",
      communicationId: "请输入沟通Id",
      template: "请选择模版",
      selectReplacementValue: "请选择替换值",
      bindingFieldNotRequired: "绑定字段不需选择",
      inputExpression: "请输入表达式",
      inputProcessStartId: "请输入流程起点Id",
      inputProcessId: "请输入流程Id",
      inputFixedValue: "请输入固定值",
      inputDefaultValue: "请输入默认值",
      inputLimitSize: "长度限制",
      timeFormat: "时间格式",
      behaviorModel: "选择行为模型",
      selectModel: "请选择模型",
    }
  }
};
