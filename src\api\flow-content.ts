/**
 * 节点服务管理
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function createContent(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/flow-content`;
  return axios.post(uri, info) ;
}

export function modifyContent(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/flow-content`;
  return axios.put(uri, info);
}

export function deleteContent(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/flow-content/${id}`
  );
}

export function getContent(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-content/${id}`);
}

export function findContentList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-content/list`, {
    params,
  });
}

export function findContentPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-content`, {
    params: {
      ...params,
      ...query,
    },
  });
}
