<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <module edit>
    <template #main>
      <a-spin style="display: block"
              :loading="spinLoading">
        <a-form ref="dataFormRef"
                layout="vertical"
                class="general-form"
                :model="entity">
          <a-space direction="vertical"
                   :size="16">
            <a-card class="general-card">
              <a-row :gutter="80">
                <a-col :span="8">
                  <a-form-item field="id"
                               :label="t('systemSetting.canvasNodeManagement.chuDianBianMa')"
                               :rules="[{ required: true, message: t('systemSetting.canvasNodeManagement.QSRCDBM') }]"
                               label-col-flex="70px">
                    <a-input v-model="entity.id"
                             :placeholder="t('systemSetting.canvasNodeManagement.chuDianBianMa')"
                             :disabled="module.isEdit" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="name"
                               :label="t('systemSetting.canvasNodeManagement.chuDianMingCheng')"
                               :rules="[{ required: true, message: t('systemSetting.canvasNodeManagement.QSRCDMC') }]"
                               label-col-flex="70px">
                    <a-input v-model="entity.name"
                             :placeholder="t('systemSetting.canvasNodeManagement.chuDianMingCheng')" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="enabled"
                               :label="t('systemSetting.canvasNodeManagement.zhuangTai')"
                               label-col-flex="70px">
                    <a-switch v-model="entity.enabled"
                              type="round">
                      <template #checked> {{t('systemSetting.canvasNodeManagement.keYong')}} </template>
                      <template #unchecked> {{t('systemSetting.canvasNodeManagement.tingYong')}} </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="80">
                <a-col :span="16">
                  <a-form-item field="summary"
                               :label="t('systemSetting.canvasNodeManagement.chuDianShuoMing')"
                               label-col-flex="70px">
                    <a-input v-model="entity.summary"
                             type="textarea"
                             :placeholder="t('systemSetting.canvasNodeManagement.QSRCDSM')" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="groupId"
                               :label="t('systemSetting.canvasNodeManagement.xuanZeFenLei')"
                               :rules="[{ required: true, message: t('systemSetting.canvasNodeManagement.qingShuRuFenLei') }]"
                               label-col-flex="70px">
                    <a-select v-model="entity.groupId"
                              :placeholder="t('systemSetting.canvasNodeManagement.qingXuanZeFenLei')">
                      <a-option v-for="item of groups"
                                :key="item.id"
                                :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="80">
                <a-col :span="8">
                  <a-form-item field="icon"
                               :label="t('systemSetting.canvasNodeManagement.tuBiao')"
                               label-col-flex="70px"
                               :rules="[{ required: true, message: t('systemSetting.canvasNodeManagement.qingXuanZeTuBiao') }]">
                    <div :class="[`iconfont ${entity.icon}`]"
                         style="width: 100px; margin-left: 30px"></div>
                    <a-button type="primary"
                              @click="selectIcon">{{$t('systemSetting.canvasNodeManagement.xuanZeTuBiao')}}</a-button>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="background"
                               :label="t('systemSetting.canvasNodeManagement.beiJingSe')"
                               label-col-flex="70px"
                               :rules="[{ required: true, message: t('systemSetting.canvasNodeManagement.qingShuRuBeiJingSe') }]">
                    <a-input v-model="entity.background"
                             :placeholder="t('systemSetting.canvasNodeManagement.qingShuRuBeiJingSe')" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="themeColor"
                               :label="t('systemSetting.canvasNodeManagement.zhuTiSe')"
                               label-col-flex="70px"
                               :rules="[{ required: true, message: t('systemSetting.canvasNodeManagement.qingShuRuZhuTiSe') }]">
                    <a-input v-model="entity.themeColor"
                             :placeholder="t('systemSetting.canvasNodeManagement.qingShuRuZhuTiSe')" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="80">
                <a-col :span="8">
                  <a-form-item field="icon"
                               :label="t('systemSetting.canvasNodeManagement.paiXu')"
                               label-col-flex="70px"
                               :tooltip="t('systemSetting.canvasNodeManagement.AZCXDDDSXPX')">
                    <a-input-number v-model="entity.index"
                                    :placeholder="t('systemSetting.canvasNodeManagement.qingShuRuPaiXuZhi')" />
                  </a-form-item>
                </a-col>
              </a-row>
              <template v-if="entity.type === 'flow_content' || !entity?.type">
                <a-row>
                  <a-col :span="8">
                    <h3>{{$t('systemSetting.canvasNodeManagement.chuDianPeiZhi')}}</h3>
                  </a-col>
                </a-row>

                <a-row :gutter="40">
                  <a-col :span="8">
                    <a-form-item field="category"
                                 :rules="[{ required: true, message: t('systemSetting.canvasNodeManagement.qingShuRuFenLei')}]"
                                 label-col-flex="70px">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.sheZhiFenLei')}}
                        <a-tooltip position="right" content="在 【系统设置】-【触点分类管理】管理数据">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-select v-model="entity.flowCategory"
                                :placeholder="t('systemSetting.canvasNodeManagement.QXZSZFL')">
                        <a-option v-for="item of nodeSettingCategories"
                                  :key="item.id"
                                  :value="item.id">{{ item.name
                          }}</a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item field="category"
                                 :rules="[{ required: true, message: t('systemSetting.canvasNodeManagement.QSRMBLX') }]"
                                 label-col-flex="70px">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.muBanLeiXing')}}
                        <a-tooltip position="right" content="模板预览时，按对应格式显示">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-select v-model="entity.setting.templateType"
                                :placeholder="t('systemSetting.canvasNodeManagement.QSRMBLX')">
                        <a-option v-for="item of [
        { id: 'WECHAT_TEMPLATE_MSG', name: t('systemSetting.canvasNodeManagement.weiXinMuBanXiaoXi') },
        { id: 'WECHAT_CUSTOMER_MSG', name: t('systemSetting.canvasNodeManagement.weiXinKeFuXiaoXi') },
        {id: 'TENCENT_WORK', name: t('systemSetting.canvasNodeManagement.qiWeiMuBanXiaoXi')},
        { id: 'SMS', name: t('systemSetting.canvasNodeManagement.duanXin') },
        { id: 'MMS', name: t('systemSetting.canvasNodeManagement.caiXin') },
        { id: 'EMAIL', name: t('systemSetting.canvasNodeManagement.youJian')},

      ]"
                                  :key="item.id"
                                  :value="item.id">{{ item.name
                          }}</a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item field="setting.reachField">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.chuDaZiDuan')}}
                        <a-tooltip position="right"
                                   :content="t('systemSetting.canvasNodeManagement.YHZGQDXDCDZD')">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-tree-select v-model="entity.setting.reachField"
                                     :data="modelFields"
                                     allow-clear
                                     allow-search
                                     :field-names="fieldStruct"
                                     :placeholder="t('systemSetting.canvasNodeManagement.qingXuanZeZiDuan')" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item field="setting.reachFieldPrefix">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.shenFenZiDuanQianZhui')}}
                        <a-tooltip position="right"
                                   :content="t('systemSetting.canvasNodeManagement.HCDZDYQZCSFBSZD')">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-input v-model="entity.setting.reachFieldPrefix" />
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="40">
                  <a-col :span="6">
                    <a-form-item field="setting.assembleContent">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.zhuangPeiNeiRong')}}
                        <a-tooltip :content="t('systemSetting.canvasNodeManagement.shiFouZai')">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-switch v-model="entity.setting.assembleContent"
                                type="round"
                                class="form-switch">
                        <template #checked>{{$t('systemSetting.canvasNodeManagement.shi')}}</template>
                        <template #unchecked>{{$t('systemSetting.canvasNodeManagement.fou')}}</template>
                      </a-switch>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item field="setting.customerData">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.chuanDiKeHuShuJu')}}
                        <a-tooltip :content="t('systemSetting.canvasNodeManagement.SFCDKHSJD')">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-switch v-model="entity.setting.customerData"
                                type="round"
                                class="form-switch">
                        <template #checked>{{$t('systemSetting.canvasNodeManagement.shi')}}</template>
                        <template #unchecked>{{$t('systemSetting.canvasNodeManagement.fou')}}</template>
                      </a-switch>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item field="setting.contentData">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.chuanDiNeiRongShuJu')}}
                        <a-tooltip :content="t('systemSetting.canvasNodeManagement.SFCDNRSJD')">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-switch v-model="entity.setting.contentData"
                                type="round"
                                class="form-switch">
                        <template #checked>{{$t('systemSetting.canvasNodeManagement.shi')}}</template>
                        <template #unchecked>{{$t('systemSetting.canvasNodeManagement.fou')}}</template>
                      </a-switch>
                    </a-form-item>
                  </a-col>
                  <!-- <a-col :span="6">
                    <a-form-item field="setting.feedback">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.dengDaiFanKuiJieGuo')}}
                        <a-tooltip  :content="t('systemSetting.canvasNodeManagement.SFDDFKJGCZXHXLC')}}">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-switch v-model="entity.setting.feedback" type="round" class="form-switch">
                        <template #checked>{{$t('systemSetting.canvasNodeManagement.shi')}}</template>
                        <template #unchecked>{{$t('systemSetting.canvasNodeManagement.fou')}}</template>
                      </a-switch>
                    </a-form-item>
                  </a-col> -->
                </a-row>
                <a-row v-if="entity.setting.assembleContent"
                       :gutter="40">
                  <a-col :span="6">
                    <a-form-item field="setting.lengthLimit">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.lengthLimit')}}
                      </template>
                      <a-input-number v-model="entity.setting.lengthLimit">
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="40">
                  <a-col :span="12">
                    <a-form-item field="setting.publishId"
                                 :label="t('systemSetting.canvasNodeManagement.guanLianLiuCheng')"
                                 label-col-flex="70px">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.guanLianLiuCheng')}}
                        <a-tooltip :content="t('systemSetting.canvasNodeManagement.zai')">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-select v-model="entity.setting.publishId"
                                class="easyflow-select"
                                :placeholder="t('systemSetting.canvasNodeManagement.qingXuanZe')"
                                :loading="loading"
                                :filter-option="false"
                                @search="handleSearchFlow"
                                @change="handleSearchFlowStarts">
                        <a-option v-for="item of flows"
                                  :key="item.id"
                                  :value="item.id">{{ item.name }}</a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item field="startId"
                                 :label="t('systemSetting.canvasNodeManagement.liuChengQiDian')"
                                 label-col-flex="70px">
                      <a-select v-model="entity.setting.startId"
                                class="easyflow-select"
                                :placeholder="t('systemSetting.canvasNodeManagement.qingXuanZe')"
                                :loading="loading"
                                :filter-option="false">
                        <a-option v-for="item of starts"
                                  :key="item.taskId"
                                  :value="item.taskId">{{ item.name
                          }}</a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <limit-setting v-model:limitSetting="entity.setting.limitSetting"
                               :model-fields="modelFields" />
                <a-row :gutter="40">
                  <a-col :span="8">
                    <a-form-item field="setting.batchSize"
                                 :label="t('systemSetting.canvasNodeManagement.piCiShuLiang')"
                                 label-col-flex="70px">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.piCiShuLiang')}}
                        <a-tooltip :content="t('systemSetting.canvasNodeManagement.DQQMZPCSLS')">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-input-number v-model="entity.setting.batchSize"
                                      :placeholder="t('systemSetting.canvasNodeManagement.QSRPCSL')" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item field="setting.flushTime"
                                 :label="t('systemSetting.canvasNodeManagement.piCiShuaXinShiJian')"
                                 label-col-flex="70px">
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.piCiShuaXinShiJian')}}
                        <a-tooltip :content="t('systemSetting.canvasNodeManagement.DDDPCSXSJRBMZPCSL')">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-input-number v-model="entity.setting.flushTime"
                                      :placeholder="t('systemSetting.canvasNodeManagement.QSRPCSXSJ')">
                        <template #append>{{$t('systemSetting.canvasNodeManagement.miao')}}</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                </a-row>
                <!--
                <a-row :gutter="80">
                  <a-col :span="4">
                    <a-form-item>
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.pinCiXianZhi')}}
                        <a-tooltip  :content="t('systemSetting.canvasNodeManagement.DQQDSFXYPCXZ')}}">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-switch v-model="entity.setting.limitSetting.type" type="round" class="form-switch"
                        checked-value="DATE" unchecked-value="DISABLED">
                        <template #checked>{{$t('systemSetting.canvasNodeManagement.shi')}}</template>
                        <template #unchecked>{{$t('systemSetting.canvasNodeManagement.fou')}}</template>
                      </a-switch>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item>
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.GTXZSBZD')}}
                        <a-tooltip  :content="t('systemSetting.canvasNodeManagement.YHZGQDXXZPCDSBZD')}}">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-tree-select v-model="entity.setting.limitSetting.identifyField"
                        :disabled="entity.setting.limitSetting.type == 'DISABLED'" :data="modelFields" allow-clear
                        allow-search :field-names="fieldStruct" :placeholder="t('systemSetting.canvasNodeManagement.qingXuanZeZiDuan')}}" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="4">
                    <a-form-item>
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.meiNianCiShu')}}
                        <a-tooltip  :content="t('systemSetting.canvasNodeManagement.YHZGQDXMNXZCDCS')}}">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-input-number v-model="dateUnits.YEAR"
                        :disabled="entity.setting.limitSetting.type == 'DISABLED'" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="4">
                    <a-form-item>
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.meiYueCiShu')}}
                        <a-tooltip  :content="t('systemSetting.canvasNodeManagement.YHZGQDXMYXZCDCS')}}">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-input-number v-model="dateUnits.MONTH"
                        :disabled="entity.setting.limitSetting.type == 'DISABLED'" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="4">
                    <a-form-item>
                      <template #label>
                        {{$t('systemSetting.canvasNodeManagement.meiTianCiShu')}}
                        <a-tooltip  :content="t('systemSetting.canvasNodeManagement.YHZGQDXMTXZCDCS')}}">
                          <icon-info-circle />
                        </a-tooltip>
                      </template>
                      <a-input-number v-model="dateUnits.DAY"
                        :disabled="entity.setting.limitSetting.type == 'DISABLED'" />
                    </a-form-item>
                  </a-col>
                </a-row>
 -->
              </template>
            </a-card>
          </a-space>
        </a-form>
        <IconDrawer ref="iconDrawer"
                    :visible="visible"
                    :drawer-title="drawerTitle"
                    @updateDrawerVisible="updateDrawerVisible"
                    @selectionIcon="selectionIcon"></IconDrawer>
      </a-spin>
    </template>
  </module>
</template>

<script>
import {ref, provide, getCurrentInstance} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {createNode, modifyNode, getNode} from '@/api/node'
import {findFlowList, findFlowStarts} from '@/api/campaign'
import {findNodeCategoryList} from '@/api/node-category'
import {findNodeGroupList} from '@/api/node-group'
import {Message} from '@arco-design/web-vue'
import {findCustomerModel} from '@/api/system'
import {formatFields} from '@/utils/field'
import LimitSetting from '@/components/ma/limit-setting/index.vue'
import IconDrawer from './component/icon-drawer.vue'

export default {
  components: {
    IconDrawer,
    LimitSetting
  },

  setup() {
    const {
      proxy: {t}
    } = getCurrentInstance()
    const route = useRoute()
    const router = useRouter()
    const queryValue = route.query.id
    const spinLoading = ref(false)
    const module = ref({
      entityIdField: 'id',
      mainPath: '/system/node-config',
      breadcrumb: [
        {
          name: t('menu.system'),
          path: '/system/setting'
        },
        {
          name: t('systemSetting.canvasNodeManagement.canvasNodeManagement'),
          path: '/system/node-config'
        },
        {
          name: t('systemSetting.canvasNodeManagement.bianJiHuaBuJieDian')
        }
      ],
      isEdit: !!queryValue
    })

    const visible = ref(false)
    const drawerTitle = ref("{{$t('systemSetting.canvasNodeManagement.tuBiaoXuanZe')}}")
    const iconDrawer = ref(null)
    const loading = ref(false)

    const entity = ref({
      type: 'flow_content',
      category: 'FLOW_TEMPLATE',
      setting: {
        feedback: false,
        customerData: true,
        contentData: true,
        assembleContent: false,
        limitSize: 0,
        limitSetting: {
          type: 'DISABLED',
          dateLimit: {}
        },
        batchSize: 500,
        flushTime: 20
      }
    })
    const dataFormRef = ref({})
    const typeList = ref([
      {value: 'flow_content', label: 'FLOW_CONTENT'},
      {value: 'system', label: 'SYSTEM', disable: true}
    ])
    const nodeCategories = ref([
      {id: 'SYSTEM', name: 'SYSTEM'},
      {id: 'FLOW_TEMPLATE', name: 'FLOW_TEMPLATE'}
    ])
    const fieldStruct = ref({
      key: 'path',
      value: 'path',
      title: 'label',
      children: 'fields'
    })
    const nodeSettingCategories = ref([])
    const groups = ref([])
    const flows = ref([])
    const starts = ref([])
    const modelFields = ref([])

    const getNodeSettingCategories = async () => {
      const res = await findNodeCategoryList()
      res.map(item => {
        nodeSettingCategories.value.push({id: item.id, name: item.name})
        return []
      })
    }
    const getModelFields = async () => {
      const data = await findCustomerModel()
      // console.log(data);
      if (data) {
        modelFields.value = formatFields(data.fields)
      }
      // console.log(modelFields.value);
    }
    const getNodeGroupList = async () => {
      const res = await findNodeGroupList()
      res.map(item => {
        groups.value.push({id: item.id, name: item.name})
        return []
      })
    }
    const handleSearchFlow = async name => {
      loading.value = true
      const params = {fields: 'name'}
      params.expression = 'status eq ENABLED'
      if (name) {
        params.expression += `AND name like ${name}`
      }
      flows.value = await findFlowList(params)
      loading.value = false
    }

    const handleSearchFlowStarts = async () => {
      starts.value = []
      // entity.value.setting.startId = "";
      loading.value = true
      starts.value = await findFlowStarts(entity.value.setting.publishId)
      loading.value = false
    }

    const bindData = async () => {
      if (module.value.isEdit) {
        entity.value = await getNode(queryValue)
        if (entity.value.setting?.publishId) {
          starts.value = await findFlowStarts(entity.value.setting.publishId)
        }

        if (entity.value.setting?.limitSetting && !entity.value.setting.limitSetting.dateLimit) {
          entity.value.setting.limitSetting.dateLimit = {}
        }
      }
      if(entity.value.category === "FLOW_TEMPLATE"){
        await handleSearchFlow()
        if (entity.value.setting.publishId) {
          handleSearchFlowStarts()
        }
      }
    }
    const isChinese = id => {
      const regex = /[^\x00-\xff]/
      return regex.test(id)
    }
    const selectIcon = () => {
      visible.value = true
    }
    const updateDrawerVisible = val => {
      visible.value = val
    }
    const selectionIcon = val => {
      entity.value.icon = val
    }
    const quit = () => {
      router.push({path: module.value.mainPath})
    }
    const save = async () => {

      dataFormRef.value.validate()
      if (isChinese(entity.value.id)) {
        Message.error(t('systemSetting.canvasNodeManagement.BMBKWZW'))
        return
      }

      if (!entity.value.setting.type) {
        entity.value.setting.type = entity.value.category
      }
      if (!entity.value?.enabled) {
        entity.value.enabled = false
      }
      if (!entity.value?.setting?.limitSetting?.enabled && entity.value.category !== 'SYSTEM') {
        entity.value.setting.limitSetting.enabled = false
      }
      // eslint-disable-next-line no-restricted-syntax
      if (module.value.isEdit) {
        await modifyNode(entity.value)
      } else {
        await createNode(entity.value)
      }
      spinLoading.value = false
      Message.success(t('global.tips.success.save'))
      quit()
    }
    getModelFields()
    getNodeSettingCategories()
    getNodeGroupList()
    const setup = {
      t,
      save,
      route,
      router,
      spinLoading,
      module,
      entity,
      visible,
      drawerTitle,
      iconDrawer,
      dataFormRef,
      bindData,
      loading,
      nodeCategories,
      nodeSettingCategories,
      groups,
      typeList,
      flows,
      starts,
      modelFields,
      fieldStruct,
      selectIcon,
      handleSearchFlow,
      handleSearchFlowStarts,
      getNodeSettingCategories,
      getNodeGroupList,
      updateDrawerVisible,
      selectionIcon
    }
    provide('edit', setup)
    return setup
  }
}
</script>
