import { resolve } from "path";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import svgLoader from "vite-svg-loader";

export default defineConfig({
  plugins: [vue(), vueJsx(), svgLoader({ svgoConfig: {} })],
  resolve: {
    alias: [
      {
        find: "@",
        replacement: resolve(__dirname, "../src"),
      },
      {
        find: "assets",
        replacement: resolve(__dirname, "../src/assets"),
      },
      {
        find: "vue-i18n",
        replacement: "vue-i18n/dist/vue-i18n.cjs.js", // Resolve the i18n warning issue
      },
      {
        find: "vue",
        replacement: "vue/dist/vue.esm-bundler.js", // compile template
      },
      {
        find: 'antd/lib',
        replacement: 'antd/es',
      },
      {
        find: "@antv/x6",
        replacement: "@antv/x6/dist/x6.js",
      },
      {
        find: "@antv/x6-vue-shape",
        replacement: "@antv/x6-vue-shape/lib",
      },
    ],
    extensions: [".ts", ".js"],
  },
  define: {
    "process.env": {},
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          hack: `true; @import (reference) "${resolve(
            "src/assets/style/breakpoint.less"
          )}";`,
        },
        javascriptEnabled: true,
      },
    },
  },
});
