/**
 * 触点分类接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findSilenceRulePage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/silence-rule`, {
    params: {
      ...params,
      ...query
    }
  });
}

export function findSilenceRuleList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/silence-rule/list`, {
    params
  });
}

export function createSilence(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/silence-rule`;
  return axios.post(uri, info);
}


export function modifySilence(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/silence-rule`;
  return axios.put(uri, info);
}

// export function deleteNode(id: string) {
//   return axios.delete(
//     `/api/ma-manage/${tenantId}/${buCode}/flow-category/${id}`
//   );
// }

// export function getCategory(id: string) {
//   return axios.get(`/api/ma-manage/${tenantId}/${buCode}/flow-category/${id}`);
// }
