<template>
  <div class="Initialize-page">
    <a-steps :current="current">
      <!-- <a-step>系统环境检查</a-step>
      <a-step>分库存储设置</a-step>
      <a-step>OSS存储设置</a-step>
      <a-step>数据仓库设置</a-step>
      <a-step>定时任务设置</a-step>
      <a-step>分析报表设置</a-step>
      <a-step>Flow设置</a-step>
      <a-step>上线 -->
        <a-step>{{t("init.systemEnvironmentCheck")}}</a-step>
        <a-step>{{t("init.shardedStorageConfiguration")}}</a-step>
        <a-step>{{t("init.ossStorageConfiguration")}}</a-step>
        <a-step>{{t("init.dataWarehouseConfiguration")}}</a-step>
        <a-step>{{t("init.scheduledTaskConfiguration")}}</a-step>
        <!-- <a-step>{{t("init.analysisReportConfiguration")}}</a-step> -->
        <a-step>{{t("init.flowConfiguration")}}</a-step>
        <a-step>{{t("init.deployment")}}
        <template v-if="onlineRef?.loading" #icon>
        <icon-loading/>
      </template>
    </a-step>
    </a-steps>
    <a-divider />
    <div class="step-list">
      <div class="step-item">
        <check-dependence v-if="current == 1" ref="dependenceRef" />
        <init-zoned v-if="current == 2" ref="zonedRef" :setting="entity.setting.devices" />
        <init-storage v-if="current == 3" ref="storageRef" :setting="entity.setting.bucketSetting" />
        <init-customer-model v-if="current == 4" ref="dwhRef" :setting="entity.setting.dataWareHouseSetting" />
        <init-scheduler v-if="current == 5" ref="schedulerRef" :setting="entity.setting.schedulerSetting" />
        <!-- <init-analysis v-if="current == 6" ref="analysisRef" :setting="entity.setting.analysisSetting" /> -->
        <init-flow v-if="current == 6" ref="flowRef" :setting="entity.setting.flowSetting" />
        <init-online v-if="current == 7" ref="onlineRef" :save-entity="saveEntity" :config-pass="onConfigPass" />

      </div>
    </div>
    <a-space class="btn-line" size="large">
      <a-button type="secondary" :disabled="current <= 1" @click="onPrev">
        <IconLeft /> 上一步
      </a-button>
      <a-button v-if="current <= 6" type="primary" @click="onNext">
        下一步
        <IconRight />
      </a-button>
      <a-button v-if="current == 7" type="primary" :disabled="onlineStatus == 'NOT'" @click="online">
        完成
      </a-button>
    </a-space>
  </div>
</template>

<script setup>
// import { ref } from "vue";
import { provide, ref, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import {
  saveMarketingCenter,
  onlineMarketingCenter
} from "@/api/marketing_center";
import { useBussinessUnitStore } from "@/store";
import checkDependence from "./check-dependence.vue";
import InitZoned from "./init-zoned.vue";
import InitStorage from "./init-storage.vue";
import InitCustomerModel from "./init-customer-model.vue";
import InitScheduler from "./init-scheduler.vue";
import InitAnalysis from "./init-analysis.vue";
import InitFlow from "./init-flow.vue";
import InitOnline from "./online.vue";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

const {
      proxy: { t }
    } = getCurrentInstance()
const router = useRouter();

const current = ref(1);
const onlineStatus = ref("NOT");

// Refs
const dependenceRef = ref(null);
const zonedRef = ref(null);
const storageRef = ref(null);
const dwhRef = ref(null);
const schedulerRef = ref(null);
const analysisRef = ref(null);
const flowRef = ref(null);
const onlineRef = ref(null);

const stateRefs = [
  dependenceRef,
  zonedRef,
  storageRef,
  dwhRef,
  schedulerRef,
  analysisRef,
  flowRef
];

const entity = ref({
  id: `${tenantId}_${buCode}`,
  tenantId,
  buCode,
  name: `${buCode}营销中心`,
  status: "CONFIG",
  setting: {
    devices: {
      MongoDB: { zoned: false },
      ElasticSearch: { zoned: false }
    },
    bucketSetting: {},
    dataWareHouseSetting: {
      customerModel: {}
    },
    schedulerSetting: {},
    flowSetting: {},
    analysisSetting: {}
  }
});

const mc = userBussinessUnitStore.marketingCenter;
if (mc?.id != null) {
  entity.value = mc;
}

const onPrev = () => {
  current.value -= 1;
};

const onNext = async () => {
  if (await stateRefs[current.value - 1].value.commit(entity)) {
    current.value += 1;
  }
};

const online = () => {
  onlineMarketingCenter().then(() => {
    router.push({
      path: "/dashboard/workplace"
    });
  });
};

const saveEntity = async () => {
  await saveMarketingCenter(entity.value);
};

const onConfigPass = () => {
  onlineStatus.value = "PASS";
};
</script>

<style lang="less" scoped>
.Initialize-page {
  height: 100%;

  .step-list {
    margin: 10px;
    margin-top: 40px;
    position: relative;
    height: calc(100% - 140px);
    overflow-y: auto;
    display: flex;
    justify-content: center;

    .step-item {
      width: 60%;
    }

    .tree-right {
      position: absolute;
      right: 0;
      top: 70px;
      bottom: 0;
      width: 300px;
      overflow: auto;
      padding: 10px 0 10px 10px;
      background-color: var(--color-fill-2);
    }
  }

  .btn-line {
    width: 100%;
    justify-content: end;
  }
}
</style>
