const config = {
  path: "budget",
  name: "budget",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.budget",
    requiresAuth: true,
    iconFont: "icon-money",
    order: 20,
    parentMenu:true,
  },
  children: [
    {
      path: "budget",
      name: "Budget",
      component: () => import("@/views/ma-budget/budget/main.vue"),
      meta: {
        locale: "menu.budget.main",
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "budget/edit",
      name: "BudgetEdit",
      component: () => import("@/views/ma-budget/budget/edit.vue"),
      meta: {
        locale: "menu.budget.edit",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
  ],
};

// export default config;
