<template>
  <module edit>
    <template #main>
      <div class="audience-edit">
        <a-form ref="formRef" layout="vertical" class="general-form" :model="entity">
          <a-space direction="vertical" :size="16">
            <a-card class="general-card">
              <a-row :gutter="80">
                <a-col :span="12">
                  <a-form-item :label="t('audience.edit.name')" :rules="[{ required: true, message: t('audience.reminder.notEmpty') }]"
                    :validate-trigger="['change', 'input']" field="name">
                    <a-input v-model="entity.name" :disabled="isEdit" :placeholder="t('audience.reminder.input_audience_name')"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item :label="t('audience.edit.testSwitch')" field="usageType">
                    <a-switch v-model="entity.usageType" type="round" checked-value="TEST" unchecked-value="CAMPAIGN">
                      <template #checked> {{t('audience.edit.testAudience')}} </template>
                      <template #unchecked> {{t('audience.edit.realTrigger')}} </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item>
                    <template #label>
                      <div>
                      <span>{{t('audience.edit.estimatedAudience')}}</span>
                      <a-button type="text" size="mini" @click="countCustomers">{{ t('global.button.refresh') }}</a-button>
                    </div>
                    </template>
                    <div class="customer-num">{{ customerNumber }}</div>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :label="t('audience.edit.description')" field="description">
                    <a-textarea v-model="entity.description" :placeholder="t('audience.reminder.input_description')" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :disabled="!entity.own" :content-flex="false">
                    <conditions v-if="showComponents" v-model:condition="entity.filter.filedFilter" :name="t('audience.edit.fieldFiltering')"
                      :data-model="dataModel" class="condition-card" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :disabled="!entity.own" :label="t('audience.edit.tagFiltering')" :content-flex="false">
                    <TagSelect v-if="showComponents" :is-edit="isEdit" :filter="entity.filter.tagFilter" />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-card>
          </a-space>
        </a-form>
      </div>
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance } from "vue";
import { useRoute, useRouter } from "vue-router";
import { findAudienceItem, saveInfo, preCountAudience } from "@/api/audience";
import { Message } from "@arco-design/web-vue";
import Conditions from "@/components/ma/conditions/index.vue";
import TagSelect from "@/components/ma/tag-select/index.vue";
import { findCustomerModel } from "@/api/system";
import { useBussinessUnitStore } from "@/store";

export default {
  components: {
    Conditions,
    TagSelect,
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const store = useBussinessUnitStore();
    const route = useRoute();
    const router = useRouter();

    const showComponents = ref(false);

    let entityId = route.query.id;
    const isEdit = ref(entityId !== undefined);

    const module = ref({
      entityIdField: "id",
      mainPath: "/audience/main",
      breadcrumb: [
        {
          // name: "活动人群",
          name: t('audience.title'),
          path: "/audience/main"
        },
        {
          // name: "编辑条件人群"
          name: t('audience.edit.title')
        }
      ],    });

    const entity = ref({
      name: "",
      selectType: "CONDITION",
      usageType: "CAMPAIGN",
      description: "",
      own: true,
      filter: {
        filedFilter: {empty: true},
        tagFilter: {
          requiredTags: [], // 必需全部包含的标签
          optionalTags: [], // 至少包含一个的标签
          excludedTags: [], // 不能包含的任何标签
        }
      },
      setting: {
        dwhType: store.marketingCenter?.setting?.dataWareHouseSetting?.type,
      },
    });

    // 计算人数
    const customerNumber = ref("--");
    const countCustomers = () => {
      preCountAudience(entity.value).then((res) => {
        customerNumber.value = res;
      });
    };

    // 导入数据
    const dataModel = ref({ fields: [] });
    const bindData = async () => {
      if (isEdit.value) {
        entity.value = await findAudienceItem(entityId);
      }
      dataModel.value = await findCustomerModel();
      showComponents.value = true;
    };

    const formRef = ref(null);

    const quit = () => {
      router.push({ path: module.value.mainPath });
    };

    const save = async () => {
      const res = await formRef.value.validate();
      if (res) {
        return;
      }
      saveInfo(entity.value).then((res) => {
        entityId = res.id;
        isEdit.value = !!entityId;
        bindData();
        // Message.success("提交成功！");
        Message.success(t('audience.reminder.submitSuccess'));
        quit();
      });
    };

    const filterEnableTags = (tags, name) => {
      const selected = ["requiredTags", "optionalTags", "excludedTags"]
        .filter((it) => it !== name) // 过滤掉当前类型
        .flatMap((it) => entity.value.filter[it]); // 得到已选择的标签

      return tags.filter((item) => !selected.includes(item.value));
    };

    const changeTags = (name, tags) => {
      entity.value.filter.tagFilter[name] = tags;
    };

    const setup = {
      t,
      isEdit,
      showComponents,
      module,
      entity,
      bindData,
      save,
      entityId,
      dataModel,
      countCustomers,
      customerNumber,
      formRef,
      changeTags,
      filterEnableTags,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style scoped lang="less">
.audience-edit {
  .customer-num {
    margin: 0 5px;
  }
  :deep(.arco-progress-type-circle) {
    display: none;
  }

  :deep(.arco-upload-icon-start) {
    display: none;
  }
}
</style>
