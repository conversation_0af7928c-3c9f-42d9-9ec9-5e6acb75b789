<template>
  <div class="mapping-template">
    <div class="item-tab title-name">
      <div class="item">{{ $t('reach.sms.templateField') }}</div>
      <div class="item">{{ $t('reach.sms.mappingRelationship') }}</div>
      <div class="item">{{ $t('reach.sms.mappedField') }}</div>
      <div class="item">{{ $t('reach.sms.defaultValue') }}</div>
      <div class="item">{{ $t('reach.sms.fieldLengthLimit') }}</div>
      <a-button v-if="isChange" type="text" @click="addItem">{{ $t('global.button.add') }}</a-button>
    </div>
    <template v-for="(item, index) in templateData" :key="index">
      <div class="item-tab">
        <div class="item">
          <a-input v-model="item.source" :placeholder="$t('reach.sms.templateField')" />
        </div>
        <div class="item">
          <a-select v-model="item.type" :placeholder="$t('reach.sms.mappingType')" @change="changeType(item)">
            <a-option :label="$t('reach.sms.customerField')" value="CUSTOMER" />
            <a-option :label="$t('reach.sms.behaviorField')" value="BEHAVIOR" />
            <a-option :label="$t('reach.sms.fixedValue')" value="CONSTANT" />
            <a-option :label="$t('reach.sms.replacementValue')" value="SYSTEM" />
            <!-- <a-option label="表达式"
                      value="EXPRESSION" /> -->
            <!-- <a-option label="绑定字段" value="BINDING" /> -->
            <a-option :label="$t('reach.sms.flow')" value="FLOW" />
          </a-select>
        </div>
        <div class="item">
          <div v-if="item.type === 'CUSTOMER'" class="m-t-p">
            <a-row>
              <a-col :span="12">
                <a-tree-select v-model="item.value" allow-search :data="customerFields" :field-names="treeFieldStruct"
                  :filter-tree-node="filterTreeNode" :placeholder="$t('reach.reminder.field')"
                  @change="changeField(item, customerFields)" />
              </a-col>
              <a-col v-if="isTime(item)" :span="6">
                <a-select v-model="item.formatPattern" :placeholder="$t('reach.reminder.timeFormat')"
                  :options="timePatterns" />
              </a-col>
              <a-col :span="8">
                <a-input v-model="item.defaultValue" :placeholder="$t('reach.reminder.inputDefaultValue')" />
              </a-col>
              <a-col :span="4">
                <a-input-number v-model="item.lengthLimit" :placeholder="t('reach.reminder.inputLimitSize')" />
              </a-col>
            </a-row>
          </div>
          <div v-else-if="item.type === 'BEHAVIOR'" class="m-t-p">
            <a-row>
              <a-col :span="6">
                <a-select v-model="item.modelId" allow-search :placeholder="t('reach.reminder.behaviorModel')"
                  @change="changeBehaviorModel(item)">
                  <a-option v-for="b in behaviorList" :key="b.name" :value="b.name" :label="b.aliasName" />
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-tree-select v-model="item.value" allow-search :data="item.behaviorFields"
                  :field-names="treeFieldStruct" :placeholder="$t('reach.reminder.field')" @change="changeField(item)"
                  @click="changeBehaviorModel(item)" />
              </a-col>
              <a-col v-if="isTime(item)" :span="6">
                <a-select v-model="item.formatPattern" :placeholder="$t('reach.reminder.timeFormat')"
                  :options="timePatterns" />
              </a-col>
              <a-col :span="8">
                <a-input v-model="item.defaultValue" :placeholder="$t('reach.reminder.inputDefaultValue')" />
              </a-col>
              <a-col :span="4">
                <a-input-number v-model="item.lengthLimit" :placeholder="t('reach.reminder.inputLimitSize')" />
              </a-col>
            </a-row>
          </div>
          <div v-else-if="item.type === 'CONSTANT'" class="m-t-p">
            <a-input v-model="item.value" :placeholder="$t('reach.reminder.inputFixedValue')" />
          </div>
          <div v-else-if="item.type === 'FLOW'" class="m-t-p">
            <a-row>
              <a-col :span="12">
                <a-input v-model="item.flowId" :placeholder="$t('reach.reminder.inputProcessId')" />
              </a-col>
              <a-col :span="12">
                <a-input v-model="item.flowStartId" :placeholder="$t('reach.reminder.inputProcessStartId')" />
              </a-col>
            </a-row>
          </div>
          <div v-else-if="item.type === 'SYSTEM'" class="m-t-p">
            <a-select v-model="item.value" :placeholder="$t('reach.reminder.selectReplacementValue')">
              <a-option :label="$t('reach.sms.currentTime')" value="CURRENT_TIME" />
              <a-option :label="$t('reach.sms.currentDate')" value="CURRENT_DATE" />
            </a-select>
          </div>
          <div v-else-if="item.type === 'BINDING'" class="m-t-p">
            <a-input disabled :placeholder="$t('reach.reminder.bindingFieldNotRequired')" />
          </div>
          <div v-else-if="item.type === 'EXPRESSION'" class="m-t-p">
            <a-input v-model="item.value" :placeholder="$t('reach.reminder.inputExpression')" />
          </div>
          <!-- <div v-else-if="item.type === 'FLOW'" class="m-t-p">
            <a-row>
              <a-col :span="4">
                <a-input v-model="item.prefix" placeholder="请输入前缀" />
              </a-col>
              <a-col :span="8">
                <a-select v-model="item.modelId" placeholder="选择FLOW流程" :loading="loading"
                  @search="handleSearchFlow" @change="changeFlowModel(item)">
                  <a-option v-for="b in flowList" :key="b.id" :value="b.id" :label="b.name" />
                </a-select>
              </a-col>
              <a-col :span="12">
                <a-tree-select v-model="item.value" allow-search :data="item.flowFields" :field-names="fieldStruct"
                  :loading="loading" placeholder="请选择字段" />
              </a-col>
            </a-row>
          </div> -->
        </div>
        <a-button v-if="isChange" type="text" @click="delItem(index)">{{ $t('global.button.delete') }}</a-button>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, getCurrentInstance } from "vue";
import { treeFieldStruct, timePatterns } from "@/constant/common"
import { findBehaviorModelList, getBehaviorModelByName } from "@/api/system";
import { findFlowList, findFlowEndModel } from "@/api/campaign";
import { formatFields, getFieldByPath } from "@/utils/field";

const {
  proxy: { t }
} = getCurrentInstance();

const emit = defineEmits(["update:dataList"]);

const props = defineProps({
  isChange: {
    type: Boolean,
    default: true
  },
  dataList: Array,
  content: String,
  customerModel: Object,
});
const loading = ref(false);

const customerFields = ref(props.customerModel?.fields);
const behaviorList = ref([]);
// 模板内容解析
const templateData = computed(() => { return props.dataList || [] });

const flowList = ref([]);

// watch(() => props.content, (newVal) => {
//   if (newVal && templateData.value.length === 0) {
//     newVal.replace(/\${.*?}/g, function (str) {
//       const key = str.match(/\${(\S*)}/)[1];
//       templateData.value.push({
//         source: key
//       })
//     });
//   }
// }, { immediate: true })

/**
 * 加载页面时，加载当前实体引用的行为模型
 */
const loadBehaviorModels = async () => {
  const ids = Array.from(new Set(props.dataList.filter(it => it.type === "BEHAVIOR").map(it => it.modelId))).filter(it => it != null);
  if (ids.length === 0) {
    return;
  }
  const models = await findBehaviorModelList({ expression: `id in ${Array.from(ids).join(",")}` });
  for (let i = 0; i < templateData.value.length; i += 1) {
    if (templateData.value[i].type === "BEHAVIOR") {
      const model = models.find(it => it.id === templateData.value[i].modelId);
      templateData.value[i].behaviorFields = formatFields(model?.fields);
    }
  }
}

const addItem = () => {
  templateData.value.push({});
};

const delItem = (index) => {
  templateData.value.splice(index, 1);
};

const changeBehaviorModel = async (item) => {
  const model = await getBehaviorModelByName(item.modelId);
  item.behaviorFields = formatFields(model.fields);
};
const filterTreeNode = (searchValue, nodeData) => {
  if (searchValue) {
    return nodeData.label.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  }
};

const handleSearchFlow = async (name) => {
  loading.value = true;
  const params = { fields: "name" };
  params.expression = "status eq ENABLED";
  if (name) {
    params.expression += `AND name like ${name}`;
  }
  flowList.value = await findFlowList(params);
  loading.value = false;
};

const handleSearchFlowModel = async () => {
  const ids = Array.from(new Set(props.dataList.filter(it => it.type === "FLOW").map(it => it.modelId))).filter(it => it != null);
  if (ids.length === 0) {
    return;
  }
  const models = [];
  for (let i = 0; i < ids.length; i += 1) {
    const model = await findFlowEndModel(ids[i]);
    models.push({ id: ids[i], fields: model[0].model.fields });
  }

  for (let i = 0; i < templateData.value.length; i += 1) {
    if (templateData.value[i].type === "FLOW") {
      const model = models.find(it => it.id === templateData.value[i].modelId);
      templateData.value[i].flowFields = formatFields(model.fields);
    }
  }
};

const changeFlowModel = async (item) => {
  const flowEnd = await findFlowEndModel(item.modelId);
  if (flowEnd.length !== 1) {
    return;
  }
  item.flowFields = formatFields(flowEnd[0].model.fields);
};

// 重选类型
const changeType = (item) => {
  item.modelId = null;
  item.value = null;
  if (item.type == 'CONSTANT' || item.type == 'SYSTEM' || item.type == 'FLOW') {
    item.valueType = 'string';
  } else {
    item.valueType = null;
  }
};

const changeField = (item, fields) => {
  const field = getFieldByPath(fields, item.value)
  item.valueType = field.type;
  item.formatPattern = null;
};
const isTime = (item) => {
  return item.valueType === "date";
};

const bindData = async () => {

  findBehaviorModelList({ fields: "name,aliasName" }).then((data) => {
    if (data) {
      behaviorList.value = data;
    }
  });
  loadBehaviorModels();
  handleSearchFlow();
  handleSearchFlowModel();
};

onMounted(async () => {
  await bindData();
});

watch(props.dataList, (data) => {
  templateData.value = data;
});

watch(templateData.value, (data) => {
  emit("update:dataList", data);
}
);
</script>

<style lang="less" scoped>
.mapping-template {
  width: 100%;

  .item-tab {
    display: flex;
    align-items: center;

    .item {
      flex: 1;
      height: 32px;
      display: flex;
      align-items: center;
      margin: 2px 0 0;

      &:nth-child(1) {
        max-width: 160px;
      }

      &:nth-child(2) {
        max-width: 120px;
        margin-left: 2px;
        margin-right: 2px;
      }
    }

    .gay {
      background-color: #efefef;
      padding: 10px;
    }
  }

  .title-name {
    background-color: #efefef;

    .item {
      padding: 5px 10px;
      margin: 5px 0;
    }
  }
}

.data-null {
  width: 100%;
  text-align: center;
  padding: 10px;
  background-color: var(--color-fill-2);
}

.behavior-list {
  display: flex;
  align-items: center;
  width: 100%;

  .behavior-item {
    flex: 1;
    width: 100%;
  }
}

.m-t-p {
  position: relative;
  width: 100%;
}

.item-select {
  width: 100%;
  display: flex;
  align-items: center;

  .m-t-p {
    flex: 1;

    &:nth-child(1) {
      margin-right: 2px;
    }
  }
}

.mapping-template-select {
  position: relative;
}
</style>
