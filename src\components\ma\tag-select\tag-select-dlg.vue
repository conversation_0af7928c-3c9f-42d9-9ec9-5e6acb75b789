<template>
  <a-modal v-model:visible="modelVisible" width="auto" @ok="handleOk" @before-cancel="handleCancel">
    <template #title> {{t('audience.popup.select_title')}} </template>
    <a-transfer v-if="modelVisible" v-model="dataRight" class="transfer" :data="dataLeft" :title="[t('audience.popup.optionalLabel'), t('audience.popup.selectedLabel')]"
      :default-value="dataRight" show-search value="name" label="id" />
  </a-modal>
</template>

<script setup>
import { ref, inject, getCurrentInstance } from "vue";
import { findTagList } from "@/api/audience";

const {
      proxy: { t }
    } = getCurrentInstance()
const editInject = inject("edit");

let callback;
const dataLeft = ref([]);
const dataRight = ref([]);

const modelVisible = ref(false);

// 确定选择
const handleOk = () => {
  const result = dataLeft.value.filter((item) =>
    dataRight.value.includes(item.value)
  );
  callback(result);
  modelVisible.value = true;
};
// 取消选择
const handleCancel = async () => {
  callback();
};

const show = async (tagType, data) => {
  findTagList({}).then((res) => {
    dataLeft.value = res.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    // 过滤已选中的
    dataLeft.value = editInject.filterEnableTags(dataLeft.value, tagType);
  });
  dataRight.value = data;
  modelVisible.value = true;
  return new Promise((resolve, reject) => {
    callback = resolve;
  });
};

defineExpose({ show });
</script>

<style lang="less" scoped>
.transfer {
  .arco-transfer-view {
    height: 300px;
  }
}
</style>
