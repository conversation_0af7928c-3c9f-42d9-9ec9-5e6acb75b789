<template>
  <div class="container system-setting">
    <a-card v-for="systemSetting in systemSettings"
            :key="systemSetting.name"
            class="setting-cards"
            :bordered="false"
            :title="systemSetting.name">
      <a-space style="flex-wrap: wrap;">
        <a-button v-for="menu in systemSetting.children"
                  :key="menu.view"
                  v-permission="menu.roles"
                  class="setting-card"
                  hoverable
                  @click="gotoSetting(menu.view, menu.query)">
          {{ menu.name }}
        </a-button>
      </a-space>
    </a-card>
    <!-- chat -->
    <a-button type="primary"
              class="chat-btn"
              @click="visible = true">
      <template #icon>
        <icon-message />
      </template>
    </a-button>

    <a-drawer v-if="visible"
              v-model:visible="visible"
              width="1200px"
              title="lwCaht"
              :footer="false"
              @cancel="visible = false">
      <iframe src="https://saas.lianwei.com.cn/lm/"
              frameborder="0"
              class="chat-iframe"></iframe>
    </a-drawer>
  </div>

</template>

<script>
// import { ref } from "vue";
import {provide, ref, getCurrentInstance} from 'vue'
import {useRouter} from 'vue-router'

export default {
  setup() {
    const {
      proxy: {t}
    } = getCurrentInstance()
    const router = useRouter()

    const systemSettings = ref([
      {
        name: t('systemSetting.title.basicSettings'),
        // name: t('menu.title.basicSettings'),
        children: [
          // {
          //   name: "分组管理",
          //   view: "group-activity",
          // },
          {
            name: t('systemSetting.basic.customerModel'),
            view: 'customer-model',
            // roles: ['ma_menu.system.customer-model'],
          },
          {
            name: t('systemSetting.basic.behaviorModel'),
            view: 'behavior-model',
            roles: ['ma_menu.system.behavior-model']
          },
          {
            name: t('systemSetting.basic.behaviorEvent'),
            view: 'behavior-event',
            roles: ['ma_menu.system.behavior-event']
          },
          // {
          //   name: "客户标签",
          //   view: "customer-tag",
          // },

          {
            name: t('systemSetting.basic.activityCategory'),
            view: 'campaign-category',
            roles: ['ma_menu.system.campaign-category']
          },
          {
            name: t('systemSetting.basic.channelLimitation'),
            view: 'channel-limited',
            roles: ['ma_menu.system.channel-limited']
          },
          {
            name: t('systemSetting.basic.marketingCenterConfig'),
            view: 'center',
            roles: ['ma_menu.system.center']
          }
        ]
      },
      // {
      //   name: "财务设置",
      //   children: [
      //     {
      //       name: "财年营销预算",
      //       view: "system-budget",
      //     },
      //   ],
      // },
      {
        name: t('systemSetting.title.touchpointManagement'),
        children: [
          {
            name: t('systemSetting.touchpoint.communicationChannel'),
            view: 'reach',
            roles: ['ma_menu.system.communicate-channel']
          },
          {
            name: t('systemSetting.touchpoint.benefitChannel'),
            view: 'benefit',
            roles: ['ma_menu.system.benefit-channel']
          },
          {
            name: t('systemSetting.touchpoint.canvasNodeManagement'), // 原节点管理
            view: 'node-config',
            roles: ['ma_menu.system.node-config']
          },
          {
            name: t('systemSetting.touchpoint.touchpointCategoryManagement'),
            view: 'node-category',
            roles: ['ma_menu.system.flow-category']
          },
          {
            name: t('systemSetting.touchpoint.silenceRule'),
            view: 'silence-rule'
            // roles: ["*"/* "ma_menu.system.silence-rule" */]
          }
        ]
      }
      // {
      //   name: "数据分析",
      //   children: [
      // {
      //   name: "人群诊断",
      //   view: "audience",
      // },
      // {
      //   name: "RFM分析",
      //   view: "rfm",
      // },
      // {
      //   name: "AARRR分析",
      //   view: "aarrr",
      // },
      // {
      //   name: "分析指标",
      //   view: "metric",
      // },
      //   ],
      // },
      // {
      //   name: "系统设置",
      //   children: [
      // {
      //   name: "csv设置",
      //   view: "csvSetting",
      // },
      //   ],
      // },
    ])
    const gotoSetting = (id, query) => {
      router.push({path: id, query})
    }

    const visible = ref(false)
    const gotoCaht = () => {
      router.push({path: '/system/chat'})
    }
    const gotoCaht2 = () => {
      window.open('http://*************:3000/', '_blank')
    }

    return {
      router,
      systemSettings,
      visible,
      gotoSetting,
      gotoCaht,
      gotoCaht2
    }
  }
}
</script>

<style scoped lang="less">
.setting-cards {
  width: 100%;
  margin-bottom: 10px;
  .setting-card {
    font-size: medium;
    margin-bottom: 20px;
    padding: 20px 40px;
    cursor: pointer;
  }
}
.container {
  padding: 20px 20px 20px 20px;

  :deep(.arco-list-content) {
    overflow-x: hidden;
  }

  :deep(.arco-card-meta-title) {
    font-size: 14px;
  }
}
.system-setting {
  :deep(.arco-card-header) {
    border: 0;
    .arco-card-header-title {
      font-weight: bold;
    }
  }
}
:deep(.arco-list-col) {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

:deep(.arco-list-item) {
  width: 33%;
}

:deep(.block-title) {
  margin: 0 0 12px 0;
  font-size: 14px;
}

:deep(.list-wrap) {
  // min-height: 140px;
  .list-row {
    align-items: stretch;

    .list-col {
      margin-bottom: 16px;
    }
  }

  :deep(.arco-space) {
    width: 100%;

    .arco-space-item {
      &:last-child {
        flex: 1;
      }
    }
  }
}
.chat-btn {
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
}
.chat-btn2 {
  position: absolute;
  right: 20px;
  bottom: 60px;
  z-index: 1000;
}
.chat-drawer {
  padding: 0;
}
.chat-iframe {
  width: 100%;
  height: calc(100% - 10px);
}
</style>
