<template>
  <a-card :title="t('analysis.statementAnalysis')" style="margin: 20px">
    <a-space direction="vertical" fill>
      <a-card :title="t('analysis.systemReport')">
        <a-space>
          <!-- <a-card class="item-tag" @click="toPage('analysis-rfm', {id: 'perform_distribution_24',title: '投放吞吐量',})">投放吞吐量</a-card> -->
          <a-card v-permission="['ma_menu.analysis.report1']" class="item-tag" @click="toPage('throughput')">{{t('analysis.throughputAnalysisReport')}}</a-card>
          <!--<a-card class="item-tag">用户活动分析</a-card>-->
          <!--<a-card class="item-tag">用户行为监控</a-card>-->
          <!-- <a-card class="item-tag" @click="toPage('analysis-rfm', {id: 'result_of_execution_24',title: '投放状态分析',})">投放状态分析</a-card> -->
          <!-- <a-card class="item-tag" @click="toPage('analysis-rfm', {id: 'perform_type_24',title: '投放渠道分析',})">投放渠道分析</a-card> -->
        </a-space>
      </a-card>

      <!--<a-card title="活动分析">-->
      <!--<a-space>-->
      <!--<a-card class="item-tag">漏斗分析</a-card>-->
      <!--<a-card class="item-tag">事件分析</a-card>-->
      <!--<a-card class="item-tag">留存分析</a-card>-->
      <!--<a-card class="item-tag">活动效果分析</a-card>-->
      <!--</a-space>-->
      <!--</a-card>-->

      <!-- <a-card title="用户分析">
        <a-space>
          <a-card class="item-tag" @click="
            toPage('analysis-rfm', {
              title: 'RFM分析',
              type: 'rfm',
            })
            ">RFM分析</a-card>
          <a-card class="item-tag">AIPL分析</a-card>
          <a-card class="item-tag" @click="
            toPage('analysis-rfm', {
              id: 'ma_analysis',
              type: 'one',
              title: 'AARRR分析',
            })
            ">AARRR分析</a-card>
          <a-card class="item-tag">流失预警</a-card>
          <a-card class="item-tag">高价值客户分析</a-card>
        </a-space>
      </a-card> -->
    </a-space>
  </a-card>
</template>

<script>
import { ref, provide, getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";

export default {
  components: {},
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const route = useRoute();
    const router = useRouter();

    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: "分析报表",
          // name: t('analysis.statementAnalysis'),
        },
      ],
      mainPath: "/analysis/analysis",
      editPath: "/audience/audience/edit",
      createPath: "/audience/audience/edit",
    });

    // 切换数据
    const toPage = (name, query = {}) => {
      router.push({ path: name, query });
    };

    const setup = {
      module,
      toPage,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.item-tag {
  cursor: pointer;
}
</style>
