/**
  数据监控，重试
 */
<template>
  <a-modal v-model:visible="visible" v-model:selectedKeys="selectedKeys" @before-ok="handleOk">
    <a-table
            ref="table"
            :bordered="false"
            :data="dataSource"
            :pagination="false">
          <template #columns>
            <a-table-column title="客户标示" data-index="name"/>
            <a-table-column title="状态" data-index="planned"/>
            <a-table-column title="描述" data-index="spent"/>
          </template>
    </a-table>

  </a-modal>
</template>

<script setup>
import { ref } from 'vue'

const visible = ref(false)
const dataSource = ref([])
const selectedKeys = ref([])
defineExpose({});
</script>
<style  lang="less" scoped>

</style>
