import axios from "axios";
import { UserState } from "@/store/modules/user/types";
import { QueryInfo } from "@/types/api";

export interface LoginData {
  username: string;
  password: string;
}

export interface LoginRes {
  token: string;
}
export function login(data: LoginData) {
  return axios.post<LoginRes>(
    "/api/platform-tenant/authentication/dev_user",
    data
  );
}

export function logout() {
  return axios.post<LoginRes>("/api/platform-tenant/authentication/logout");
}

export function encryption() {
  const uri = `/api/platform-tenant/authentication/transport_encrypt_setting`
  return axios.get(uri)
}

export function getUserInfo() {
  return axios.get<UserState>("/api/platform-tenant/authentication/status");
}

export function getBussinessUnit() {
  return axios.get(
    "/api/platform-tenant/tenant_business_unit/current_user_business_unit"
  );
}

export function getMarketingCenter(tenantId: any, buCode: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/view`;
  return axios.get(uri);
}

export function findCustomerModel(tenantId: any, buCode: any, path?: any) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}${path}`);
}

export function setCustomerList(tenantId: any, buCode: any, info?: any) {
  return axios.put(`/api/ma-manage/${tenantId}/${buCode}/customer-model`, info);
}

export function getCdpCollectionList(tenantId: any, buCode: any) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/data_ware_house/list`);
}

export function getCdpCollectionModel(
  tenantId: any,
  buCode: any,
  collectionId: any
) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/data_ware_house_table/${collectionId}/list`
  );
}

export function getDefaultCustomerModel(tenantId: any, buCode: any) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/customer-model/default-model`
  );
}

export function saveMarketingCenter(tenantId: any, buCode: any, data: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/zone`;
  return axios.put(uri, data);
}

export function setnitializeBi(tenantId: any, buCode: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/bi_operate/initialized`;
  return axios.get(uri);
}

export function setnitializeLoyalty(tenantId: any, buCode: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/loyalty_operate/initialized`;
  return axios.get(uri);
}

export function getUserList(tenantId: any, buCode: any, query?: QueryInfo) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/platform_user`, {
    params: {
      ...query,
    },
  });
}

export function getCurrentUserDataRole(query?: QueryInfo) {
  return axios.get(
    "/api/platform-tenant/tenant_data_role/current_user_data_role",
    {
      params: {
        ...query,
      },
    }
  );
}

export function getTenantDataRole(query?: QueryInfo) {
  return axios.get("/api/platform-tenant/tenant_data_role", {
    params: {
      ...query,
    },
  });
}
