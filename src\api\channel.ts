import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findPage(query?: QueryInfo, params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/marketing_channel`,
    {
      params: {
        ...params,
        ...query,
      },
    }
  );
}

export function findItem(id: string) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/marketing_channel/${id}`
  );
}

export function saveInfo(info?: any) {
  return info.id
    ? axios.put(`/api/ma-manage/${tenantId}/${buCode}/marketing_channel`, info)
    : axios.post(
        `/api/ma-manage/${tenantId}/${buCode}/marketing_channel`,
        info
      );
}

export function deleteItem(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/marketing_channel/${id}`
  );
}

export function findScrmAccountList(params?: Params) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/marketing_channel/scrm/account_list`,
    { params }
  );
}

export function saveSetnitialize(data: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/business_unit_setting`;
  return axios.put(uri, data);
}

export function findGlobalLimitSetting() {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/business_unit_setting`
  );
}
