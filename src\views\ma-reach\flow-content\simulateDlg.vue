<template>
  <a-modal v-model:visible="visible" title="模拟发送" @cancel="handleCancel" @ok="handleOk">
    <a-form :model="form">
      <div class="tag-name">用户查询</div>
      <a-tree-select v-model="form.customerField" style="margin-bottom: 10px" :data="userIdData.fields" allow-clear
        :field-names="{ key: 'path', value: 'path', title: 'name', children: 'fields' }" placeholder="请选择用户字段">
      </a-tree-select>
      <a-input-search v-model="form.value" style="margin-bottom: 10px" placeholder="请输入用户字段值" button-text="搜索"
        :loading="loadingData" search-button @search="searchUser" />
      <div v-if="customerList?.length > 0" class="user-list">
        <div class="user-title">查询结果</div>
        <a-space direction="vertical" size="large">
          <a-radio-group v-model="form.customerId">
            <template v-for="item in customerList" :key="item.value">
              <a-radio :value="item.value">
                <a-tag>{{ item.label }}</a-tag>
              </a-radio>
            </template>
          </a-radio-group>
        </a-space>
      </div>
      <div v-if="customerList?.length === 0" class="user-list">
        <a-empty description="暂无对应信息的用户" />
      </div>

      <template v-for="(item, key, i) in form.payload" v-if="customerList?.length > 0">
        <div class="tag-name">映射字段值</div>
        <template v-for="(cItem, cKey, cI) in item" v-if="key !== 'BINDING'">
          <a-input v-model="form.payload[key][cKey]"
            :placeholder="`请输入${key === data.setting.bindName ? '绑定' : '行为'}字段[${cKey}]值`" style="margin-bottom: 10px"
            allow-clear />
        </template>
      </template>

    </a-form>
  </a-modal>
</template>

<script>
import { reactive, ref, defineComponent, watch } from 'vue';
import { findCustomerPage } from "@/api/audience";
import { simulateCommunicateSend } from "@/api/communicate";
import { findBehaviorModelList } from "@/api/system";
import { Message } from "@arco-design/web-vue";

export default defineComponent({
  name: "SimulateDlg",
  props: {
    mappings: {
      type: [Array, Object],
      default: () => {
        return null
      }
    },
    customerModel: Object,
    entity: Object
  },
  setup(props) {
    const visible = ref(false);
    const form = reactive({
      value: '',
      payload: {},
      customerField: ''
    });

    const data = ref({
      customer: null,
      commId: null,
      payload: {},
      setting: {
        sendMessage: true,
        limited: false,
        bindName: '_ma_binding_model_'
      },
      budgetSetting: {
        enabled: false
      },
    })

    const commId = ref('')

    const handleClick = (id) => {
      commId.value = id
      visible.value = true;
    };

    // 取值payload
    watch(() => props.mappings, (count, prevCount) => {
      const str = {}
      count.forEach(item => {
        if (item.mappingType === 'BEHAVIOR') {
          str[item.modelId] = {
            [item.targetValue]: ''
          }
        }
        if (item.mappingType === 'BINDING') {
          str[data.value.setting.bindName] = {
            ...str[data.value.setting.bindName],
            [item.sourceField]: ''
          }
        }
      })
      form.payload = str
    }, { deep: true })

    const handleOk = async () => {
      data.value.customer = form.customerId
      data.value.commId = commId.value
      data.value.simulate = true
      data.value.payload = form.payload
      if (!form.customerId) {
        Message.warning("请选择用户");
        return false
      }
      await simulateCommunicateSend(data.value)
      visible.value = false;
      Message.success("发送成功！");
    };
    const handleCancel = () => {
      visible.value = false;
    }

    const userIdData = ref(props.customerModel);

    // 搜索用户
    const customerList = ref(null)
    const loadingData = ref(false)
    const searchUser = async () => {
      customerList.value = null
      if (!form.value) {
        Message.warning("请输入字段值");
        return false
      }
      if (!form.customerField) {
        Message.warning("请选择用户字段");
        return false
      }
      loadingData.value = true
      try {
        const list = await findCustomerPage({
          expression: `${form.customerField} eq ${form.value}`
        })
        loadingData.value = false
        const fieldName = userIdData.value.pkName || ''

        customerList.value = []

        list.content.forEach(item => {
          customerList.value.push({
            label: item.payload[fieldName],
            value: item.id,
          })
        });
      } catch (err) {
        loadingData.value = false
      }
    }

    // 获取数据源数据
    const initBehavior = (data, parent) => {
      if (!Array.isArray(data)) return;
      data.forEach((i) => {
        if (parent) {
          i.path = `${parent.id}.${i.name}`;
          i.viewName = `${parent.viewName}.${i.name}`;
        } else {
          i.path = i.id;
          i.viewName = i.name;
        }
        i.name = `${i.viewName}      「${i.aliasName}」`

        if (i.fields && i.fields.length > 0) {
          return initBehavior(i.fields, i);
        }
      });
    };
    const behaviorData = ref([])
    findBehaviorModelList().then((data) => {
      if (data) {
        behaviorData.value = data;
        initBehavior(behaviorData.value, "");
      }
    });

    // 取值拆分
    const changeModelVlaue = (val, item) => {
      item.value = ''
      if (val) {
        const data = val.split('.')
        item.modelId = data[0]
        item.targetValue = data[1] || ''
      } else {
        item.modelId = ''
        item.targetValue = ''
      }
    }

    const changType = (val, item) => {
      item.modelId = ''
      item.targetValue = ''
      item.value = ''
    }

    return {
      visible,
      form,
      customerList,
      commId,
      data,
      handleClick,
      handleOk,
      handleCancel,
      userIdData,
      loadingData,
      initBehavior,
      behaviorData,
      searchUser,
      changeModelVlaue,
      changType
    }
  },
})
</script>

<style lang="less" scoped>
.user-list {
  .user-title {
    font-size: 12px;
    color: #666666;
    margin-bottom: 10px;
  }
}

.tag-name {
  color: #333333;
  font-size: 14px;
  padding: 0 5px;
  line-height: 20px;
  border-left: 3px solid rgb(var(--primary-6));
  margin-bottom: 10px;
}

:deep(.arco-radio) {
  margin-bottom: 10px;
}
</style>
