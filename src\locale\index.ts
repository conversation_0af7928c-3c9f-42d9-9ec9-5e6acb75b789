import { createI18n } from "vue-i18n";
import en from "./en-US";
import cn from "./zh-CN";

export const LOCALE_OPTIONS = [
  { label: "中文", value: "zh-cn" },
  { label: "English", value: "en-us" },
];
let defaultLocale = "zh-cn";
// let defaultLocale = "en-us";
const lang = localStorage.getItem("APP_LANG");
if (lang) {
  defaultLocale = JSON.parse(lang);
}

const i18n = createI18n({
  locale: defaultLocale,
  fallbackLocale: "en-us",
  allowComposition: true,
  messages: {
    "en-us": en,
    "zh-cn": cn,
  },
});

export default i18n;
