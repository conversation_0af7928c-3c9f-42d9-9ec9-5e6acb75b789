/** 预算条目添加对话框 */
<template>
  <a-form :model="entity">
    <a-form-item label="名称" field="entity.name">
      <a-input v-model="entity.name" placeholder="请输入条目名称" />
    </a-form-item>
    <a-form-item label="类目" field="entity.category">
      <a-select v-model="entity.type" placeholder="请选择审批结果">
        <a-option key="1" value="APPROVE">通过</a-option>
        <a-option key="1" value="REJECT">拒绝</a-option>
      </a-select>
    </a-form-item>
    <a-form-item label="计划金额" field="entity.estimate">
      <a-input v-model="entity.name" placeholder="请输入条目计划金额" />
    </a-form-item>
    <a-form-item label="保留金额" field="entity.name">
      <a-input v-model="entity.name" placeholder="请输入条目保留金额" />
    </a-form-item>
    <a-form-item label="备注" field="entity.summary">
      <a-textarea
        v-model="entity.note"
        placeholder="请输入条目备注"
        allow-clear
      />
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from "vue";

const entity = ref({});
defineExpose({});
</script>

<style lang="less" scoped></style>
