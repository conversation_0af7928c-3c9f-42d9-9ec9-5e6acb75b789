<template>
  <module edit>
    <template #action>
      <a-popconfirm :content="synchronousText" position="br" @ok="synchronous">
        <a-button v-permission="['ma_menu.system.customer-model.sync']" :loading="syncing" type="primary">{{t('global.button.sync')}}</a-button>
      </a-popconfirm>
      <!-- <a-button type="primary" @click="save">保存</a-button> -->

    </template>
    <template #main>
      <a-row class="grid-demo" :gutter="20">
        <a-col :span="24">
          <a-card class="general-card" style="height: calc(100vh - 160px)" :title="t('systemSetting.basicSettings.customerFieldSetting')" :bordered="false">
            <CustomerModelConfig :data-model="entity" :is-remove="false" :is-behavior="false" />
          </a-card>
        </a-col>
      </a-row>
    </template>
  </module>
</template>

<script>
import { ref, provide,getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import {
  findCustomerModel,
  saveCustomerModel,
  syncCustomerModel,
} from "@/api/system";
import { Notification } from "@arco-design/web-vue";
import { useUserStore } from "@/store";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const module = ref({
      mainPath: "/system/setting",
      disabledSave: false,
      breadcrumb: [
        {
          name: t('menu.system'),
          path: "/system/setting",
        },
        {
          name: t('systemSetting.basic.customerModel'),
          path: "/system/customer-model",
        },
      ],
    });
    const syncing = ref(false);
    const userStore = useUserStore();
    const { userAuthInfo } = userStore;
    const router = useRouter();
    const entity = ref({
      fields: [],
      identifyFieldMap: {},
    });

    const initFields = (data, parent) => {
      if (!Array.isArray(data)) return;
      data.forEach((i) => {
        if (parent) {
          i.path = `${parent.path}__${i.name}`;
          i.idf = `${parent.idf}.${i.name}`;
        } else {
          i.path = i.name;
          i.idf = i.name;
        }
        if (entity.value.pkName === i.name) {
          i.isKey = true;
        }

        i.identifyPerfix = i.options?.identifyPerfix || "";

        if (i.fields && i.fields.length > 0) {
          return initFields(i.fields, i);
        }
      });
    };

    const bindData = async () => {
      findCustomerModel().then((data) => {
        if (data) {
          entity.value = data;
          initFields(entity.value.fields, "");
        }
      });
    };

    const synchronousText = ref(t('systemSetting.basicSettings.synchronousText'));
    const saveText = ref(t('systemSetting.basicSettings.saveText'));

    const quit = () => {
      router.push({ path: "/system/setting" });
    };

    // 同步按钮
    const synchronous = () => {
      syncing.value = true;
      // 调用同步
      const permissionList = userAuthInfo.menus;
      if (permissionList.includes('ma_menu.system.customer-model.sync')) {
        syncCustomerModel().then(() => {
        Notification.success({
            title: t('global.tips.success.sync'),
          });
          quit();
        });
      } else {
        Notification.error({
          title: t('global.tips.error.permission'),
          content: 'No Permission!',
        });
      }
      syncing.value = false;
    };

    // 数组降维
    const threeToOne = (entityData) => {
      return [].concat(
        ...entityData.map((item) => {
          if (item.fields) {
            const arr = [].concat(item, ...threeToOne(item.fields));
            delete item.fields;
            return arr;
          }
          return [].concat(item);
        })
      );
    };

    // 为空验证
    const isNull = (entityData) => {
      const data = threeToOne(entityData);
      return data.find((item) => {
        return item.name === "" || item.aliasName === "" || item.type === "";
      });
    };

    const save = async () => {
      const item = await isNull(
        JSON.parse(JSON.stringify(entity.value.fields))
      );
      if (item) {
        Notification.warning({
          title: t('systemSetting.basicSettings.someFieldIsNull'),
        });
        return false;
      }
      await saveCustomerModel(entity.value);
      Notification.success({
        title: t('global.tips.success.save'),
      });
      quit();
    };

    const setup = {
      t,
      module,
      router,
      syncing,
      synchronousText,
      synchronous,
      saveText,
      entity,
      quit,
      save,
      bindData
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style scoped lang="less"></style>
