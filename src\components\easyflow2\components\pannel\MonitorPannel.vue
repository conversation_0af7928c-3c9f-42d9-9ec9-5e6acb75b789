/** 节点监控界面 显示节点接收，处理和出错数据统计
显示节点数据表格，并提供状态筛选 支持筛选数据导出 */
<template>
  <a-drawer :mask="true" :width="pannelWidth" :footer="false" :mask-closable="true" :visible="show" class="monitor-pannel"
    popup-container="#easyflow-content" @cancel="handleCancel">
    <template #title>
      <div v-if="node" class="title-name theme-color">
        <span class="node-icon iconfont" :class="iconClass" />
        <span class="panel-title">
          <span>{{ node?.data?._name }}</span>
        </span>
      </div>
      <div>
        <span class="arco-icon-hover">
          <a-popover title="节点说明" trigger="click" position="br">
            <icon-question-circle />
            <template #content>
              <div ref="helpRef" class="help">
                <component :is="help" :key="id" type="monitor"/>
              </div>
            </template>
          </a-popover>
        </span>
      </div>
    </template>
    <div v-if="show">
      <a-space>
        <a-statistic class="monitor-card" :value="incoming" show-group-separator>
          <template #title>
            <span class="title">接收</span>
          </template>
        </a-statistic>
        <a-statistic class="monitor-card" :value="outgoing" show-group-separator>
          <template #title>
            <span class="title">成功</span>
          </template>
        </a-statistic>
        <a-statistic class="monitor-card" :value="error" show-group-separator>
          <template #title>
            <span class="title">失败</span>
          </template>
        </a-statistic>
      </a-space>

      <div class="btn-line">
        <!-- <a-button type="text" @click="showCompareDlg">人群对比</a-button> -->
        <a-button v-if="haveAuth('export_task_record')"  v-permission="['ma_menu.campaign.monitor-export']" type="text" @click="exportNodePayloadId('export_task_record')">导出</a-button>
        <a-button v-if="haveAuth('export_task_reach_record')" v-permission="['ma_menu.campaign.monitor-export']" type="text" @click="exportNodePayloadId('export_task_reach_record')">导出</a-button>
        <a-button v-if="haveAuth('export_task_chain_record')" v-permission="['ma_menu.campaign.monitor-export']" type="text" @click="exportNodePayloadId('export_task_chain_record')">导出链路触达数据</a-button>
        <!-- <a-button v-if="flow.status == 'RUNNING'" type="text" @click="retryExecute">全部重试</a-button> -->
      </div>
      <a-table ref="tableRef" row-key="payloadId" class="monitor-table" :bordered="false" :data="dataSource"
        :pagination="false" @filter-change="onFilterChange">
        <template #columns>
          <a-table-column title="客户id" data-index="payloadId">
            <template #cell="{ record }">
              <span class="monitor-item" @click="showDetail(record)">{{ record.payloadId ? record.payloadId : '-'
              }}</span>
            </template>
          </a-table-column>

          <a-table-column title="状态" data-index="status" class="status-cell" :filterable="filterStatus">
            <template #cell="{ record }">
              <span class="status-cell" :class="record.status">{{
                filterStatusText(record.status)
              }}</span>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <a-pagination :total="pagination.total" size="small" :current="pagination.page" :page-size="pagination.size" simple
        @change="changePage" />
      <!-- <span class="tip">最多显示10000条，如需查看全部数据，请<a href="javascript:void" @click="downloadData">下载</a>到本地查看</span> -->
    </div>
    <MonitorCompareDlg ref="compareRef" />
    <MonitorDetailDlg ref="monitorDetailDlgRef" />
  </a-drawer>
</template>

<script setup>
import { computed, inject, ref, shallowRef } from "vue";
import { Notification, Modal, Message } from "@arco-design/web-vue";
import { useUserStore } from "@/store";
import { getMonitorPage, createMonitorJob } from "@/api/monitor";
import { saveTask2, startTask } from "@/api/task";
import { retryTask } from "@/api/campaign";
import { flowNodeStatus } from "@/constant/flow";
import MonitorCompareDlg from "@/components/modal-dlg/monitor-compare-dlg.vue";
import MonitorDetailDlg from "@/components/modal-dlg/monitor-detail-dlg.vue";
import { uuid } from "@/utils/uuid";

const userStore = useUserStore();
const userInfo = computed(() => {
  return userStore.userAuthInfo;
});

const flow = inject("flow");
const { easyflow, entity } = flow;
const id = ref(null);
const node = ref(null);
const connections = ref(null);
const show = ref(false);
const pannel = shallowRef(null);
const iconClass = ref(null);
const globalExpression = ref(null);
const pannelWidth = ref(460);
const dataSource = ref(null);
const pagination = ref({
  total: 0,
  page: 1,
  size: 10,
  showPageSize: true,
});
const tableRef = ref(null);
const compareRef = ref(null);
const monitorDetailDlgRef = ref();
const status = ref(null);
const help = shallowRef(null);
const incoming = ref(0);
const outgoing = ref(0);
const error = ref(0);
const filterStatus = ref({
  filters: flowNodeStatus,
});
const filterStatusText = (status) => {
  if (status === "ERROR") {
    return "失败";
  } if (status === "OUTGOING") {
    return "成功";
  } if (status === "INCOME") {
    return "接收";
  }
};

const graph = computed(() => {
  return flow?.graph.value;
});

const showPannel = (nodePannel) => {
  pannel.value = nodePannel;
};

const showHelp = (helpPannel) => {
  help.value = helpPannel;
};

const tooglePannel = async (is, _node, _connections, _data) => {
  if (is) {
    id.value = _node.id;
    node.value = _node;
    connections.value = _connections;
    iconClass.value = easyflow.value.getNodeConfig(_node.shape).iconClass;
    incoming.value = _data?.income ? _data?.income : 0;
    outgoing.value = _data?.outgoing ? _data?.outgoing : 0;
    error.value = _data?.error ? _data.error : 0;
    await bindData();
    show.value = is;
  } else {
    graph.value.cleanSelection();
    pannel.value = null;
    iconClass.value = null;
    show.value = is;
  }
};

const haveAuth = (auth) =>{
  const nodeConfig = easyflow.value.getNodeConfig(node.value.shape);
  return nodeConfig?.auth?.includes(auth);
};

const handleCancel = () => {
  tooglePannel(false);
};

const cancel = () => {
  tooglePannel(false);
};
const changePage = (e) => {
  pagination.value.page = e;
  bindData();
};
const bindData = async () => {
  let expression = `flowId eq ${flow.flowId} AND taskId eq ${node.value.id} AND instanceId eq ${flow.instanceId.value}`;
  if (status.value) {
    expression += ` AND status eq ${status.value}`;
  }
  globalExpression.value = expression;
  const data = await getMonitorPage({
    expression,
    page: pagination.value.page - 1,
    size: 10,
  });
  for (let i = 0; i < data.content.length; i++) {
    const it = data.content[i];
    if (it.status === "ERROR") {
      it.statusText = "失败";
    } else if (it.status === "OUTGOING") {
      it.statusText = "成功";
    } else if (it.status === "INCOME") {
      it.statusText = "接收";
    }
  }
  dataSource.value = data.content;
  pagination.value.total = data.totalElements;
};

const onFilterChange = async (dataIndex, filteredValues) => {
  status.value = filteredValues[0];
  if (dataIndex === "status") {
    await bindData();
  }
};

const downloadData = async () => {
  Notification.info({
    title: "下载任务创建成功，请到任务中心下载文件。",
  });
};
const showCompareDlg = () => {
  compareRef.value.flowId = flow.flowId;
  compareRef.value.node = node.value;
  compareRef.value.show();
};
const showDetail = (item) => {
  monitorDetailDlgRef.value.flowId = flow.flowId;
  monitorDetailDlgRef.value.node = node.value;
  monitorDetailDlgRef.value.show(item);
};

const exportNodePayloadId = async (type) => {
  Modal.confirm({
    title: "创建导出任务",
    content: "创建导出任务后，可在任务管理中查看，请确认是否导出?",
    onOk: async () => {
      const job = {
        id: `${id.value}_${uuid(6)}`,
        name: `${entity.name}_${node.value?.data?._name}_数据导出`,
        type,
        timestamp: new Date(),
        status: "SUBMITTED",
        userId: userInfo.value.username,
        payload: {
          query: globalExpression.value,
        }
      }
      const res = await saveTask2(job);
      await startTask(res.id);
      // await createMonitorJob(globalExpression.value);
      Message.success("创建活动导出任务成功,可在任务管理中查看");
    },
  });
};

const retryExecute = async () => {
  const myParams = new URLSearchParams();
  myParams.append("flowId", flow.flowId.value);
  myParams.append("taskId", node.value.id);
  await retryTask(myParams);
};

defineExpose({
  tooglePannel,
  showPannel,
  showHelp,
  handleCancel,
});
</script>

<style lang="less">
.monitor-pannel {
  z-index: 5 !important;

  .title-name {
    display: inherit;

    .node-icon {
      margin-right: 5px;
    }

    .arco-icon {
      margin-left: 5px;
      color: lightgray;
      cursor: pointer;
    }

    .arco-input-wrapper .arco-input.arco-input-size-medium {
      line-height: 16px;
    }
  }

  .btn-line {
    margin: 10px 0;
  }

  .arco-drawer {
    z-index: 5;
    border-left: 1px solid var(--color-border);
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 8%);

    .arco-drawer-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }

    .monitor-card {
      border: 1px solid;
      padding: 10px;

      .title {
        font-size: 18px;
        font-weight: bold;
      }
    }

    .monitor-table {
      margin-top: 10px;

      .monitor-item {
        cursor: pointer;
        color: rgb(var(--primary-6));
      }

      .status-cell {
        &.INCOME {
          color: gray;
        }

        &.OUTGOING {
          color: green;
        }

        &.ERROR {
          color: red;
        }
      }
    }

    .tip {
      color: grey;
    }
  }
}
</style>
