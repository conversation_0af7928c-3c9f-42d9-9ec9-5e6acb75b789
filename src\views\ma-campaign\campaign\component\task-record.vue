<template>
  <div>
    <a-table ref="table" :bordered="false" :data="dataList" :pagination="false">
      <template #columns>
        <a-table-column :title="t('campaign.task.operationTime')" data-index="timestamp" :width="50">
          <template #cell="{ record }">
            {{ $moment(record.createTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.task.operationType')" data-index="type" :width="25">
          <template #cell="{ record }">
            {{ typeName(record.type) }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.task.operationUser')" data-index="userId" :width="25">
          <template #cell="{ record }">
            {{ record.userName }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.task.operationInfo')" data-index="userId" :width="80">
          <template #cell="{ record }">
            <!-- {{ record.message }} -->
            <div v-html="record.message"></div>
          </template>
        </a-table-column>
      </template>
    </a-table>

    <a-pagination
      :total="pagination.total"
      size="small"
      :current="pagination.page"
      :page-size="pagination.size"
      show-total
      show-jumper
      show-page-size
      @change="changePage"
      @page-size-change="changeSizePage"
    />
  </div>
</template>

<script>
import { ref, getCurrentInstance } from "vue";
import { getRecordViewList } from "@/api/campaign";
import { getUserList } from "@/api/user";
import { filters } from "@/utils/filter";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export default {
  components: {},
  props: {},
  setup(props) {
    const {
      proxy: { t }
    } = getCurrentInstance();

    const columns = [
      {
        // title: "操作时间",
        title: t('campaign.task.operationTime'),
        dataIndex: "time"
      },
      {
        // title: "操作类型",
        title: t('campaign.task.operationType'),
        dataIndex: "type"
      },
      {
        // title: "操作用户",
        title: t('campaign.task.operationUser'),
        dataIndex: "user"
      }
    ];
    const dataList = ref([]);

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true
    });

    // 类型
    const typeName = (key) => {
      const list = {
        CREATE: "创建",
        DELETE: "删除",
        CLONE: "克隆",
        COMMIT: "提交",
        APPROVE: "审批",
        START: "启动",
        STOP: "停止",
        PAUSE: "暂停",
        RESUME: "恢复"
      };
      return list[key];
    };

    const activeId = ref("");
    const userIds = ref([]);
    const userList = ref([]);

    const getDataList = async () => {
      getRecordViewList({
        ...pagination.value,
        page: pagination.value.page - 1,
        expression: `campaignId eq ${activeId.value}`,
        sort: ""
      }).then((res) => {
        dataList.value = res.content;
        pagination.value.total = res.totalElements;
      });
    };

    // 获取用户列表
    const getUserLists = () => {
      getUserList(tenantId, buCode, {
        expression: `id in ${userIds.value.toString()}`,
        fields: ""
      }).then((res) => {
        userList.value = res.content;
      });
    };

    // 切换页数
    const changePage = (e) => {
      pagination.value.page = e;
      getDataList();
    };
    // 切换条数
    const changeSizePage = (e) => {
      pagination.value.page = 1;
      pagination.value.size = e;
      getDataList();
    };

    return {
      columns,
      pagination,
      getDataList,
      activeId,
      typeName,
      changePage,
      getUserLists,
      changeSizePage,
      dataList,
      userList,
      filters
    };
  }
};
</script>

<style lang="less" scoped>
.ma-campaign-list {
  .tag-item {
    margin: 0 5px 5px 0;
  }

  .status {
    &.DRAFT {
      color: rgba(var(--green-6));
    }

    &.COMMITTED {
      color: rgba(var(--lime-6));
    }

    &.APPROVED {
      color: rgba(var(--cyan-6));
    }

    &.REJECTED {
      color: rgba(var(--gold-6));
    }

    &.RUNNING {
      color: rgba(var(--blue-6));
    }

    &.PAUSED {
      color: rgba(var(--magent-6));
    }

    &.FINISHED {
      color: rgba(var(--arcoblue-6));
    }

    &.STOP {
      color: rgba(var(--gray-6));
    }
  }

  .cell-item {
    display: flex;
    align-items: center;
  }
}
</style>
