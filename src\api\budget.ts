import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function getAnnualfindPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget-annual`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function setAnnualBudgetItem(data?: any) {
  return axios[data.id ? "put" : "post"](
    `/api/ma-manage/${tenantId}/${buCode}/budget-annual`,
    data
  );
}

export function deleteAnnualItem(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/budget-annual/${id}`
  );
}

export function findList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget/list`, {
    params,
  });
}

export function getAnnualBudget(annual?: any) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/budget-annual/${annual}/summary`
  );
}

export function findItem(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget/${id}`);
}

export function saveInfo(info?: any) {
  return info.id
    ? axios.put(`/api/ma-manage/${tenantId}/${buCode}/budget`, info)
    : axios.post(`/api/ma-manage/${tenantId}/${buCode}/budget`, info);
}

export function deleteItem(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/budget-item/${id}`);
}

export function findBudgetItemList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget-item/list`, {
    params,
  });
}

export function getBudgetSummary(id?: any) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget/${id}/summary`);
}

export function setBudgetItem(data?: any) {
  return axios[data.id ? "put" : "post"](
    `/api/ma-manage/${tenantId}/${buCode}/budget-item`,
    data
  );
}

export function addBudgetItem(data?: any) {
  return axios[data.id ? "put" : "post"](
    `/api/ma-manage/${tenantId}/${buCode}/budget-item`,
    data
  );
}

export function addBudget(data?: any) {
  return axios[data.id ? "put" : "post"](
    `/api/ma-manage/${tenantId}/${buCode}/budget`,
    data
  );
}

export function deleteBudgetItem(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/budget-item/${id}`);
}

export function getBudgetItemInfo(id?: any) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget-item/${id}`);
}

export function findAnnualBudgetList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget-annual/list`, {
    params,
  });
}

export function changeBudgetItem(data?: any) {
  return axios.put(`/api/ma-manage/${tenantId}/${buCode}/budget-item`, data);
}

export function getBudgetGroupList() {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget-group/list`);
}

export function getBudgetAnnualGroupItem(data?: any, annual?: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/budget/annual/${annual}/group`,
    data
  );
}

export function setDefaultAnnualBudgetItem(id?: any) {
  return axios.put(
    `/api/ma-manage/${tenantId}/${buCode}/budget-annual/makeDefault/${id}`
  );
}

export function getBudgetAlertPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget-alert`, {
    params: {
      ...params,
      ...query,
    },
  });
}
