<template>
  <div class="navbar">
    <div class="left-side">
      <a-space>
        <a-avatar :size="32" :style="{ cursor: 'pointer', backgroundColor: '#fff' }" @click="drawerVisible = true">
          <img alt="avatar" src="@/assets/favicon.ico" />
        </a-avatar>
        <a-drawer :visible="drawerVisible" :footer="false" :header="false" placement="left" class="menu-drawer"
          width="360px" @cancel="drawerVisible = false">
          <div class="top-list">
            <i class="iconfont icon-app-center" @click="drawerVisible = false" />
            <a href="/" class="item-list">LIANWEI SaaS</a>
          </div>
          <div class="title-name">应用</div>
          <div class="app-content">
            <a v-for="app in userStore.userInfo.userAuthInfo.grantedApplications" :key="app.name" class="app"
              :href="app.url">
              <div class="app-icon">
                <span class="iconfont" :class="app.icon" />
              </div>
              <div class="app-name">
                <div class="name">{{ app.name }}</div>
                <div class="sub-name">联蔚 {{ app.aliasName || app.name }}</div>
              </div>
            </a>
          </div>
        </a-drawer>

        <a-typography-title :style="{ margin: 0, fontSize: '18px' }" :heading="5">
          LIANWEI MA
        </a-typography-title>
        <icon-menu-fold v-if="appStore.device === 'mobile'" style="font-size: 22px; cursor: pointer" />
      </a-space>

    </div>
    <ul class="right-side">
      <li>
        <a-dropdown trigger="click" @select="changeBussinessUnit">
          <a-button style="color: black; background: white; font-weight: bold">
            <icon-apps :stroke-width="5" />
            {{ userBussinessUnitStore.currentBussinessUnit.name }}
            <icon-down :stroke-width="5" />
          </a-button>
          <template #content>
            <a-doption v-for="item in userBussinessUnitStore.bussinessUnit" :key="item.id" :value="item">
              <template #icon>
                <icon-apps />
              </template>
              {{ item.name }}
            </a-doption>
          </template>
        </a-dropdown>
      </li>

      <li class="user-body">
        <a-dropdown trigger="click">
          <div class="user-dropdown">
            <a-avatar :size="32" :style="{
              marginRight: '8px',
              background: 'rgb(var(--primary-6))',
            }">
              <icon-user :style="{ fontSize: '18px' }" />
            </a-avatar>
            <div class="user-name">
              <div class="title">
                {{ userInfo.tenantName }}
              </div>
              <div class="desc">
                {{ userInfo.realName }}
              </div>
            </div>
          </div>
          <template #content>
            <div style="width: 100px; display: flex; margin: 10px">
              <div>
                <a-avatar :size="32" :style="{ cursor: 'pointer', background: 'black' }">
                  <icon-user :style="{ fontSize: '18px' }" />
                </a-avatar>
              </div>
              <div class="user-name">
                <div class="title">{{ userInfo.realName }}</div>
                <div class="desc">{{ userInfo.email }}</div>
              </div>
            </div>
            <a-divider />
            <a-doption>
              <a-space @click="handleLogout">
                <icon-export />
                <span>
                  {{ $t("messageBox.logout") }}
                </span>
              </a-space>
            </a-doption>
            <div style="height: 20px"></div>
          </template>
        </a-dropdown>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup async>
import { computed, ref } from "vue";
import { useAppStore, useUserStore, useBussinessUnitStore } from "@/store";
// import { LOCALE_OPTIONS } from "@/locale";
import useLocale from "@/hooks/locale";
import useUser from "@/hooks/user";

const appStore = useAppStore();
const userStore = useUserStore();
const userBussinessUnitStore = useBussinessUnitStore();
const userInfo = computed(() => {
  return userStore.userAuthInfo;
});
const { logout } = useUser();
const { changeBussinessUnit } = useLocale();
// const locales = [...LOCALE_OPTIONS];

const handleLogout = () => {
  logout();
};

const drawerVisible = ref(false);
</script>

<style scoped lang="less">
.navbar {
  display: flex;
  justify-content: space-between;
  height: 100%;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
}

.left-side {
  display: flex;
  align-items: center;
  padding-left: 20px;
}

.right-side {
  display: flex;
  padding-right: 20px;
  list-style: none;

  :deep(.locale-select) {
    border-radius: 20px;
  }

  li {
    display: flex;
    align-items: center;
    padding: 0 10px;
  }

  a {
    color: var(--color-text-1);
    text-decoration: none;
  }

  .nav-btn {
    color: rgb(var(--gray-8));
    font-size: 16px;
    border-color: rgb(var(--gray-2));
  }

  .trigger-btn,
  .ref-btn {
    position: absolute;
    bottom: 14px;
  }

  .trigger-btn {
    margin-left: 14px;
  }
}

.app-content {
  display: flex;
  flex-wrap: wrap;
}

.app {
  width: 50%;
  height: 56px;
  display: flex;
  cursor: pointer;
  overflow: hidden;
  border-radius: 4px;
  transition: all ease 0.1s;
  text-decoration: none;

  >div {
    height: 100%;
  }

  .app-icon {
    width: 56px;
    display: flex;
    color: #165dff;
    align-items: center;
    justify-content: center;

    .iconfont {
      font-size: 24px;
      // font-weight: bolder;
    }
  }

  .app-name {
    flex: 1;
    position: relative;

    // font-weight: bolder;
    .name {
      top: 17px;
      font-size: 14px;
      color: #1d2129;
      position: absolute;
      letter-spacing: 0.5px;
      transition: all ease 0.3s;
    }

    .sub-name {
      top: 37px;
      opacity: 0;
      font-size: 12px;
      font-weight: 400;
      color: #4e5969;
      position: absolute;
      transition: all ease 0.3s;
    }
  }

  &:hover {
    background: rgba(0, 0, 0, 0.03);

    .name {
      top: 10px;
    }

    .sub-name {
      top: 30px;
      opacity: 1;
      position: absolute;
    }

    .app-icon {
      color: #11d2ac;
    }
  }
}

.message-popover {
  .arco-popover-content {
    margin-top: 0;
  }
}

.user-body {
  .user-dropdown {
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}

.user-name {
  display: flex;
  flex-direction: column;
  margin-left: 5px;

  .title {
    font-weight: bold;
  }

  .desc {
    font-size: 12px;
    margin-top: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 60px;
  }
}

.top-list {
  display: flex;
  align-items: center;
  justify-content: space-between;

  i {
    color: rgb(var(--primary-6));
  }

  .item-list {
    cursor: pointer;
    color: rgb(var(--primary-6));
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s;

    &:hover {
      opacity: 0.5;
    }
  }
}

.title-name {
  font-size: 22px;
  color: #666;
  padding: 30px 0;
}
</style>
