const config = {
  path: "reach",
  name: "reach",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.reach.template",
    requiresAuth: true,
    icon: "icon-layers",
    order: 40,
    parentMenu: true,
  },
  children: [
    {
      path: "dynamic/:id",
      name: "dynamic",
      component: () => import("@/views/ma-reach/flow-content/main.vue"),
      meta: {
        type: "menu",
        locale: "menu.reach.template",
        requiresAuth: true,
        // roles: ["ma_menu.flow-content"],
      },
    },
    {
      path: "dynamic/edit",
      name: "ChannelTemplateEdit",
      component: () => import("@/views/ma-reach/flow-content/edit.vue"),
      meta: {
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
    {
      path: "flow-template",
      name: "FlowTemplateMain",
      component: () => import("@/views/ma-reach/flow-template/main.vue"),
      meta: {
        type: "menu",
        locale: "menu.flow.template",
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "flow-template/edit",
      name: "FlowTemplateEdit",
      component: () => import("@/views/ma-reach/flow-template/edit.vue"),
      meta: {
        locale: "menu.flow.template.edit",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
    {
      path: "communicate",
      name: "CommunicateMain",
      component: () => import("@/views/ma-reach/communicate/main.vue"),
      meta: {
        type: "menu",
        locale: "menu.communicate",
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "communicate/wechat",
      name: "Wechat",
      component: () => import("@/views/ma-reach/communicate/wechat.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "communicate/statement",
      name: "TemplateStatement",
      component: () => import("@/views/ma-reach/communicate/statement.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "communicate/sms",
      name: "sms",
      component: () => import("@/views/ma-reach/communicate/sms.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "communicate/email",
      name: "Email",
      component: () => import("@/views/ma-reach/communicate/email.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "benefit",
      name: "BenefitMain",
      component: () => import("@/views/ma-reach/benefit/main.vue"),
      meta: {
        type: "menu",
        locale: "menu.benefit",
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "benefit/coupon",
      name: "BenefitCouponEdit",
      component: () => import("@/views/ma-reach/benefit/coupon.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "benefit/points",
      name: "BenefitPointsEdit",
      component: () => import("@/views/ma-reach/benefit/points.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "benefit/benefit",
      name: "BenefitBenefitEdit",
      component: () => import("@/views/ma-reach/benefit/benefit.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
    {
      path: "benefit/statement",
      name: "BenefitTemplateStatement",
      component: () => import("@/views/ma-reach/benefit/statement.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"],
      },
    },
  ],
};

export default config;
