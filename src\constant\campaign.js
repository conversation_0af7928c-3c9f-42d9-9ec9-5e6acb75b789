import i18n from "../locale";

export const campaignType = [
    {
        value: "AUDIENCE",
        // label: "人群包",
        label: i18n.global.t("global.task.campaign.audiencePackage")
    },
    {
        value: "EVENT",
        // label: "事件",
        label: i18n.global.t("global.task.campaign.event")
    },
    {
        value: "FLOW",
        // label: "流程",
        label: i18n.global.t("global.task.campaign.flow")
    },
    {
        value: "GROUP",
        // label: "企微客户群",
        label: i18n.global.t("global.task.campaign.customerGroup")
    },
];

export const campaignCategories = [{
    value: "solicit",
    // label: "拉新",
    label: i18n.global.t("global.task.campaign.acquisition")
},
{
    value: "activate",
    // label: "促活",
    label: i18n.global.t("global.task.campaign.activation")
},
{
    value: "redeem",
    // label: "挽留",
    label: i18n.global.t("global.task.campaign.retention")
},];
export const campaignStatus = [{
    value: "DRAFT",
    // label: "草稿",
    label: i18n.global.t("global.task.campaign.draft")
},
{
    value: "COMMITTED",
    // label: "已提交",
    label: i18n.global.t("global.task.campaign.submitted")
},
{
    value: "APPROVED",
    // label: "审核通过",
    label: i18n.global.t("global.task.campaign.approved")
},
{
    value: "REJECTED",
    // label: "审核拒绝",
    label: i18n.global.t("global.task.campaign.rejected")
},
{
    value: "STOPPED",
    // label: "停止",
    label: i18n.global.t("global.task.campaign.stopped")
},
{
    value: "RUNNING",
    // label: "运行中",
    label: i18n.global.t("global.task.campaign.running")
},
{
    value: "PAUSED",
    // label: "暂停",
    label: i18n.global.t("global.task.campaign.paused")
},
{
    value: "FINISHED",
    // label: "完成",
    label: i18n.global.t("global.task.campaign.finished")
},
{
    value: "ERROR",
    // label: "异常",
    label: i18n.global.t("global.task.campaign.error")
},
];

export const simulateEnabled = (status) => {
    return ["DRAFT", "COMMITTED", "REJECTED", "PAUSED"].indexOf(
        status
    ) !== -1
}
