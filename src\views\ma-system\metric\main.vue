/**
  分析指标列表页
 */
<template>
  <module main>
    <template v-slot:filter></template>
    <template v-slot:search></template>
    <template v-slot:main>
      <a-table
        ref="table"
        :bordered="false"
        :data="dataSource"
        :pagination="false"
        @selection-change="onSelect"
      >
        <template #columns>
          <a-table-column title="指标名称" data-index="name" />
          <a-table-column title="指标类型" data-index="type">
            <template #cell="{ record }">
              {{ filters(metricTypes, record.type) }}
            </template>
          </a-table-column>
          <a-table-column title="模型类型" data-index="modelType">
            <template #cell="{ record }">
              {{ filters(modelTypes, record.modelType) }}
            </template>
          </a-table-column>
          <a-table-column title="分析模型" data-index="modelId">
            <template #cell="{ record }">
              {{ record.modelId }}
            </template>
          </a-table-column>
          <a-table-column title="创建时间" data-index="createTime">
            <template #cell="{ record }">
              {{ $moment(record.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </a-table-column>
          <a-table-column title="描述信息" data-index="summary" />
          <a-table-column title="操作" align="center">
            <template #cell="{ record }">
              <a-button @click="detail(record)" type="text" size="small"
                >编辑</a-button
              >
              <a-button @click="detail(record)" type="text" size="small"
                >报表</a-button
              >
              <a-button @click="deleteData(record.id)" type="text" size="small"
                >删除</a-button
              >
            </template>
          </a-table-column>
        </template>
      </a-table>
    </template>
  </module>
</template>

<script>
import { ref, watch, provide, computed, onMounted } from "vue";
import { Notification } from "@arco-design/web-vue";
import { useRouter } from "vue-router";
import { filters } from "@/utils/filter";
import { metricTypes, modelTypes } from "@/constant/metric";
import { findMetricPage, deleteMetric } from "@/api/analysis";
export default {
  components: {},
  setup() {
    // 路由API
    const router = useRouter();

    const module = ref({
      entityIdField: "id",
      entityName: "分析指标",
      breadcrumb: [
        {
          name: "系统设置",
          path: "/system/setting",
        },
        { name: "分析指标" },
      ],
      editPath: "/system/metric/edit",
      viewPath: "/system/metric/edit",
      createPath: "/system/metric/edit",
    });

    const filter = ref([]);

    let group = ref([]);

    let show = ref(true);

    // 数据设置
    const entity = ref({});
    // 分页设置
    const pagination = ref({
      page: "1",
      size: "10",
      total: 0,
      showPageSize: true,
    });
    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    // 查询API
    const bindData = async (expression) => {
      entity.value = await findMetricPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        {
          expression: expression,
          fields: "name,type,modelType,createdTime,summary",
        }
      );
      pagination.value.total = entity.value.totalElements;
    };

    const detail = (row) => {
      const query = {};
      query["id"] = row["id"];
      router.push({ path: module.value.viewPath, query: query });
    };

    const deleteData = async (id) => {
      await deleteMetric(id);
      Notification.warning({
        title: "删除分析指标成功",
      });
      if (dataSource.value.length === 1 &&
          pagination.value.page > 1) {
        pagination.value.page--
      }
      await bindData();
    };
    const onSelect = async () => {};
    onMounted(async () => {});
    const setup = {
      show,
      group,
      bindData,
      filter,
      module,
      filters,
      metricTypes,
      modelTypes,
      dataSource,
      onSelect,
      detail,
      deleteData,
    };
    provide("main", setup);
    return setup;
  },

  data() {
    return {};
  },
};
</script>
