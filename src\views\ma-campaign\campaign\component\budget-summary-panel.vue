<template>
  <a-grid :row-gap="16" class="panel">
    <a-grid-item
        class="panel-col"
        :span="6">
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
              alt="avatar"
              src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/fdc66b07224cdf18843c6076c2587eb5.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
            title="计划预算"
            :value="budgetSummary.planned"
            show-group-separator
            :precision="2"
        >
          <template #suffix>
            <span class="unit">元</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
        class="panel-col"
        :span="6">
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
              alt="avatar"
              src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/fdc66b07224cdf18843c6076c2587eb5.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
            title="已使用"
            :value="budgetSummary.spent"
            :precision="2"
            show-group-separator>
          <template #suffix>
            <span class="unit">元</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
        class="panel-col"
        :span="6"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
              alt="avatar"
              src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/77d74c9a245adeae1ec7fb5d4539738d.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
            title="已确认"
            :value="budgetSummary.confirmed"
            :precision="2"
            show-group-separator
        >
          <template #suffix>
            <span class="unit">元</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item
        class="panel-col"
        :span="6"
        style="border-right: none"
    >
      <a-space>
        <a-avatar :size="54" class="col-avatar">
          <img
              alt="avatar"
              src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/c8b36e26d2b9bb5dbf9b74dd6d7345af.svg~tplv-49unhts6dw-image.image"
          />
        </a-avatar>
        <a-statistic
            title="预算余额"
            :value="budgetSummary.balance"
            :precision="2"
            show-group-separator
        >
          <template #suffix>
            <span class="unit">元</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>
    <a-grid-item :span="30">
      <a-divider class="panel-border"/>
    </a-grid-item>
  </a-grid>
</template>

<script>
export default {
  props: {
    budgetSummary: {
      type: Object,
      default: () => {
        return {
          balance: 0,
          confirmed: 0,
          planned: 0,
          reserved: 0,
          spent: 0,
          total: 0
        }
      }
    }
  },
  setup() {
  }
}
</script>

<style lang="less" scoped>
.arco-grid.panel {
  margin-bottom: 20px;
}

:deep(.arco-statistic-title){
  margin-bottom: 0;
}

.panel-col {
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid rgb(var(--gray-2));
}

.col-avatar {
  margin-right: 12px;
  background-color: var(--color-fill-2);
}

.up-icon {
  color: rgb(var(--red-6));
}

.unit {
  margin-left: 8px;
  color: rgb(var(--gray-8));
  font-size: 12px;
}

:deep(.panel-border) {
  margin: 4px 0 0 0;
}
</style>
