import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import AbTestingNode from "./node.vue";
import AbTestPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "ab_testing",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<AbTestingNode />`,
      components: {
        AbTestingNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("AbTestingNode", nodeData.node, true);
};

const AbTest = {
  type: "ab_testing",
  name: "AbTesting",
  shape: "AbTestingNode",
  iconClass: "icon-ABtestshezhi",
  registerNode,
  pannel: AbTestPannel,
  help: Help,
  skippable: false,
  auth: [
    "export_task_record"
  ]
};

export default AbTest;
