import { Graph } from "@antv/x6";
import { getPorts } from "../../components/node";
import FlowBatchNode from "./node.vue";
import FlowBatchPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "flow_batch",
  node: {
    inherit: "vue-shape",
    x: 0,
    y: 0,
    width: 232,
    height: 104,
    component: {
      template: `<FlowBatchNode />`,
      components: {
        FlowBatchNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("FlowBatchNode", nodeData.node, true);
};

const FlowBatch = {
  type: "flow_batch",
  name: "去重",
  shape: "FlowBatchNode",
  iconClass: "icon-quzhong",
  registerNode,
  pannel: FlowBatchPannel,
  help: Help,
  skippable: true,
};

export default FlowBatch;
