<template>
  <component :is="component" ref="modelRef">
    <template #breadcrumb><slot name="breadcrumb"></slot></template>
    <template #top><slot name="top"></slot></template>
    <template #filter><slot name="filter"></slot></template>
    <template #search><slot name="search"></slot></template>
    <template #action><slot name="action"></slot></template>
    <template #context><slot name="context"></slot></template>
    <template #main><slot name="main"></slot></template>
    <template #card><slot name="card"></slot></template>
  </component>
</template>

<script>
import { ref, computed } from "vue";

import main from "./mainTemplate.vue";
import edit from "./editTemplate.vue";
import view from "./viewTemplate.vue";

export default {
  components: {
    "main-template": main,
    "edit-template": edit,
    "view-template": view,
  },
  props: {
    main: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    edit: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    view: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const modelRef = ref(null);
    const component = computed(() => {
      if (props.main) {
        return "main-template";
      }
      if (props.edit) {
        return "edit-template";
      }
      if (props.view) {
        return "view-template";
      }
      return "main-template";
    });

    return {
      component,
      modelRef,
    };
  },
};
</script>
