import { Graph } from "@antv/x6";
import { getPorts } from "../../components/node";
import UniqueNode from "./node.vue";
import UniquePannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "unique",
  node: {
    inherit: "vue-shape",
    x: 0,
    y: 0,
    width: 232,
    height: 104,
    component: {
      template: `<UniqueNode />`,
      components: {
        UniqueNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("UniqueNode", nodeData.node, true);
};

const Unique = {
  type: "unique",
  name: "去重",
  shape: "UniqueNode",
  iconClass: "icon-quzhong",
  registerNode: registerNode,
  pannel: UniquePannel,
  help: Help,
  skippable: true,
};

export default Unique;
