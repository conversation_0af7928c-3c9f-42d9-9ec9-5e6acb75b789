import { getCategory } from "@/api/category";

export default {
  "menu.campaign": "活动管理",
  "menu.campaign.campaign": "活动管理",
  "menu.campaign.flow": "活动流程",
  "menu.campaign.kpi": "活动 KPI",
  "menu.campaign.abtest": "A/B Test",
  "menu.campaign.budget": "活动预算",
  "menu.campaign.history": "执行记录",
  campaign :{
    title: "营销活动",
    name: "活动名称",

    button: {
      search: "查询",
      reset: "重置",
      create: "新建",
      increaseBudget: "追加预算",
    },

    flow: {
      title: "活动流程",
      campaign: "流程活动",
      taskCommit: "提交审批",
      taskRecord: "操作记录",
    },

    task: {
      title: "活动任务",
      package: "人群包活动",
      name: "名称",
      description: "描述",
      campaignPeriod: "活动周期",
      campaignCategory: "活动分类",
      campaignTags: "活动标签",
      selectAudiencePackage: "选择人群包",
      estimatedPopulation: "预估人数",
      executionAction: "执行动作",
      diversionStrategy: "分流策略",
      noDiversion: "不分流",
      percentageDiversion: "百分比分流",
      operationTime:"操作时间",
      operationType:"操作类型",
      operationUser:"操作用户",
      operationInfo:"操作信息",
    },

    pannel: {
      selectAudience:"选择人群",
      audienceSnapshot:"人群快照",
      estimatedNumber:"预估人数",
      marketingFrequency:"营销频次",
      triggerTime:"触发时间",
      startCampaign:"开始活动",
      audienceDeduplication:"人群去重",
      appendAudience:"追加人群",
      nodeDescription:"节点说明",
      remarkInfo:"备注信息",
      reachContent:"触达内容",
      reachField:"触达字段",
      frequencyLimit:"频次限制",
      deduplication:"去重",
      selectDeduplication: "选择去重字段",
    },

    report: {
      title: "活动报表",
      cumulativeReachCount: "累计触达次数",
      cumulativeReachPeople: "累计触达人数",
      reachChannel: "触达渠道",
      reachCount: "触达次数",
      todayReachCount: "今日触达次数",
      todayReachSuccessRate: "今日触达成功率",
      cumulativeReachSuccessCount: "累计触达成功次数",
      cumulativeReachSuccessRate: "累计触达成功率",
      channelName: "渠道名称",
      channel: "渠道",
      operationTime: "操作时间",
      campaignId: "活动编号",
      campaignName: "活动名称",
      campaignCategory: "活动分类",
      canvasId: "画布编号",
      canvasName: "画布名称",
      executionStatus: "执行状态",
      customerId: "客户ID",
      customerIdentityField: "客户身份字段",
      reachField: "触达字段",
      reachFieldValue: "触达字段值",
      reachContent: "触达内容",
      reachData: "触达数据",
      reachStatus: "触达状态",
      processInstanceId: "流程实例ID",
      canvasNodeId: "画布节点ID",
      reachTemplateId: "触达模板ID",
      reachTemplateName: "触达模板名称",
      reachContentId: "触达内容ID",
      reachContentName: "触达内容名称",
      acceptanceStatus: "接受状态",
      completionStatus: "完成状态",
      errorStatus: "出错状态",
      nodeType: "节点类型",
      isTemplateFrequencyLimit: "是否模版频次限制",
      isChannelFrequencyLimit: "是否渠道频次限制",
      isGlobalFrequencyLimit: "是否全局频次限制",
      message: "消息",
    },
    budget: {
      budgetManagement: "预算管理",
      budgetSettings: "预算设置",
      entryName: "条目名称",
      plan: "计划",
      used: "已使用",
      remaining: "结余",
      warningValue: "预警值",
      action: "操作",
      addBudget: "追加预算",
    },

    canvas: {
      title: "活动画布",
      code: "编码",
      name: "名称",
      category: "分类",
      status: "状态",
      type: "活动类型",
      remark: "备注",
      saveAsTitle: "画布另存为",
      addTitle: "新增画布",
      canvasId:"画布ID",
      canvasName:"画布名称",
      campaignCode:"活动编码",
      timePeriod:"时间周期",
      tag:"标签",
    },

    popup: {
      create_title: "新增活动",
      edit_title: "编辑活动",
      code: "活动编码",
      name: "活动名称",
      dataRole: "数据角色",
      description: "备注",
      save_title: "保存营销活动",
    },

    operation: {
      view: "查看",
      edit: "编辑",
      delete: "删除"
    },

    column: {
      code: "活动编码",
      name: "活动名称",
      remark: "备注",
      campagin:"活动",
      canvasId: "画布ID",
      canvasName: "画布名称",
      category: "分类",
      type: "类型",
      tags: "标签",
      status: "状态",
      description: "描述",
      startTime: "开始时间",
      endTime: "结束时间",
      action: "操作",
    },
    reminder: {
      input_activity_name: "请输入活动名称",
      data_irretrievable_warning: "删除之后数据不可恢复，请确认是否删除？",
      delete_campaign: "删除活动",
      input_activity_code: "请输入活动编码",
      input_canvas_id: "请输入画布ID",
      input_canvas_name: "请输入画布名称",
      input_data_role: "请输入数据角色",
      input_description: "请输入活动备注信息",
      input_canvas_remark: "请输入画布备注信息",
      activity_code_format: "活动编码只能输入大小写字母、数字和下划线",
      inputCanvasCode: "请输入编码",
      inputCanvasName: "请输入名称",
      selectCanvasCategory: "请选择分类",
      selectCanvasStatus: "请选择状态",
      selectCampaignType: "请选择活动类型",
      name: "请输入名称",
      field: "请选择字段",
      description: "请输入描述",
      campaignPeriod: "活动周期不能为空",
      campaignCategory: "请选择活动分类",
      campaignTags: "请输入活动标签，按回车添加",
      selectAudiencePackage: "请选择人群包",
      selectAudience: "请选择人群",
      audienceSnapshot: "请选择人群快照",
      marketingFrequency: "请选择营销频次",
      estimatedPopulation: "请输入预估人数",
      executionAction: "请选择执行动作",
      notNull: "不能为空",
      inputAmount: "请输入金额",
      channelName: "渠道名称",
      reachField: "触达字段",
      reachFieldValue: "触达字段值",
      reachStatus: "触达状态",
      canvasNodeId: "画布节点ID",
    }
  }
};
