<template>
  <div v-if="visible" ref="menuRef" class="easyflow-menu">
    <div v-for="(group, index) in groups" :key="index" class="node-container">
      <div class="group-title" @click="toggle(group)">
        <span class="title">{{ group.category }}</span>
        <icon-right v-if="!group.open" title="展开" />
        <icon-down v-if="group.open" title="收起" />
      </div>
      <div v-if="group.open" class="node-items">
        <template v-for="(node, nodeIndex) in group.nodes" :key="nodeIndex">
          <div v-if="!node.fixed" class="node" @mousedown="drag($event, node)">
            <span :class="node.iconClass" class="node-icon iconfont" :style="{
              color: node.color || group.color,
              background: node.themebg || group.themebg,
            }"></span>
            <span class="node-name">{{ node.name }}</span>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, inject, onMounted, nextTick } from "vue";
import { Addon } from "@antv/x6";

export default {
  // inject: {
  //   flow: {
  //     default: null,
  //   },
  // },
  // props: {
  //   nodeGroups: {
  //     type: Array,
  //     default: () => [],
  //   },
  //   graph: {
  //     type: Object,
  //     default: () => { },
  //   },
  // },
  setup() {
    const flow = inject("flow");
    const visible = ref(false);
    const menuRef = ref(null);
    const groups = ref([]);

    onMounted(() => {
      nextTick(() => {
        groups.value = groups.value.concat(flow.nodeGroups.value);
        groups.value.forEach((it) => {
          it.open = true;
        });
        visible.value = true;
      });
    });

    const drag = (e, nodeItem) => {
      const { id } = nodeItem

      // 创建是拿最新颜色
      if (nodeItem.changeItem) {
        nodeItem.changeItem(id)
      }
      const index = flow.easyflow.value.getNodeMaxIndex(nodeItem.shape);
      const v = {
        id: `${nodeItem.type}_${index}`,
        shape: nodeItem.shape,
        data: {
          _type: nodeItem.type,
          _name: nodeItem.name,
          configId: id,
        },
        params: {
          id
        }
      };
      console.log(v)
      const nodeCom = flow.graph.value.createNode(v);
      const dnd = new Addon.Dnd({
        target: flow.graph.value,
        getDragNode: (node) => node.clone({ keepId: true }),
        getDropNode: (node) => node.clone({ keepId: true }),
      });
      dnd.start(nodeCom, e);
    };

    const toggle = (group) => {
      group.open = !group.open;
    };

    return {
      visible,
      groups,
      drag,
      toggle,
      menuRef,
    };
  },

};
</script>

<style lang="less" scoped>
.easyflow-menu {
  width: 268px;
  border-right: 0;
  max-width: 268px;
  overflow-y: overlay;
  border-radius: 4px;
  background: #ffffff;
  box-sizing: border-box;
  user-select: none;

  &::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 1px;
    background: rgba(0, 0, 0, 0.1);
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0);
  }

  .button-box {
    margin: 10px;
    text-align: center;
  }

  .node-container {
    padding: 10px 24px;
    box-sizing: border-box;
    border-bottom: 1px solid #eeeeee;

    &:last-child {
      border-bottom: 0;
    }

    .group-title {
      display: flex;
      justify-content: space-between;
      padding-top: 10px;
      cursor: pointer;
    }

    .title {
      color: #222222;
      margin: 0;
    }

    .node-items {
      margin-top: 14px;
    }

    .node {
      width: 100%;
      height: 42px;
      display: flex;
      cursor: pointer;
      border-radius: 3px;
      align-items: center;
      margin-bottom: 10px;
      box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.055);

      .node-icon {
        width: 32px;
        height: 32px;
        display: flex;
        margin-left: 15px;
        margin-right: 10px;
        border-radius: 50%;
        font-weight: bolder;
        align-items: center;
        justify-content: center;
      }

      .node-name {
        font-size: 14px;
        font-weight: 400;
        color: #222222;
        font-family: PingFang SC;
      }
    }
  }
}
</style>
