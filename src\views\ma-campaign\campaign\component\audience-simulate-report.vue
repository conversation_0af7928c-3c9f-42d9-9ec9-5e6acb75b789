<template>
  <div>
    <a-table ref="recordTable" :pagination="false" :bordered="false" :data="dataSource">
      <template #columns>
        <a-table-column :title="t('campaign.report.channel')" data-index="nodeConfigId" :width="80">
          <template #cell="{ record }">
            {{ nodeConfigNameFilter(record.nodeConfigId) }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.report.operationTime')" data-index="timestamp" :width="120">
          <template #cell="{ record }">
            {{ $moment(record.timestamp).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.report.executionStatus')" data-index="status" :width="80">
          <template #cell="{ record }">
            {{ nodeStatusFilter(record.status) }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.report.customerId')" data-index="payloadId" :width="120" />
        <a-table-column :title="t('campaign.report.reachField')" data-index="reachField" :width="100" />
        <a-table-column :title="t('campaign.report.reachFieldValue')" data-index="reachFieldValue" :width="150" />
        <a-table-column :title="t('campaign.report.reachContent')" data-index="reachContent" :width="200"
          :ellipsis="true" :tooltip="{ class: 'tooltip-content' }" />
        <a-table-column :title="t('campaign.report.reachData')" data-index="reachMapping" :width="200" :ellipsis="true"
          :tooltip="{ class: 'tooltip-content' }" />
        <a-table-column :title="t('campaign.report.reachStatus')" data-index="reachStatus" :width="100">
          <template #cell="{ record }">
            {{ reachStatusFilter(record.reachStatus) }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.report.acceptanceStatus')" data-index="income" :width="80" />
        <a-table-column :title="t('campaign.report.completionStatus')" data-index="outgoing" :width="80" />
        <a-table-column :title="t('campaign.report.errorStatus')" data-index="error" :width="80" />
        <a-table-column :title="t('campaign.report.nodeType')" data-index="taskType" :width="120" />
        <a-table-column :title="t('campaign.report.isTemplateFrequencyLimit')" data-index="limitedTemplate" :width="160"
          :ellipsis="true" :tooltip="true" />
        <a-table-column :title="t('campaign.report.isChannelFrequencyLimit')" data-index="limitedChannel" :width="160"
          :ellipsis="true" :tooltip="true" />
        <a-table-column :title="t('campaign.report.isGlobalFrequencyLimit')" data-index="limitedGlobal" :width="160"
          :ellipsis="true" :tooltip="true" />
        <a-table-column :title="t('campaign.report.message')" data-index="message" :width="150" :ellipsis="true"
          :tooltip="{ class: 'tooltip-content' }" />
      </template>
    </a-table>

  </div>
</template>

<script setup>
import { computed, ref, onMounted, provide, getCurrentInstance } from "vue";
import { filters } from "@/utils/filter";
import { flowNodeStatus } from "@/constant/flow";
import { campaignStatus } from "@/constant/campaign";
import { reachStatus } from "@/constant/reach";
import { getMonitorPage } from "@/api/monitor";

const loading = ref(true);
const isMonitor = ref(false);
const instanceId = ref(null);
const nodelist = ref([]);
const dataSource = ref([]);
// 分页设置
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
  totalElements: 0,
  showPageSize: true,
});

const props = defineProps(["flowId"]);
let monitorInterval;

const nodeStatusFilter = (val) => {
  return filters(flowNodeStatus, val, "text", "value");
};

const nodeConfigNameFilter = (val) => {
  const res = nodelist.value.find((item) => {
    return item.id === val;
  });
  return res ? res.name : "";
};

const reachStatusFilter = (val) => {
  const item = reachStatus.find((it) => {
    return it.value === val;
  })
  return item?.label || "--";
};

const getQueryExp = (exp) => {
  let expression = `instanceId eq ${instanceId.value} AND reachStatus not NULL`;
  if (exp) {
    expression = `${expression} AND ${exp}`;
  }
  return expression;
};

const loadDate = async () => {
  const expression = getQueryExp();
  const page = await getMonitorPage(
    {
      ...pagination.value,
      page: pagination.value.page - 1,
    },
    { expression }
  );

  pagination.value.total =
    page.totalElements > 10000 ? 10000 : page.totalElements;
  pagination.value.totalElements = page.totalElements;

  dataSource.value = page.content || [];
  dataSource.value.map((it) => {
    it.ext = JSON.stringify(it.ext);
    return it;
  });
  loading.value = false;
}

// 切换监控状态
const toogleMonitor = async (enable, _instanceId, engineType) => {
  if (enable) {
        console.log("start Monitor");
    instanceId.value = _instanceId;
    isMonitor.value = enable;
    await loadDate();
    monitorInterval = setInterval(async () => {
      if (!isMonitor.value) {
        clearInterval(monitorInterval);
      } else {
        await loadDate();
      }
    }, 10000);
  } else {
    console.log("stop Monitor");

    isMonitor.value = enable;
    clearInterval(monitorInterval);
    dataSource.value = [];
    instanceId.value = props.flowId;
  }
};

defineExpose({
  toogleMonitor
});
</script>

<style></style>
