import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import ExtendDataNode from "./node.vue";
import pannel from "./pannel.vue";
import help from "./help.vue";

const nodeData = {
  type: "extend_data",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<ExtendDataNode />`,
      components: {
        ExtendDataNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("ExtendDataNode", nodeData.node, true);
};

const getModelId = (data: any) => {
  const result = new Set();
  if(data.modelId){
    result.add(`Behavior:${data.modelId}`);
  }
  return result;
};

const ExtendData = {
  type: "extend_data",
  name: "扩展数据",
  shape: "ExtendDataNode",
  iconClass: "icon-flow-stop",
  color: "#ffffff",
  themebg: "#ff6d69",
  registerNode,
  help,
  pannel,
  skippable: false,
  getModelId
};


export default ExtendData;
