<template>
  <module edit>
    <template #main>
      <div class="template-body">
        <a-form ref="formRef" class="left-form" :model="entity">
          <a-space direction="vertical" :size="16">
            <a-card class="general-card">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="模板名称" field="name">
                    <a-input v-model="entity.name" placeholder="请输入模板名称" />
                  </a-form-item>
                  <a-form-item label="描述信息" field="summary">
                    <a-textarea v-model="entity.summary" placeholder="请输入描述信息" />
                  </a-form-item>
                  <a-form-item label="预算单价" field="budgetSetting.price">
                    <a-input-number v-model="entity.budgetSetting.price" placeholder="请输入沟通预算单价" :min="0">
                    </a-input-number>
                  </a-form-item>
                  <a-form-item label="沟通渠道" field="channelType">
                    <div class="m-t-p">
                      <a-select v-model="entity.channelId" placeholder="请选择沟通渠道" @change="onChangeChannel">
                        <a-option v-for="item in channelList" :key="item.id" :value="item.id" :label="item.name" />
                      </a-select>
                    </div>
                  </a-form-item>
                  <a-form-item label="短信模板代码" field="templateId">
                    <a-input v-model="entity.setting.templateId" placeholder="请输入短信模板代码" />
                  </a-form-item>

                  <a-form-item label="反馈结果" field="setting.feedback" tooltip="需要反馈结果的沟通模板在返回结果后才会触发流程继续执行。">
                    <a-switch v-model="entity.setting.feedback" type="round">
                      <template #checked> 启用 </template>
                      <template #unchecked> 禁用 </template>
                    </a-switch>
                  </a-form-item>
                  <a-form-item label="状态" field="status" tooltip="禁用后在活动流程中将不再显示该沟通模板。">
                    <a-switch v-model="entity.status" type="round" checked-value="ENABLED" unchecked-value="DISABLED">
                      <template #checked> 启用 </template>
                      <template #unchecked> 禁用 </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="模板内容" field="setting.template">
                    <a-textarea v-model="entity.setting.template" placeholder="请输入模板内容" />
                  </a-form-item>
                </a-col>
                <a-col :span="18">
                  <a-typography-title :heading="6">
                    字段映射配置
                  </a-typography-title>
                  <MappingTemplate v-model:dataList="entity.setting.templateMappings" :customer-model="customerModel" />
                </a-col>
              </a-row>
            </a-card>
          </a-space>
        </a-form>

        <!-- 显示 -->
        <ModelView :entity="entity" view-type="SMS" />
      </div>
    </template>

    <template #action>
      <a-button v-if="module.isEdit" type="primary" :loading="simulating" @click="simulate">模拟发送</a-button>
      <simulate-dialog ref="simulateDlg" />
    </template>
  </module>
</template>

<script>
import { ref, provide } from "vue";
import { useRoute, useRouter } from "vue-router";
import { findCommunicateItem, saveCommunicateInfo, findChannelList, simulateCommunicateSend } from "@/api/communicate";
import { findCustomerModel } from "@/api/system";
import { Message } from "@arco-design/web-vue";
import { formatFields } from "@/utils/field";
import SimulateDialog from "@/components/modal-dlg/customer-selector-dlg.vue";
import ModelView from "@/components/ma/model-view/index.vue";
import MappingTemplate from "@/components/ma/mapping-template/index.vue";
export default {
  components: {
    SimulateDialog,
    MappingTemplate,
    ModelView,
  },
  setup() {
    const route = useRoute();
    const router = useRouter();

    const module = ref({
      entityIdField: "id",
      mainPath: "/reach/communicate",
      breadcrumb: [
        {
          name: "营销云触达",
          path: "/reach/communicate"
        },
        {
          name: "编辑短信模板"
        }
      ],
      capabilityType: "sms",
      isEdit: !!route.query.id
    });

    let queryValue = route.query.id;

    const entity = ref({
      status: "ENABLED",
      type: module.value.capabilityType,
      category: "reach",
      budgetSetting: {},
      setting: {
        feedback: false,
        useLengthLimit: false,
        templateMappings: [],
      }
    });
    const channelList = ref([]);
    const customerModel = ref({});
    const simulateDlg = ref(null);
    const simulating = ref(false);

    const bindData = async () => {
      if (module.value.isEdit) {
        entity.value = await findCommunicateItem(queryValue);
      }
      const data = await findCustomerModel();
      if (data) {
        customerModel.value = data;
        customerModel.value.fields = formatFields(data.fields);
      }
      channelList.value = await findChannelList({ expression: `type eq ${module.value.capabilityType}` });
    };

    const save = async () => {
      entity.value = await saveCommunicateInfo(entity.value);
      queryValue = entity.value.id;
      module.value.isEdit = true;
      Message.success("保存成功！");
      if (!entity.value.id) {
        router.push({ path: module.value.mainPath });
      }
    };

    const simulate = async () => {
      const customers = await simulateDlg.value.show(customerModel.value);
      if (customers) {
        simulating.value = true;
        try {
          for (let i = 0; i < customers.length; i += 1) {
            const customer = customers[i];
            const simResult = await simulateCommunicateSend({
              capabilityId: entity.value.id,
              customerId: customer,
              triggerType: 'simulate',
              setting: {
                limited: false
              },
              budgetSetting: {
                enabled: false
              },
            });
            if (simResult.status !== "SENT") {
              Message.error({
                content: `客户${simResult.customerId}模拟失败：${simResult.message}`,
                closable: true
              });
              return;
            }
          }
          Message.success("模拟发送成功！");
        } finally {
          simulating.value = false;
        }
      }
    };

    const onChangeChannel = (id) => {
      entity.value.setting.type = channelList.value.find(it => it.id === id).setting.type;
    };

    const setup = {
      module,
      entity,
      channelList,
      customerModel,
      bindData,
      save,
      simulate,
      simulateDlg,
      simulating,
      onChangeChannel
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.template-body {
  display: flex;
  height: calc(100vh - 160px);
  background-color: #ffffff;

  .left-form {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}

::v-deep(.general-card) {
  min-height: calc(100vh - 160px);
}

.m-t-p {
  position: relative;
  width: 100%;
}
</style>
