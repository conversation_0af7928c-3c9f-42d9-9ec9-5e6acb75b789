<template>
  <div v-if="showComponents" class="conditions">
    <div class="data-top-name">
      <div class="left">
        <slot name="title">
          <i class="iconfont icon-service"></i>{{ name }}
        </slot>
      </div>
      <slot name="btn">
        <a-button type="outline" size="small" @click="addOneItem">添加</a-button>
      </slot>
    </div>
    <TabItem v-if="rootCondition" :tab-condition="rootCondition" :data-model-fields="dataModelFields" :tags="tags" :on-change-data="changeData" />
    <slot name="bottom"></slot>
  </div>
</template>

<script>
import { defineComponent, onMounted, ref, watch } from "vue";
import { formatFields } from "@/utils/field"
import { clone } from "@/utils/common"
import TabItem from "./components/tab-item.vue";

export default defineComponent({
  name: "Conditions",
  components: {
    TabItem,
  },
  props: {
    name: {
      // 数据标识名称
      type: String,
      default: "条件过滤",
    },
    condition: Object,
    dataModel: {
      // 下拉选择数据
      type: [Array, Object],
      default: () => {
        return {};
      },
    },
    tags: Array
  },
  emits: ["onChangeData", "update:rootCondition", "update:condition"],

  setup(props, { emit }) {
    const rootCondition = ref({empty: true});
    const dataModelFields = ref([]);
    const showComponents = ref(false);

    // 新增顶层条件
    const addOneItem = () => {
      if (rootCondition.value.empty) {
        delete rootCondition.value.empty;
        rootCondition.value.name= "";
        rootCondition.value.type= "";
        rootCondition.value.operator= "";
        rootCondition.value.value= "";
        rootCondition.value.conditions= [];
      } else {
        if (
          !rootCondition.value.conditions ||
          rootCondition.value.conditions.length === 0
        ) {
          const tmpCon = clone(rootCondition.value);
          rootCondition.value.conditions = [];
          rootCondition.value.conditions.push(tmpCon);
          rootCondition.value.name= "";
          rootCondition.value.type= "";
          rootCondition.value.operator= "";
          rootCondition.value.value= "";
          rootCondition.value.relation= "and";
        }
        rootCondition.value.conditions.push({
          name: "",
          type: "",
          operator: "",
          value: "",
          conditions: [],
        });
      }
    };

    // 删除变更
    const changeData = (data) => {
      rootCondition.value = data;
      emit("update:condition", rootCondition.value)
    };

    onMounted(() => {
      rootCondition.value = props.condition;
      dataModelFields.value = formatFields(props.dataModel.fields, "");
      showComponents.value = true;
    });

    // 监听数据返回 生成cdp数据结构
    watch(
      rootCondition,
      (newData, oldData) => {
        emit("update:rootCondition", newData);
      },
      { immediate: true, deep: true }
    );

    return {
      showComponents,
      addOneItem,
      rootCondition,
      dataModelFields,
      changeData,
    };
  },
});
</script>

<style lang="less" scoped>
.conditions {
  .data-top-name {
    color: rgb(var(--primary-6));
    font-size: 14px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .left {
      display: flex;
      align-items: center;

      .iconfont {
        margin-right: 2px;
      }
    }
  }

  .or-change-and {
    width: 25px;
    border: 2px solid rgb(var(--primary-6));
    border-right: 0;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    height: calc(100% + 20px);
    display: flex;
    align-items: center;
    position: absolute;
    top: -10px;
    left: 5px;
    bottom: -10px;

    span {
      border: 1px solid rgb(var(--primary-6));
      background-color: #ffffff;
      padding: 1px 3px;
      font-weight: bold;
      margin-left: -12px;
      font-size: 12px;
      cursor: pointer;
    }
  }

  .data-tab {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 15px;

    .btn {
      margin-right: 5px;

      &:last-child {
        margin-right: 0;
      }
    }

    .item-input {
      margin-right: 5px;
      flex: 1;
      position: relative;

      &:nth-child(2) {
        flex: 0.3;
      }

      .type-text {
        background-color: rgb(var(--primary-6));
        font-size: 8px;
        color: #ffffff;
        position: absolute;
        top: -18px;
        right: 0;
        padding: 2px 5px;
        zoom: 0.7;
      }
    }
  }
}</style>
