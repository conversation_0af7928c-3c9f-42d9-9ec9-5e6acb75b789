export default {
  "menu.system": "系统设置",
  "menu.behavior": "行为事件",
  "menu.system.customer": "会员模型设置",
  "menu.system.behavior": "行为模型设置",

  systemSetting: {
    title: {
      basicSettings: "基本设置",
      touchpointManagement: "触点管理",
    },
    basic: {
      basicSettings: "基本设置",
      customerModel: "客户模型",
      behaviorModel: "行为模型",
      behaviorEvent: "行为事件",
      activityCategory: "活动分类",
      channelLimitation: "全渠道限制",
      marketingCenterConfig: "营销中心配置",
    },
    category: {
      deleteCategory: "删除活动分类",
    },
    touchpoint: {
      communicationChannel: "营销云沟通渠道",
      benefitChannel: "营销云权益渠道",
      canvasNodeManagement: "画布节点管理",
      touchpointCategoryManagement: "触点分类管理",
      silenceRule: "勿扰规则",
    },
    basicSettings: {
      customerFieldSetting: "客户字段设置",
      primaryKey: "主键",
      id: "编码",
      name: "名称",
      type: "类型",
      isNull: "可空",
      isArray: "是否数组",
      identification: "身份识别字段",
      showingByList: "列表展示",
      useForQuery: "用于查询",
      fieldUsage: "字段用途",
      defaultValue: "默认值",
      synchronousText: "您将从配置的数据源同步会员模型，是否确认？",
      saveText: "您将修改会员模型，是否确认？",
      someFieldIsNull: "存在未编辑的客户模型字段！",
      fieldExisted: "该编码已存在，不能重复！",
      idTips: "只能输入不以下划线开头的字母数字下划线编码名",
      notAllowNull: "不能为空",
      enterId: "请输入编码",
      enterName: "请输入名称",
      selectType: "请选择类型",
      yes: "是",
      no: "否",
      string: "文本",
      integer: "整型",
      long: "长整型",
      double: "双精度浮点型",
      date: "日期型",
      boolean: "布尔型",
      nested: "嵌套字段",
      selectUseForQuery: "选择查询方式",
      eq: "完全匹配",
      like: "模糊搜索",
      lt: "小于输入值",
      gt: "大于输入值",
      selectFieldUsage: "请选择字段用途",
      enterDefaultValue: "请输入默认值",
      realName: "真实姓名",
      nickname: "昵称",
      gender: "性别",
      mobile: "手机号",
      wechat: "微信号",
      mail: "邮箱",
      address: "地址",
      birthday: "生日",
      register_campaign: "注册活动",
      age: "年龄",
      register_time: "注册时间",
    },

    behaviorModel: {
      behaviorModelSetting: "行为字段设置",
      modelName: "模型名称",
      primaryKey: "主键字段",
      dataSync: "数仓同步",
      enterModelName: "请输入模型名称",
      deleteError: "删除行为模型失败",
      deleteModel: "删除行为模型",
      deleteConfirm: "删除之后数据不可恢复，请确认是否删除?",
      changeBehavioEvent: "行为模型被行为事件",
      changeBehavioEventSuffix: "关联，不能删除`",
      notAllowNull: "不能为空",
      modelNameLimit: "模型名称只能为小写字母下划线数字",
      modelAliasName: "模型别名",
      enterMmodelAliasName: "请输入模型别名",
      behaviorIdentifyField: "身份识别字段映射",
      fieldName: "字段名称",
      enterFieldName: "请输入字段名称",
      showName: "显示名称",
      enterShowName: "请输入显示名称",
      fieldType: "字段类型",
      selectFieldType: "请选择字段类型",
      fieldDesc: "字段说明",
      enterFieldDesc: "请输入字段说明",
      privacyData: "隐私数据",
      fieldNameTips: "只能输入字母、数字、下划线, 且不以下划线开头",
      options: "可选项",
      value: "值",
      description: "描述",
      extras: "扩展信息",
      key: "键",
    },

    behaviorEvent: {
      editbehaviorEvent: "编辑行为事件",
      id: "行为代码",
      name: "行为名称",
      modelId: "行为模型",
      campaignId: "关联模型",
      status: "状态",
      summary: "摘要",
      createdTime: "创建时间",
      sendEvent: "发送事件",
      activity: "活动",
      activityCode: "活动编码",
      listenerId: "监听器ID",
      listenerType: "监听器类型",
      listenerFlowId: "监听器目标",
      listenerStartTime: "开始时间",
      listenerEndTime: "结束时间",
    },

    activityCategory: {
      id: "分类编码",
      enterId: "请输入分类编码",
      name: "分类名称",
      enterName: "请输入分类名称",
      summary: "备注",
    },

    channelLimitation: {
      communicateLimit: "沟通限制",
      name: "渠道名称",
      year: "年",
      month: "月",
      day: "日",
      allChannel: "全渠道",
    },

    marketingCenterConfig: {
      ossSetting: "OSS存储设置",
      type: "OSS类型",
      noDataSource: "没有可使用的数据源",
      endpoint: "服务地址",
      enterEndpoint: "请输入OSS存储服务地址",
      accessKey: "访问键",
      enterAccessKey: "请输入OSS服务访问键",
      secretKey: "访问密钥",
      enterSecretKey: "请输入OSS服务访问密钥",
      bucketName: "存储桶",
      enterBucketName: "请输入存储桶名称",
      basePath: "基础目录",
      flowSetting: "Flow设置",
      flowTemplateSyncPublishId: "Flow模板同步流程",
      selectFlowTemplateSyncPublishId: "请选择Flow模板同步流程",
      flowTemplateSyncStartId: "流程起点",
      selectFlowTemplateSyncStartId: "请选择Flow流程开始节点",
      customSetting: "定制化配置",
      campaignEditableStatus: "画布支持编辑的状态",
      manualCampaignCode: "允许手工填写画布的活动编码",
      aiEnable: "开启AI功能",
      DRAFT: "草稿",
      COMMITTED: "提交",
      REJECTED: "驳回",
      PAUSED: "暂停",
    },

    communicateChannel: {
      communicateManage: "沟通渠道管理",
      channelName: "渠道名称",
      channelType: "渠道类型",
      createTime: "创建时间",
      remark: "备注",
      sms: "短信",
      email: "邮件",
      smsManage: "短信渠道管理",
      simplifiedSms: "第三方短信",
      aliYunSms: "阿里云短信",
      messageType: "短信类型",
      userMobileField: "用户手机号字段",
      smsContent: {
        QSRQDMC: "请输入渠道名称",
        duanXinLeiXing: "短信类型",
        QXZDXLX: "请选择短信类型",
        QXZYHSJHZD: "请选择用户手机号字段",
        YHSJHZD: "用户手机号字段",
        YHZGQDXDWYBSZD: "用户在该渠道下的唯一标识字段",
        qingXuanZeZiDuan: "请选择字段",
        shenFenZiDuanQianZhui: "身份字段前缀",
        yongYu: "用于CDP身份字段逻辑",
        shenFenZiDuanLuoJi: "",
        QSRSFZDQZ: "请输入身份字段前缀",
        QXZSFKCDZD: "请选择是否可触达字段",
        YHJSGQDXXBJZD: "用户接受该渠道消息标记字段",
        lengthLimit: "信息长度限制",
        QXZYYBJHYSFJSGQDFSXXDZD:
          "请选择用于标记会员是否接受该渠道发送信息的字段",
        beiZhu: "备注",
        QSRBZXX: "请输入备注信息",
        ALYDXPZ: "阿里云短信配置",
        fuWuDiZhi: "服务地址(endpoint)",
        QSRFWDZ: "请输入服务地址",
        duanXinQianMing: "短信签名",
        QSRDXQM: "请输入短信签名",
        lianJie: "连接Id(accessKeyId)",
        qingShuRuLianJie: "请输入连接",
        lianJieMiYao: "连接密钥ID(accessKeySecret)",
        QSRLJMY: "请输入连接密钥",
        duanXinQuDao: "短信渠道",
        huoQuZiDuanShuJu: "获取字段数据",
      },
      limitSetting: {
        gouTongXianZhiPeiZhi: "沟通限制配置",
        wuRaoKaiGuan: "勿扰开关",
        kai: "开",
        guan: "关",
        wuRaoChuLiLeiXing: "勿扰处理类型",
        QXZWRCLLX: "请选择勿扰处理类型",
        kaiShiGouTongShiJian: "开始沟通时间",
        jieShuGouTongShiJian: "结束沟通时间",
        gouTongPinCiXianZhi: "沟通频次限制",
        tianJia: "添加",
        QSRZQTS: "请输入周期天数",
        tian: "天",
        QSRZQCS: "请输入周期次数",
        ci: "次",
        shanChu: "删除",
        yanShiFaSong: "延时发送",
        buFaSong: "不发送",
      },
    },

    benefitChannel: {
      points: "积分",
      coupon: "卡券",
      benefitManagement: "权益管理",
      channelName: "渠道名称",
      enterChannelName: "请输入渠道名称",
      channelType: "渠道类型",
      enterChannelType: "请输入渠道类型",
      createTime: "创建时间",
      remark: "备注",
      summary: "描述信息",
      enterSummary: "请输入描述信息",
      couponBenefitManagement: "卡券权益管理",
      pointsenefitManagement: "积分权益管理",
      couponChannel: "卡券渠道",
      pointsChannel: "积分渠道",
    },

    canvasNodeManagement: {
      canvasNodeManagement: "画布节点管理",
      huoDongFenLei: "活动分类",
      bianHao: "编号",
      tuBiao: "图标",
      jieDianMingCheng: "节点名称",
      leiXing: "类型",
      xiTong: "系统",
      kuoZhan: "扩展",
      fenLei: "分类",
      zhuangTai: "状态",
      paiXu: "排序",
      beiZhu: "备注",
      fenYeSheZhi: "分页设置",
      guoLüSheZhi: "过滤设置",
      QSRFLMC: "请输入分类名称",
      qingXuanZeZhuangTai: "请选择状态",
      huoQuLieBiao: "获取列表",
      shanChu: "删除",
      chuDianBianMa: "触点编码",
      QSRCDBM: "请输入触点编码",
      chuDianMingCheng: "触点名称",
      QSRCDMC: "请输入触点名称",
      keYong: "可用",
      tingYong: "停用",
      chuDianShuoMing: "触点说明",
      QSRCDSM: "请输入触点说明",
      xuanZeFenLei: "选择分类",
      qingShuRuFenLei: "请输入分类",
      qingXuanZeFenLei: "请选择分类",
      qingXuanZeTuBiao: "请选择图标",
      xuanZeTuBiao: "选择图标",
      beiJingSe: "背景色",
      qingShuRuBeiJingSe: "请输入背景色",
      zhuTiSe: "主题色",
      qingShuRuZhuTiSe: "请输入主题色",
      AZCXDDDSXPX: "按照从小到大的顺序排序",
      qingShuRuPaiXuZhi: "请输入排序值",
      chuDianPeiZhi: "触点配置",
      sheZhiFenLei: "设置分类",
      QXZSZFL: "请选择设置分类",
      muBanLeiXing: "模板类型",
      QSRMBLX: "请输入模板类型",
      weiXinMuBanXiaoXi: "微信模板消息",
      weiXinKeFuXiaoXi: "微信客服消息",
      qiWeiMuBanXiaoXi: "企微模板消息",
      duanXin: "短信",
      caiXin: "彩信",
      youJian: "邮件",
      chuDaZiDuan: "触达字段",
      YHZGQDXDCDZD: "用户在该渠道下的触达字段",
      RGZHDZSZLCDZD: "如果在活动中设置了触达字段",
      ZYXSYHDZDCDZD: "则优先使用活动中的触达字段",
      qingXuanZeZiDuan: "请选择字段",
      shenFenZiDuanQianZhui: "身份字段前缀MA装配触达内容",
      HCDZDYQZCSFBSZD:
        "和触达字段一起组成身份标识字段,比如手机号触达字段是: MB|130********,则这里填写: MB ",
      BRSJHCDZDS: "",
      zeZheLiTianXie: "",
      zhuangPeiNeiRong: "装配内容",
      lengthLimit:'模板信息长度限制',
      shiFouZai: "是否在MA装配触达内容",
      shi: "是",
      fou: "否",
      chuanDiKeHuShuJu: "传递客户数据",
      SFCDKHSJD: "是否传递客户数据到",
      chuanDiNeiRongShuJu: "传递内容数据",
      SFCDNRSJD: "是否传递内容数据到FLOW",
      dengDaiFanKuiJieGuo: "等待反馈结果",
      SFDDFKJGCZXHXLC: "是否等待反馈结果才执行后续流程",
      guanLianLiuCheng: "关联流程",
      zai: "在Flow中定义且部署的流程",
      ZDYQBSDLC: "",
      qingXuanZe: "请选择Flow流程Flow流程开始节点",
      liuCheng: "",
      liuChengQiDian: "流程起点",
      liuChengKaiShiJieDian: "",
      piCiShuLiang: "批次数量",
      DQQMZPCSLS: "当请求满足批次数量时,批量提交给FLOW",
      QSRPCSL: "请输入批次数量",
      piCiShuaXinShiJian: "批次刷新时间",
      DDDPCSXSJRBMZPCSL: "当达到批次刷新时间仍不满足批次数量,则直接提交给FLOW",
      zeZhiJieTiJiaoGei: "",
      QSRPCSXSJ: "请输入批次刷新时间",
      miao: "秒",
      pinCiXianZhi: "频次限制",
      DQQDSFXYPCXZ: "当前渠道是否需要频次限制",
      GTXZSBZD: "沟通限制识别字段",
      YHZGQDXXZPCDSBZD: "用户在该渠道下限制频次的识别字段",
      meiNianCiShu: "每年次数",
      YHZGQDXMNXZCDCS: "用户在该渠道下每年限制触达次数",
      meiYueCiShu: "每月次数",
      YHZGQDXMYXZCDCS: "用户在该渠道下每月限制触达次数",
      meiTianCiShu: "每天次数",
      YHZGQDXMTXZCDCS: "用户在该渠道下每天限制触达次数",
      xiTongSheZhi: "系统设置",
      huaBuJieDianGuanLi: "画布节点管理",
      bianJiHuaBuJieDian: "编辑画布节点",
      tuBiaoXuanZe: "图标选择",
      BMBKWZW: "编码不可为中文",
    },

    touchpointCategoryManagement: {
      touchpointCategoryManagement: "触点分类管理",
      id: "编码",
      name: "分类名称",
      summary: "备注",
      enterId: "请输入编码",
      enterName: "请输入分类名称",
      enterSummary: "请输入备注",
      createdTime: "创建时间",
      addCategory: "新增分组",
      editCategory: "编辑分组",
    },
    silenceManagement: {
      name: "名称",
      bianMa: "编码",
      mingCheng: "名称",
      beiZhu: "备注",
      caoZuo: "操作",
      wuRaoGuiZe: "勿扰规则",
      xinJian: "新建",
      bianJi: "编辑",
      xiTongSheZhi: "系统设置",
      huoDongFenLei: "活动分类",
      wuRaoShiDuan: "勿扰时段",
      QSRHDBZXX: "请输入活动备注信息",
      bianJiWuRaoGuiZe: "编辑勿扰规则",
      xinZengWuRaoGuiZe: "新增勿扰规则",
      qingShuRuBianMa: "请输入编码",
      QSRFLMC: "请输入分类名称",
      xinZeng: "新增",
      shanChu: "删除",
    },
  },
};
