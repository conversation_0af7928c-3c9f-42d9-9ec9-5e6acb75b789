import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import SmsNode from "./node.vue";
import SmsPannel from "./pannel.vue";

const nodeData = {
  type: "sms",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<SmsNode />`,
      components: {
        SmsNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("SmsNode", nodeData.node, true);
};

const Sms = {
  type: "sms",
  name: "短信",
  shape: "SmsNode",
  iconClass: "icon-auto-reply",
  color: "#ffffff",
  themebg: "#4594f3",
  registerNode,
  pannel: SmsPannel,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Sms;
