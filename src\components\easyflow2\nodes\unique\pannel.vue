<template>
  <div class="easyflow-pannel-unique">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item :label="$t('campaign.pannel.selectDeduplication')">
        <a-tree-select v-model="entity.uniqueField" :multiple="true" :allow-search="true" :data="flowModel" allow-clear :field-names="treeFieldStruct" :placeholder="$t('campaign.reminder.field')" @change="changeTreeSelect($event, item)">
      </a-tree-select>
      </a-form-item>

    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, getCurrentInstance } from 'vue'
import { treeFieldStruct } from "@/constant/common"
import { getFlowModelsByIds } from "@/api/campaign";
import { formatFields } from "@/utils/field"

const {
  proxy: { t }
} = getCurrentInstance();

const props = defineProps(["node", "connections", "easyflow"]);
const { node } = props;
const { connections } = props;
const { easyflow } = props;

const loading = ref(false);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;
const entity = ref({});
const flowModel = ref(null);

const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = formatFields(modelFields.fields, "");
};

const changeTreeSelect = (val, _item) => {

};

const save = () => {
  return entity.value;
};

defineExpose({
  save,
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  handelFlowModel();
});
</script>


<style lang="less" scoped></style>
