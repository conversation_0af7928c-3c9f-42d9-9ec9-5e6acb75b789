<template>
  <module edit>
    <template #action>
      <a-button v-if="simulateEnabled(entity.status)" type="primary" @click="simulate">模拟</a-button>
      <!-- <simulate-dialog ref="simulateDlg" /> -->
    </template>
    <template #main>
      <a-form ref="formRef" layout="vertical" class="general-form" :model="entity" :disabled="!editEnable">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row>
              <a-col :span="8">
                <h3>基本信息</h3>
              </a-col>
            </a-row>
            <div class="form-item-panel">
              <a-row :gutter="50">
                <a-col :span="24">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.name')"
                    :rules="[{ required: true, message: t('campaign.reminder.name') }]" field="name">
                    <a-input v-model="entity.name" :placeholder="t('campaign.reminder.name')">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.description')" field="entity.summary">
                    <a-textarea v-model="entity.summary" :placeholder="t('campaign.reminder.description')" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :label="t('campaign.task.campaignPeriod')" field="rangeValue"
                    :disabled="entity.status == 'RUNNING' || entity.status == 'PAUSED'"
                    :rules="[{ required: true, message: t('campaign.reminder.campaignPeriod') }]">
                    <a-range-picker v-model="entity.rangeValue" show-time
                      :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }" format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" @ok="handleRangeTime" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.campaignCategory')" field="category"
                    :rules="[{ required: true, message: t('campaign.reminder.campaignCategory') }]">
                    <a-select v-model="entity.category" :placeholder="t('campaign.reminder.campaignCategory')">
                      <a-option v-for="item in campaignCategories" :key="item.id" :label="item.name" :value="item.id" />
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.campaignTags')">
                    <a-select v-model="entity.tags" :placeholder="t('campaign.reminder.campaignTags')" allow-create
                      allow-clear multiple>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item :disabled="!editEnable" label="活动优先级">
                    <a-input-number v-model="entity.priority" :precision="0" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <a-row>
              <a-col :span="8">
                <h3>人群配置</h3>
              </a-col>
            </a-row>
            <div class="form-item-panel">
              <a-row :gutter="80">
                <a-col :span="8">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.selectAudiencePackage')"
                    class="form-item-select" :rules="[{ required: true, message: t('campaign.reminder.notNull') }]"
                    field="setting.audienceId">
                    <a-select v-model="entity.setting.audienceId" :allow-clear="true"
                      :placeholder="t('campaign.reminder.selectAudiencePackage')" allow-search
                      @change="handleEstimateAudience(entity.setting.audienceId)">
                      <a-option v-for="item of audiences" :key="item.id" :label="item.name" :value="item.id">
                        <template #label>
                          <div class="between-option">
                            <span>{{ item.name }}</span>
                            <span class="usage-type">{{ item.usageType }}</span>
                          </div>
                        </template>
                      </a-option>
                    </a-select>
                    <template #extra>
                      <slot name="extra">
                        <div class="estimate-number">
                          <span>{{ t('campaign.task.estimatedPopulation') }}
                            <i>{{ audienceEstimate !== "" ? audienceEstimate : "--" }}</i> 人</span>
                        </div>
                      </slot>
                    </template>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item :label="t('campaign.pannel.audienceSnapshot')" class="form-item-select"
                    :disabled="!editEnable">
                    <a-select v-model="entity.setting.snapshotId" :allow-clear="true"
                      :placeholder="t('campaign.pannel.audienceSnapshot')"
                      @change="onChangeSnapshot(entity.setting.snapshotId)">
                      <a-option v-for="item of snapshotList" :key="item.value" :value="item.value">{{ item.label
                      }}</a-option>
                    </a-select>
                    <template #extra>
                      <slot v-if="!!entity.setting.snapshotId" name="extra">
                        <div class="estimate-number">
                          <span>
                            {{ t('campaign.pannel.estimatedNumber') }}
                            <i>{{ snapshotEstimate !== "" ? snapshotEstimate : "--" }}</i>人
                          </span>
                        </div>
                      </slot>
                    </template>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item :label="t('campaign.pannel.audienceDeduplication')" :disabled="!editEnable">
                    <a-space>
                      <a-switch v-model="entity.setting.unique" type="round" />
                      <a-tree-select v-if="entity.setting.unique" v-model="entity.setting.uniqueFields" :multiple="true"
                        :allow-search="true" :data="dataModelFields" allow-clear :field-names="treeFieldStruct"
                        :placeholder="t('campaign.reminder.field')" />
                    </a-space>
                  </a-form-item>
                </a-col>

              </a-row>
              <a-row :gutter="80">
                <a-col :span="12">
                  <a-form-item :label="t('campaign.pannel.marketingFrequency')" :disabled="!editEnable"
                    :rules="[{ required: true, message: '活动频次不能为空' }]" field="setting.schedule.type">
                    <a-select v-model="entity.setting.schedule.type" class="easyflow-select" :options="frequencyOptions"
                      :placeholder="t('campaign.pannel.marketingFrequency')">
                      <template #option="{ data }">
                        <div class="easyflow-option">
                          <span class="name">{{ data?.label }}</span>
                          <i class="desc">{{ data.raw?.note }}</i>
                        </div>
                      </template>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :disabled="!editEnable" v-if="entity.setting.schedule.type === 'DATE'"
                    :label="t('campaign.pannel.triggerTime')" :rules="[{ required: true, message: '触发时间不能为空' }]"
                    field="setting.schedule.date">
                    <a-space>
                      <a-date-picker v-model="entity.setting.schedule.date" :show-time="true"
                        value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" class="form-date" :disabled="!editEnable" /> {{
                          t('campaign.pannel.startCampaign') }}
                    </a-space>
                  </a-form-item>
                  <a-form-item :disabled="!editEnable" v-if="entity.setting.schedule.type === 'CRON'"
                    :label="t('campaign.pannel.triggerTime')" :rules="[{ required: true, message: '触发时间不能为空' }]"
                    field="setting.schedule.cron">
                    <div>
                      <a-range-picker v-model="timeRange" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                        :disabled="!editEnable" @change="handleRangeTime" />
                      <CronSelect v-model:cron="entity.setting.schedule.cron" :disabled="!editEnable" />
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <a-row>
              <a-col :span="8">
                <h3>触达配置</h3>
              </a-col>
            </a-row>
            <div class="form-item-panel">
              <a-row :gutter="80">
                <a-col :span="12">
                  <a-form-item label="触达方式" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '触达方式不能为空' }]" field="setting.reachType">
                    <a-select v-model="entity.setting.reachType" class="easyflow-select" placeholder="请选择触达方式">
                      <a-option v-for="item of reachTypes" :key="item.value" :value="item.value">{{ item.label
                      }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row v-if="entity.setting.reachType == 'capability'" :gutter="80">
                <a-col :span="12">
                  <a-form-item label="营销云能力类型" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '营销云能力类型不能为空' }]" field="setting.capabilityType">
                    <a-select v-model="entity.setting.capabilityType" class="easyflow-select"
                      @change="changeCapabilityType" placeholder="请选择营销云能力类型">
                      <a-option v-for="item of capabilityType" :key="item.type" :value="item.type">{{ item.title
                      }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="营销云能力" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '营销云能力不能为空' }]" field="setting.capabilityId">
                    <a-select v-model="entity.setting.capabilityId" class="easyflow-select" placeholder="请选择营销云能力"
                      :data-loading="dataLoading" :filter-option="false" @search="handleSearchCapability"
                      @change="showContent(entity.setting.capabilityId)">
                      <a-option v-for="item of capabilities" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row v-if="entity.setting.reachType == 'flow'" :gutter="80">
                <a-col :span="12">
                  <a-form-item label="触达渠道分类" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '触达渠道分类不能为空' }]" field="setting.flowChannel">
                    <a-select v-model="entity.setting.flowChannel" class="easyflow-select" @change="changeFlowConfigId">
                      <a-option v-for="item of reachChannels" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="触达内容" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '触达内容不能为空' }]" field="setting.flowContentId">
                    <a-select v-model="entity.setting.flowContentId" class="easyflow-select" placeholder="请选择触达内容"
                      :data-loading="dataLoading" :filter-option="false" @search="handleSearchContent"
                      @change="showFlowContent">
                      <a-option v-for="item of contentList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="触达字段" :disabled="!editEnable" :rules="[{ required: true, message: '触达字段不能为空' }]"
                    field="setting.reachField">
                    <a-tree-select v-model="entity.setting.reachField" :allow-search="true" :data="dataModelFields"
                      allow-clear :field-names="treeFieldStruct" :placeholder="t('campaign.reminder.field')" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="模板示例">
                    <span class="flow-content">
                      {{ contentText }}
                    </span>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="80">
                <a-col :span="12">
                  <a-form-item label="勿扰策略" :disabled="!editEnable">
                    <SilenceSelect v-model:silenceRuleId="entity.setting.silenceRuleId" />
                    <!-- <a-select v-model="entity.setting.silenceRuleId" :allow-clear="true" placeholder="请选择勿扰策略">
                      <a-option v-for="item of silenceRules" :key="item.id" :label="item.label" :value="item.id">
                        {{ item.name }}
                      </a-option>
                    </a-select> -->
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="频次限制" :disabled="!editEnable">
                    <a-switch v-model="entity.setting.limit" type="round" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-space>
      </a-form>

      <SimulateDlg ref="simulateDlgRef" />
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useBussinessUnitStore } from "@/store";
import moment from "moment";
import { formatFields } from "@/utils/field"
import { simulateEnabled } from "@/constant/campaign"
import { frequencyOptions } from "@/constant/easyflow";
import { treeFieldStruct } from "@/constant/common"
import { capabilityType } from "@/constant/capability";
import { filters } from "@/utils/filter";
import { findAudienceList, countAudience, getCrowdSnapshot } from "@/api/audience";
import { getCampaign, modifyCampaign, queryFlowInstance } from "@/api/campaign";
import { findCommunicateList } from "@/api/communicate";
import { findCategoryList } from "@/api/category";
import { findNodeCategoryList } from "@/api/node-category";
import { findContentList, getContent } from "@/api/flow-content";
import { findCustomerModel } from "@/api/system";
import { findSilenceRuleList } from "@/api/silence";
import CronSelect from "@/components/ma/cron-select/index.vue";
import SilenceSelect from "@/components/ma/silence-select/index.vue"
import SimulateDlg from "./component/audience-simulate-dlg.vue";

export default {
  components: {
    CronSelect,
    SilenceSelect,
    SimulateDlg
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const { id, groupId } = route.query;

    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          // name: "营销活动",
          name: t('campaign.title'),
          path: "/campaign/group",
        },
        {
          // name: "活动任务",
          name: t('campaign.task.title'),
          path: `/campaign/campaign`,
          query: { id: groupId }
        },
        {
          // name: "人群包活动"
          name: t('campaign.task.package'),
        }
      ],
      mainPath: `/campaign/campaign?id=${groupId}`,
      isEdit: !!route.query.id,
    });

    const editEnable = ref(true);
    const dataLoading = ref(false);
    const changed = ref(false); // 流程是否变更

    const formRef = ref(null);
    const simulateDlgRef = ref(null);

    const owner = useBussinessUnitStore().marketingCenter;
    const entity = ref({
      setting: {
        reachType: 'flow',
        schedule: {
          date: null, // 定时时间
          cron: null, // cron表达式
          type: "NONE", // 定时类型：NONE,DELAY,DATE,CRON
          endTime: null, // 定时结束时间
          startTime: null, // 定时开始时间
        },
      }
    });
    const dataModelFields = ref([]);
    const audiences = ref(null); // 人群列表
    const snapshotList = ref(null); // 人群快照
    const audienceEstimate = ref(0); // 预估人数
    const snapshotEstimate = ref(0); // 预估人数

    const timeRange = ref(null);  // 周期触达时间范围

    const reachTypes = ref([
      { label: "营销云", value: "capability" },
      { label: "定制", value: "flow" }
    ]);

    const capabilities = ref([]);
    const campaignCategories = ref([]);

    const reachChannels = ref([]);
    const contentList = ref([]);

    const silenceRules = ref([]);
    const contentText = ref("");

    const loadCategories = async () => {
      campaignCategories.value = await findCategoryList();
      reachChannels.value = await findNodeCategoryList();
    };


    const handleSearchAudience = async (value) => {
      const params = { fields: "name,usageType", expression: "usageType ne TEST" };
      if (value) {
        params.expression = `AND name like ${value}`;
      }
      audiences.value = await findAudienceList(params);
      audiences.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
    };

    /**
     * 查询快照列表
     */
    const getSnapshotList = async (audienceId) => {
      snapshotList.value = []
      let params = {}
      if (audienceId) {
        params = { expression: `audienceId eq ${audienceId}` }
      }
      await getCrowdSnapshot(params).then(res => {
        res.content.forEach(item => {
          snapshotList.value.push({ value: item.id, num: item.num, label: moment(item.createDate).format('YYYY-MM-DD HH:mm:ss') })
        })
      })
    }

    const onChangeSnapshot = async (snapshotId) => {
      if (snapshotId) {
        snapshotEstimate.value = filters(snapshotList.value, snapshotId, "num", "value");
        entity.value.snapshotName = filters(snapshotList.value, snapshotId, "label", "value");
      }
    };
    const editEnabled = (status) => {
      return owner?.setting?.customizationSetting?.campaignEditableStatus.indexOf(
        status
      ) !== -1
    }
    const bindData = async () => {
      if (module.value.isEdit) {
        entity.value = await getCampaign(id);
        entity.value = entity.value ? entity.value : {};
        entity.value.rangeValue = [entity.value.startTime, entity.value.endTime];
        timeRange.value = [
          entity.value.setting.schedule.startTime,
          entity.value.setting.schedule.endTime,
        ];
        if (entity.value.setting.schedule.type === 'NONE') {
          entity.value.setting.schedule.type = null;
        }
        silenceRules.value = await findSilenceRuleList();
        editEnable.value = editEnabled(entity.value.status);
      }
      loadCategories();
      handleSearchAudience();
      if (entity.value.setting.audienceId) {
        await getSnapshotList(entity.value.audienceId);
        audienceEstimate.value = await countAudience(entity.value.setting.audienceId);
      }
      if (entity.value.setting.snapshotId) {
        onChangeSnapshot(entity.value.setting.snapshotId);
      }
      const dataModel = await findCustomerModel();
      dataModelFields.value = formatFields(dataModel.fields, { path: "Customer" });
      if (entity.value.setting.reachType === 'flow') {
        handleSearchContent(null);
        showFlowContent();
      } else {
        handleSearchCapability(null);
        showContent();
      }

      
    };

    const quit = async () => {
      await router.push({ path: module.value.mainPath, query: { id: groupId } });
    };

    const save = async () => {
      formRef.value.validate((err) => {
        if (!err) {
          modifyCampaign(entity.value).then(() => {
            quit();
          });
        }
      });
      changed.value = false;
    };

    const handleRangeTime = () => {
      entity.value.startTime = entity.value.rangeValue[0];
      entity.value.endTime = entity.value.rangeValue[1];
    };


    const handleEstimateAudience = async (audienceId) => {
      // 查询前判断是否还存在分组
      const item = audiences.value.find((x) => {
        return x.id === audienceId;
      });
      if (!item) {
        entity.value.audienceId = "";
        // estimate.value = "";
        return false;
      }
      audienceEstimate.value = await countAudience(audienceId);
    };

    const showContent = async () => {
      if (entity.value.setting.flowContentId) {
        var campaign = await getCampaign(entity.value.setting.capabilityId);
        contentText.value = campaign.content;
      }
    };

    const showFlowContent = async () => {
      if (entity.value.setting.flowContentId) {
        var content = await getContent(entity.value.setting.flowContentId);
        contentText.value = content.content
        entity.value.setting.flowConfigId = content.flowNodeId;
      }
    };
    const changeCapabilityType = async () => {
      entity.value.setting.capabilityId = null;
      await handleSearchCapability();
    };

    const handleSearchCapability = async (name) => {
      dataLoading.value = true;
      const params = { fields: "name,content,budgetSetting", expression: "" };
      params.expression = `type eq ${entity.value.setting.capabilityType} AND status eq ENABLED`;
      if (name) {
        params.expression += ` AND name like ${name}`;
      }
      capabilities.value = await findCommunicateList(params);
      dataLoading.value = false;
    };

    const changeFlowConfigId = async () => {
      entity.value.setting.flowContentId = null;
      await handleSearchContent();
    }

    const handleSearchContent = async (value) => {
      const params = { fields: "name,flowNodeId,flowTemplateId,status" };
      params.expression = `enabled in true`
      if (entity.value.setting.flowConfigId) {
        params.expression = `category eq ${entity.value.setting.flowChannel} AND ${params.expression}`
      }

      if (value) {
        params.expression = `( id like ${value} OR name like ${value} ) AND ${params.expression}`;
      }
      contentList.value = await findContentList(params);
      contentList.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
    };

    const simulate = async () => {
      // 查询当前模拟状态
      let debugInstanceId
      const debugInstances = await queryFlowInstance({ expression: `flowId eq ${id} AND engineType eq DEBUG AND status eq RUNNING` });
      if (debugInstances.length > 0) {
        debugInstanceId = debugInstances[0].id
      }
      // 检查保存
      if (changed.value) {
        Notification.warning({
          title: "活动变更未保存，模拟前请先保存流程",
        });
        return;
      }
      if (debugInstanceId) {
        simulateDlgRef.value.toogleSimulate(true, id, debugInstanceId);
      } else {
        await simulateDlgRef.value.show(id);
      }
      // 校验流程
      // if (!(await validate(true))) {
      //   return;
      // }

    };

    const setup = {
      t,
      dataLoading,
      formRef,
      simulateDlgRef,
      module,
      entity,
      editEnable,
      bindData,
      save,
      quit,
      dataModelFields,
      treeFieldStruct,
      frequencyOptions,
      campaignCategories,
      timeRange,
      reachTypes,
      audiences,
      snapshotList,
      audienceEstimate,
      snapshotEstimate,
      contentText,
      onChangeSnapshot,
      handleEstimateAudience,
      capabilityType,
      capabilities,
      reachChannels,
      contentList,
      silenceRules,
      changeCapabilityType,
      handleSearchCapability,
      changeFlowConfigId,
      handleSearchContent,
      showContent,
      showFlowContent,
      handleRangeTime,
      simulate,simulateEnabled
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.general-card {
  .form-title {
    font-weight: bold;
  }

  .form-item-panel {
    margin-left: 20px;
  }
}
</style>
