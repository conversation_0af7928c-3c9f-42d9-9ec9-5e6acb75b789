<template>
  <div v-if="loaded" class="container">
    <div class="actions">
      <slot name="title">
        <template v-if="module.breadcrumb">
          <a-breadcrumb class="breadcrumb">
            <template v-for="item in module.breadcrumb" :key="item.name">
              <a-breadcrumb-item v-if="item.path">
                <router-link :to="{ path: item.path, query: item.query || {} }">
                  <span style="font-size: 16px; font-weight: normal">{{ item.name }}</span>
                </router-link>
              </a-breadcrumb-item>
              <a-breadcrumb-item v-else>
                <span style="font-size: 16px; font-weight: bold">{{ item.name }}</span>
              </a-breadcrumb-item>
            </template>
          </a-breadcrumb>
          {{ module?.entityName }}
        </template>
        <template v-else>
          <span class="page-title">{{ module.isEdit ? t('global.button.edit') : t('global.button.create') }}{{ module?.entityName }}</span>
        </template>
      </slot>
      <a-space>
        <a-button v-if="reset"> {{ t('global.button.reset') }}</a-button>
        <a-button v-if="quit" type="outline" @click="quit"> {{ t('global.button.back') }}</a-button>
        <a-popconfirm :content="saveText" position="br" @ok="save">
          <a-button v-if="save" type="primary" :disabled="module.disabledSave">
            {{ isEdit ? t('global.button.save') : t('global.button.submit') }}
          </a-button>
        </a-popconfirm>
        <slot name="action">
          <!-- 编辑页面右上角按钮扩展 -->
        </slot>
      </a-space>
    </div>
    <div class="main">
      <slot name="main"></slot>
    </div>
  </div>
</template>

<script>
import {
  ref,
  inject,
  computed,
  onMounted,
  onBeforeUnmount,
  watch,
  getCurrentInstance
} from "vue";

import { useRouter, useRoute } from "vue-router";

export default {
  name: "EditTemplate",
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const route = useRoute();
    const router = useRouter();

    const edit = inject("edit");

    const module = edit?.module?.value || {
      mainPath: null,
      breadcrumb: null,
      entityName: null,
      entityIdField: "id",
      disabledSave: false,
      isEdit: false,
      ...edit.module.value,
    };

    // 面包屑导航处理
    if (!module.breadcrumb) {
      module.breadcrumb = JSON.parse(localStorage.getItem("breadcrumb"));
      if (module.breadcrumb) {
        module.breadcrumb.push({
          name: edit.module.value.entityName,
        });
      }
      watch(module, (count, prevCount) => {
        if (module.breadcrumb) {
          module.breadcrumb[module.breadcrumb.length - 1].name =
            count.entityName;
        }
      });
    }

    const formData = {};
    const quit = edit.quit
      ? edit.quit
      : () => {
        router.push({ path: module.mainPath, query: module.mainQuery });
      };
    const save = edit.save
      ? edit.save
      : null;

    const reset = edit.reset ? edit.save : false;
    const params = {};
    const paramsKey = route.query[module.entityIdField];
    const isEdit = !!paramsKey;
    const loaded = ref(false);

    params[module.entityIdField] = paramsKey;

    const saveText = computed(() => {
      return (
        edit?.saveText?.value ||
        `${isEdit.value ? t('editTemplate.willProceed') : t('editTemplate.willSubmit')}`
      );
    });

    onMounted(async () => {
      if (edit.bindData) {
        await edit.bindData();
      }
      loaded.value = true;
    });

    onBeforeUnmount(() => {
      localStorage.removeItem("breadcrumb");
    });
    return {
      t,
      quit,
      save,
      isEdit,
      saveText,
      reset,
      module,
      formData,
      loaded
    };
  },
};
</script>

<style scoped lang="less">
.breadcrumb {
  margin-left: 15px;
}

.page-title {
  padding: 14px 20px;
  display: flex;
  align-items: center;
}
</style>
