<template>
  <div class="silence-select">
    <a-row>
      <a-col :span="24">
        <a-select v-model="silenceRuleId" :allow-clear="true" placeholder="请选择勿扰策略" @change="changeSilence">
          <a-option v-for="item of silenceRules" :key="item.id" :label="item.label" :value="item.id">
            {{ item.name }}
          </a-option>
        </a-select>
      </a-col>
      <a-col v-for="time of silence.timeRanges" :key="time" class="time-item" :span="24">
        {{ time.start }} - {{ time.end }}
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { findSilenceRuleList } from "@/api/silence";

const emit = defineEmits(["update:silenceRuleId"]);

const props = defineProps(["silenceRuleId"]);

const silenceRuleId = ref(props.silenceRuleId)

const silenceRules = ref([]);
const silence = ref({});

const changeSilence = () => {
  if (silenceRuleId.value) {
    silence.value = silenceRules.value.find((it) => it.id === silenceRuleId.value);
  } else {
    silence.value = {};
  }
  emit('update:silenceRuleId', silenceRuleId.value)
}
onMounted(async () => {
  silenceRules.value = await findSilenceRuleList();
  silenceRuleId.value = props.silenceRuleId
});
</script>

<style lang="less" scoped>
.silence-select {
  margin: 5px 0;
  width: 100%;
}

.time-item {
  margin: 4px;
  text-align: center;
}
</style>
