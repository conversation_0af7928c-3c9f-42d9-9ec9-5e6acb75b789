<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'start' })
    const config = {
      title: item.name || "开始",
      summary: item.name || "开始节点",
      iconClass: item.icon || "icon-start",
      nodeClass: "easyflow-node-start",
      headerColor: item.themeColor|| "#39bcc5",
      headerBgColor: item.themeColor || "#39bcc5",
      background: item.background || "#f2fcfa",
      fixed: true,
      ignoreMonitor: true,
      getBrief,
    };

    provide("node", config);
    return config;
  },
  data() {
    return {
      monitor: false,
    };
  },
};
</script>
