import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

// Audience
export function findAudienceGroupPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/audience-group`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function findAudienceGroupList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/audience-group/list`, {
    params,
  });
}

export function findAudienceGroupItem(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/audience-group/${id}`);
}

export function createAudienceGroup(info?: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/audience-group`,
    info
  );
}

export function modifyAudienceGroup(info?: any) {
  return axios.put(
    `/api/ma-manage/${tenantId}/${buCode}/audience-group`,
    info
  );
}

export function deleteAudienceGroup(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/audience-group/${id}`);
}
