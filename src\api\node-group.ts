/**
 * 触点分类接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findNodeGroupPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/node-group`, {
    params: {
      ...params,
      ...query
    }
  });
}

export function findNodeGroupList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/node-group/list`, {
    params
  });
}

export function saveNodeGroup(isEdit: any, info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/node-group`;
  return !isEdit ? axios.post(uri, info) : axios.put(uri, info);
}

// export function deleteNodeGroup(id: string) {
//   return axios.delete(
//     `/api/ma-manage/${tenantId}/${buCode}/node-group/${id}`
//   );
// }

// export function getNodeGroup(id: string) {
//   return axios.get(`/api/ma-manage/${tenantId}/${buCode}/node-group/${id}`);
// }
