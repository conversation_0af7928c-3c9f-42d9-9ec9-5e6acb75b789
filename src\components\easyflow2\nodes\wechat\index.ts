import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import WechatNode from "./node.vue";
import WechatPannel from "./pannel.vue";

const nodeData = {
  type: "wechat",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<WechatNode />`,
      components: {
        WechatNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("WechatNode", nodeData.node, true);
};

const Wechat = {
  type: "wechat",
  name: "微信",
  shape: "WechatNode",
  iconClass: "icon-scrm-l",
  color: "#ffffff",
  themebg: "#4594f3",
  registerNode: registerNode,
  pannel: WechatPannel,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Wechat;
