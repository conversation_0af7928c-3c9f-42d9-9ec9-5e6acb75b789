<template>
  <div style="display: flex">
    <div style="display: flex; flex-direction: column; width: 280px">
      <div style="display: flex">
        <a-input-search
          v-model="searchKey"
          style="margin-bottom: 8px; max-width: 240px"
        />
        <div style="width: 10px"></div>
        <a-button
          type="primary"
          :disabled="Boolean(!dataModel.own && dataModel.id)"
          @click="addChildNode()"
        >
          <template #icon>
            <icon-plus />
          </template>
        </a-button>
      </div>
      <div style="height: calc(100vh - 310px); overflow: auto">
        <a-tree
          block-node
          :show-line="true"
          :default-expand-all="false"
          style="width: 100%"
          :data="treeData"
          :field-names="treeFiledName"
          @select="selectNode"
        >
          <template #extra="nodeData">
            <span v-if="nodeData.isKey" class="tag-key">{{t('systemSetting.basicSettings.primaryKey')}}</span>
            <IconPlus
              v-if="nodeData.type === 'NESTED' && nodeData.isAdd"
              class="field-plus-btn"
              @click="addChildNode(nodeData)"
            />
            <IconDelete
              v-if="nodeData.isAdd || isRemove"
              class="field-del-btn"
              @click="removeChildNode(nodeData)"
            />
          </template>
        </a-tree>
      </div>
    </div>
    <a-form
      ref="formRef"
      style="flex: 1; padding-left: 30px; max-width: 550px"
      :model="form"
    >
      <a-form-item
        field="name"
        :label="t('systemSetting.basicSettings.id')"
        :rules="[
          { required: true, message: t('systemSetting.basicSettings.notAllowNull') },
          {
            match: /^(?!_)(?!.*?_$)[a-zA-Z0-9_]+$/,
            message: t('systemSetting.basicSettings.idTips')
          }
        ]"
        :validate-trigger="['change', 'blur']"
      >
        <a-input
          v-model="form.name"
          :placeholder=" t('systemSetting.basicSettings.enterId')"
          :disabled="!form.isAdd"
          @blur="blurChange(form)"
          @change="changeName(form)"
        />
      </a-form-item>
      <a-form-item
        field="aliasName"
        :rules="[{ required: true, message:  t('systemSetting.basicSettings.notAllowNull') }]"
        :validate-trigger="['change', 'input']"
        :label=" t('systemSetting.basicSettings.name')"
      >
        <a-input
          v-model="form.aliasName"
          :placeholder="t('systemSetting.basicSettings.enterName')"
          :disabled="!form.isAdd"
        />
      </a-form-item>
      <a-form-item
        field="type"
        :rules="[{ required: true, message:  t('systemSetting.basicSettings.notAllowNull') }]"
        :validate-trigger="['change', 'input']"
        :label=" t('systemSetting.basicSettings.type') "
      >
        <a-select
          v-model="form.type"
          :placeholder="t('systemSetting.basicSettings.selectType') "
          :disabled="!form.isAdd"
        >
          <a-option value="string">{{t('systemSetting.basicSettings.string')}}</a-option>
          <a-option value="integer">{{t('systemSetting.basicSettings.integer')}}</a-option>
          <a-option value="long">{{t('systemSetting.basicSettings.long')}}</a-option>
          <a-option value="double">{{t('systemSetting.basicSettings.double')}}</a-option>
          <a-option value="date">{{t('systemSetting.basicSettings.date')}}</a-option>
          <a-option value="boolean">{{t('systemSetting.basicSettings.boolean')}}</a-option>
          <a-option value="nested">{{t('systemSetting.basicSettings.nested')}}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="allowNull" :label="t('systemSetting.basicSettings.isNull')">
        <a-switch
          v-model="form.allowNull"
          type="round"
          class="form-switch"
          :disabled="!form.isAdd"
        >
          <template #checked> {{t('systemSetting.basicSettings.yes')}}</template>
          <template #unchecked> {{t('systemSetting.basicSettings.no')}}</template>
        </a-switch>
      </a-form-item>
      <a-form-item field="arrayType" :label="t('systemSetting.basicSettings.isArray')" :disabled="!form.isAdd">
        <a-switch v-model="form.array" type="round" class="form-switch">
          <template #checked> {{t('systemSetting.basicSettings.yes')}}</template>
          <template #unchecked> {{t('systemSetting.basicSettings.no')}}</template>
        </a-switch>
      </a-form-item>

      <a-form-item field="identify" :label="t('systemSetting.basicSettings.identification')">
        <a-switch v-model="form.identify" type="round" class="form-switch">
          <template #checked> {{t('systemSetting.basicSettings.yes')}}</template>
          <template #unchecked> {{t('systemSetting.basicSettings.no')}}</template>
        </a-switch>
        <a-input
          v-if="form.identify"
          v-model="form.identifyPerfix"
          :placeholder="t('systemSetting.basicSettings.identification')"
        />
      </a-form-item>
      <a-form-item field="listDisplay" :label="t('systemSetting.basicSettings.showingByList')">
        <a-switch v-model="form.listDisplay" type="round" class="form-switch">
          <template #checked> {{t('systemSetting.basicSettings.yes')}}</template>
          <template #unchecked> {{t('systemSetting.basicSettings.no')}}</template>
        </a-switch>
      </a-form-item>
      <a-form-item field="userForSearch" :label="t('systemSetting.basicSettings.useForQuery')">
        <a-switch v-model="form.useForSearch" type="round" class="form-switch">
          <template #checked> {{t('systemSetting.basicSettings.yes')}}</template>
          <template #unchecked> {{t('systemSetting.basicSettings.no')}}</template>
        </a-switch>
        <a-select
          v-if="form?.useForSearch"
          v-model="form.operator"
          :placeholder="t('systemSetting.basicSettings.selectUseForQuery')"
        >
          <a-option value="eq">{{t('systemSetting.basicSettings.eq')}}</a-option>
          <a-option value="like">{{t('systemSetting.basicSettings.like')}}</a-option>
          <a-option value="lt">{{t('systemSetting.basicSettings.lt')}}</a-option>
          <a-option value="gt">{{t('systemSetting.basicSettings.gt')}}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="purpose" :label="t('systemSetting.basicSettings.fieldUsage')">
        <a-select v-model="form.purpose" :placeholder="t('systemSetting.basicSettings.selectFieldUsage')">
          <a-option value="name">{{t('systemSetting.basicSettings.realName')}}</a-option>
          <a-option value="nickname">{{t('systemSetting.basicSettings.nickname')}}</a-option>
          <a-option value="gender">{{t('systemSetting.basicSettings.gender')}}</a-option>
          <a-option value="mobile">{{t('systemSetting.basicSettings.mobile')}}</a-option>
          <a-option value="wechat">{{t('systemSetting.basicSettings.wechat')}}</a-option>
          <a-option value="mail">{{t('systemSetting.basicSettings.mail')}}</a-option>
          <a-option value="address">{{t('systemSetting.basicSettings.address')}}</a-option>
          <a-option value="age">{{t('systemSetting.basicSettings.age')}}</a-option>
          <a-option value="birthday">{{t('systemSetting.basicSettings.birthday')}}</a-option>
          <a-option value="register_campaign">{{t('systemSetting.basicSettings.register_campaign')}}</a-option>
          <a-option value="register_time">{{t('systemSetting.basicSettings.register_time')}}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="defaultValue" :label="t('systemSetting.basicSettings.defaultValue')">
        <a-input v-model="form.defaultValue" :placeholder="t('systemSetting.basicSettings.enterDefaultValue')" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { ref, computed,getCurrentInstance } from "vue";
import { Message } from "@arco-design/web-vue";

const DEFAULT_DATA = {
  name: "",
  aliasName: "",
  type: "",
  array: false,
  optional: false,
  fields: [],
  credentialCode: "",
  dateFormat: "",
  onlyStorage: false,
  useForTag: false,
  useForCredential: false,
  path: "",
  purpose: "",
  defaultValue: "",
  identify: false,
  identifyPerfix: "",
  listDisplay: false,
  useForSearch: false,
  operator: ""
};

export default {
  props: {
    dataModel: {
      type: Object,
      default() {
        return {
          fields: [],
          purposeFields: [],
          pkName: "",
          own: false
        };
      }
    },
    isRemove: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const searchKey = ref("");
    const treeData = computed(() => {
      if (!searchKey.value) return props.dataModel.fields;
      return searchData(searchKey.value);
    });

    function searchData(keyword) {
      const loop = (data) => {
        const result = [];
        data.forEach((item) => {
          if (
            item.aliasName.toLowerCase().indexOf(keyword.toLowerCase()) > -1
          ) {
            result.push({ ...item });
          } else if (item.fields) {
            const filterData = loop(item.fields);
            if (filterData.length) {
              result.push({
                ...item,
                fields: filterData
              });
            }
          }
        });
        return result;
      };

      return loop(props.dataModel.fields);
    }

    const treeFiledName = {
      key: "path",
      title: "aliasName",
      children: "fields"
    };

    const parentNode = ref({});

    const form = ref(DEFAULT_DATA);
    const curItem = ref(null);

    const formRef = ref(null);

    const editNewNode = ref({});
    const showd = false;

    const selectNode = async (key, node) => {
      formRef.value.clearValidate();
      form.value = node.node;
      form.value.useForSearch = form.value?.useForSearch
        ? form.value?.useForSearch
        : false;
      curItem.value = form.value;
    };

    // 判断是否重复编码
    const cont = ref(0);
    const repeatCode = (data, name) => {
      data.forEach((item) => {
        if (item.name === name) {
          cont.value += 1;
        }
        if (item?.fields?.length > 0) {
          repeatCode(item.fields, name);
        }
      });
      return cont.value > 1;
    };
    const blurChange = (item) => {
      cont.value = 0;
      const is = repeatCode(treeData.value, item.name);
      if (is) {
        Message.warning(t('systemSetting.basicSettings.fieldExisted'));
        item.name = "";
      }
    };

    return {
      t,
      searchKey,
      treeData,
      treeFiledName,
      parentNode,
      form,
      editNewNode,
      showd,
      curItem,
      selectNode,
      blurChange,
      formRef
    };
  },
  methods: {
    async addChildNode(nodeData) {
      if (
        this.curItem?.name === "" ||
        this.curItem?.aliasName === "" ||
        this.curItem?.type === ""
      ) {
        const res = await this.$refs.formRef.validate();
        if (res) {
          this.$notification.error("您存在未编辑的新增必填字段！");
        }
      } else {
        this.parentNode = nodeData;
        this.reset(nodeData ? `${nodeData.path}__` : `${new Date()}_`);
        this.editNewNode = this.form;
        if (nodeData) {
          nodeData.fields.push({
            ...this.form,
            isAdd: true
          });
        } else {
          this.dataModel.fields.push({
            ...this.form,
            isAdd: true
          });
        }
        this.curItem = {
          ...this.form,
          isAdd: true
        };
      }
    },
    removeChildNode(nodeData) {
      const { fields } = this.dataModel;
      this.removeItem(fields, nodeData);
    },
    // 删除回调
    removeItem(data, curItem) {
      const index = data.findIndex((item) => {
        return item.path === curItem.path;
      });

      if (index >= 0) {
        data.splice(index, 1);
        // 判断删除的是否是当前编辑的
        if (this.curItem.path === curItem.path) {
          this.curItem = null;
        }
      } else {
        data.forEach((item) => {
          this.removeItem(item.fields, curItem);
        });
      }
    },
    changeName(nodeData) {
      const temp = nodeData.path.split("__");
      let tempStr;
      for (let i = 0; i < temp.length; i += 1) {
        if (i === 0) {
          if (temp.length === 1) {
            tempStr = nodeData.name;
          } else {
            tempStr = temp[i];
          }
        } else if (i + 1 === temp.length) {
          tempStr = `${tempStr}__${nodeData.name}`;
        } else {
          tempStr = `${tempStr}__${temp[i]}`;
        }
      }
      nodeData.path = tempStr;
    },
    reset(path) {
      this.form = DEFAULT_DATA;
    }
  }
};
</script>

<style scoped lang="less">
::v-deep(.arco-tree-node-title-text) {
  height: 22px;
}

.field-plus-btn {
  position: absolute;
  right: 24px;
  font-size: 12px;
  top: 10px;
  color: #3370ff;
}
.field-del-btn {
  position: absolute;
  right: 8px;
  font-size: 12px;
  top: 10px;
  color: #3370ff;
}
.form-switch {
  margin-right: 10px;
}
.tag-key {
  color: rgb(var(--arcoblue-6));
  background-color: rgb(var(--arcoblue-1));
  border: 1px solid transparent;
  padding: 0 5px;
  font-size: 10px;
  zoom: 0.8;
  position: absolute;
  right: 60px;
}
</style>
