<template>
  <module edit>
    <template #action>
      <a-button v-if="simulateEnabled(entity.status)" type="primary" @click="simulate">模拟</a-button>
    </template>
    <template #main>
      <a-form ref="formRef" layout="vertical" class="general-form" :model="entity" :disabled="!editEnable">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row>
              <a-col :span="8">
                <h3>基本信息</h3>
              </a-col>
            </a-row>
            <div class="form-item-panel">
              <a-row :gutter="50">
                <a-col :span="24">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.name')"
                    :rules="[{ required: true, message: t('campaign.reminder.name') }]" field="name">
                    <a-input v-model="entity.name" :placeholder="t('campaign.reminder.name')">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.description')" field="entity.summary">
                    <a-textarea v-model="entity.summary" :placeholder="t('campaign.reminder.description')" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :label="t('campaign.task.campaignPeriod')" field="rangeValue"
                    :disabled="entity.status == 'RUNNING' || entity.status == 'PAUSED'"
                    :rules="[{ required: true, message: t('campaign.reminder.campaignPeriod') }]">
                    <a-range-picker v-model="entity.rangeValue" show-time
                      :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }" format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" @ok="handleRangeTime" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.campaignCategory')" field="category"
                    :rules="[{ required: true, message: t('campaign.reminder.campaignCategory') }]">
                    <a-select v-model="entity.category" :placeholder="t('campaign.reminder.campaignCategory')">
                      <a-option v-for="item in campaignCategories" :key="item.id" :label="item.name" :value="item.id" />
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item :disabled="!editEnable" :label="t('campaign.task.campaignTags')">
                    <a-select v-model="entity.tags" :placeholder="t('campaign.reminder.campaignTags')" allow-create
                      allow-clear multiple>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item :disabled="!editEnable" label="活动优先级">
                    <a-input-number v-model="entity.priority" :precision="0" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <a-row>
              <a-col :span="8">
                <h3>客户群配置</h3>
              </a-col>
            </a-row>
            <div class="form-item-panel">
              <a-row :gutter="80">
                <a-col :span="8">
                  <a-form-item label="选择推送方式" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '推送方式不能为空' }]" field="setting.pushMethod">
                    <a-select v-model="entity.setting.pushMethod" class="easyflow-select" placeholder="请选择推送方式"
                      @change="handlePushMethodChange">
                      <a-option value="customerGroup">客户群</a-option>
                      <a-option value="groupOwner">群主</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item :label="t('campaign.pannel.marketingFrequency')" :disabled="!editEnable"
                    :rules="[{ required: true, message: '活动频次不能为空' }]" field="setting.schedule.type">
                    <a-select v-model="entity.setting.schedule.type" class="easyflow-select" :options="frequencyOptions"
                      :placeholder="t('campaign.pannel.marketingFrequency')">
                      <template #option="{ data }">
                        <div class="easyflow-option">
                          <span class="name">{{ data?.label }}</span>
                          <i class="desc">{{ data.raw?.note }}</i>
                        </div>
                      </template>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item :disabled="!editEnable" v-if="entity.setting.schedule.type === 'DATE'"
                    :label="t('campaign.pannel.triggerTime')" :rules="[{ required: true, message: '触发时间不能为空' }]"
                    field="setting.schedule.date">
                    <a-space>
                      <a-date-picker v-model="entity.setting.schedule.date" :show-time="true"
                        value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" class="form-date" :disabled="!editEnable" /> {{
                          t('campaign.pannel.startCampaign') }}
                    </a-space>
                  </a-form-item>
                  <a-form-item :disabled="!editEnable" v-if="entity.setting.schedule.type === 'CRON'"
                    :label="t('campaign.pannel.triggerTime')" :rules="[{ required: true, message: '触发时间不能为空' }]"
                    field="setting.schedule.cron">
                    <div>
                      <a-range-picker v-model="timeRange" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                        :disabled="!editEnable" @change="handleRangeTime" />
                      <CronSelect v-model:cron="entity.setting.schedule.cron" :disabled="!editEnable" />
                    </div>
                  </a-form-item>
                </a-col>
                </a-row>
                
                <!-- 群主推送方式的选择 -->
                <a-row v-if="entity.setting.pushMethod === 'groupOwner'" :gutter="80">
                  <a-col :span="8">
                    <a-form-item label="选择群主" :disabled="!editEnable" class="form-item-select"
                      :rules="[{ required: true, message: '群主不能为空' }]" field="setting.groupOwnerId">
                      <a-select v-model="entity.setting.groupOwnerId" class="easyflow-select" placeholder="请选择群主"
                        @change="handleGroupOwnerChange">
                        <a-option v-for="owner in groupOwners" :key="owner.userId" :value="owner.userId">
                          {{ owner.name }}
                        </a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row v-if="entity.setting.pushMethod === 'groupOwner'" :gutter="80">
                  <a-col :span="8">
                    <a-button type="primary" @click="openCustomerGroupModal">选择客户群</a-button>
                  </a-col>
                </a-row>

                <!-- 客户群推送方式的选择 -->
                <a-row v-if="entity.setting.pushMethod === 'customerGroup'" :gutter="80">
                  <a-col :span="8">
                    <a-button type="primary" class="select-customer-group-btn" @click="openCustomerGroupModal">选择客户群</a-button>
                  </a-col>
                </a-row>

              <!-- 已选客户群展示区域 -->
              <a-row  v-if="selectedGroups.length > 0" :gutter="80">
                <a-col :span="24">
                  <a-form-item >
                    <a-list class="selected-groups">
                      <template #header>
                        已选 {{ selectedGroups.length }} 个客户群
                      </template>
                      <a-list-item v-for="group in selectedGroups" :key="group.id" class="selected-group-item">
                        <a-list-item-meta>
                          <template #title>
                            {{ group.name }}
                          </template>
                        </a-list-item-meta>
                      </a-list-item>
                    </a-list>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <a-row>
              <a-col :span="8">
                <h3>触达配置</h3>
              </a-col>
            </a-row>
            <div class="form-item-panel">
              <a-row :gutter="80">
                <a-col :span="12">
                  <a-form-item label="触达方式" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '触达方式不能为空' }]" field="setting.reachType">
                    <a-select v-model="entity.setting.reachType" class="easyflow-select" placeholder="请选择触达方式" :disabled="true">
                      <!-- <a-option v-for="item of reachTypes" :key="item.value" :value="item.value">{{ item.label
                      }}</a-option> -->
                      <a-option value="flow">定制</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- <a-row  :gutter="80">
                <a-col :span="12">
                  <a-form-item label="营销云能力类型" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '营销云能力类型不能为空' }]" field="setting.capabilityType">
                    <a-select v-model="entity.setting.capabilityType" class="easyflow-select"
                      @change="changeCapabilityType" placeholder="请选择营销云能力类型">
                      <a-option v-for="item of capabilityType" :key="item.type" :value="item.type">{{ item.title
                      }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="营销云能力" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '营销云能力不能为空' }]" field="setting.capabilityId">
                    <a-select v-model="entity.setting.capabilityId" class="easyflow-select" placeholder="请选择营销云能力"
                      :data-loading="dataLoading" :filter-option="false" @search="handleSearchCapability"
                      @change="showContent(entity.setting.capabilityId)">
                      <a-option v-for="item of capabilities" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row> -->

              <a-row  :gutter="80">
                <a-col :span="12">
                  <a-form-item label="触达渠道分类" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '触达渠道分类不能为空' }]" field="setting.flowChannel">
                    <a-select v-model="entity.setting.flowChannel" class="easyflow-select" @change="changeFlowConfigId">
                      <a-option v-for="item of reachChannels" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="触达内容" :disabled="!editEnable" class="form-item-select"
                    :rules="[{ required: true, message: '触达内容不能为空' }]" field="setting.flowContentId">
                    <a-select v-model="entity.setting.flowContentId" class="easyflow-select" placeholder="请选择触达内容"
                      :data-loading="dataLoading" :filter-option="false" @search="handleSearchContent"
                      @change="showFlowContent">
                      <a-option v-for="item of contentList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="触达字段" :disabled="!editEnable" :rules="[{ required: true, message: '触达字段不能为空' }]"
                    field="setting.reachField">
                    <a-tree-select v-model="entity.setting.reachField" :allow-search="true" :data="dataModelFields"
                      allow-clear :field-names="treeFieldStruct" :placeholder="t('campaign.reminder.field')" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="模板示例">
                    <span class="flow-content">
                      {{ contentText }}
                    </span>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="80">
                <a-col :span="12">
                  <a-form-item label="勿扰策略" :disabled="!editEnable">
                    <SilenceSelect v-model:silenceRuleId="entity.setting.silenceRuleId" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="频次限制" :disabled="!editEnable">
                    <a-switch v-model="entity.setting.limit" type="round" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-space>
      </a-form>

      <!-- 新增：选择客户群弹窗 -->
      <a-modal v-model:visible="customerGroupModalVisible" title="选择客户群" width="600px" @ok="handleSaveCustomerGroups" @cancel="handleCancelCustomerGroups">
        <a-form layout="horizontal" :model="searchForm">
          <a-row :gutter="16">
            <a-col :span="16">
              <a-form-item label="客户群" name="keyword">
                <a-input v-model="searchForm.keyword" placeholder="请输入关键字" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-button type="primary" @click="handleSearch">搜索</a-button>
            </a-col>
          </a-row>
        </a-form>

        <a-table
          :data="filteredCustomerGroups"
          :loading="loading"
          :pagination="false"
          :bordered="true"
          size="small"
          style="margin-top: 16px;"
        >
          <template #columns>
            <a-table-column title="客户群" data-index="name">
              <template #cell="{ record }">
                <a-checkbox v-model="record.selected" @change="(val) => toggleSelect(record, val)">
                  {{ record.name }}
                </a-checkbox>
              </template>
            </a-table-column>
            <a-table-column title="群主" data-index="ownerName">
              <template #cell="{ record }">
                {{ record.ownerName || '未知' }}
              </template>
            </a-table-column>
          </template>
        </a-table>

        <!-- 分页组件 -->
        <div class="pagination-container" style="text-align: center; margin-top: 16px;">
          <a-pagination
            v-model:current="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            show-size-changer
            show-jumper
            showTotal="(total) => `共 ${total} 条`"
            @change="handlePageChange"
            @showSizeChange="handlePageSizeChange"
          />
        </div>

        <template #footer>
          <a-button @click="handleCancelCustomerGroups">取消</a-button>
          <a-button type="primary" @click="handleSaveCustomerGroups">保存</a-button>
        </template>
      </a-modal>

      <SimulateDlg ref="simulateDlgRef" />
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Notification } from "@arco-design/web-vue";
import { useBussinessUnitStore } from "@/store";
import moment from "moment";
import { formatFields } from "@/utils/field"
import { simulateEnabled } from "@/constant/campaign"
import { frequencyOptions } from "@/constant/easyflow";
import { treeFieldStruct } from "@/constant/common"
import { capabilityType } from "@/constant/capability";
import { filters } from "@/utils/filter";
import CronSelect from "@/components/ma/cron-select/index.vue";
import SilenceSelect from "@/components/ma/silence-select/index.vue"
import { getCampaign, modifyCampaign, createCampaign, queryFlowInstance } from "@/api/campaign";
import { findCategoryList } from "@/api/category";
import { findCommunicateList } from "@/api/communicate";
import { findNodeCategoryList } from "@/api/node-category";
import { findContentList, getContent } from "@/api/flow-content";
import { findCustomerModel } from "@/api/system";
import { findSilenceRuleList } from "@/api/silence";
import { getChatInfo } from "@/api/wechatgroup";
import { getGroup } from "@/api/group";
import SimulateDlg from "./component/audience-simulate-dlg.vue";

export default {
  components: {
    CronSelect,
    SilenceSelect,
    SimulateDlg
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const { id, groupId } = route.query;

    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: t('campaign.title'),
          path: "/campaign/group",
        },
        {
          name: t('campaign.task.title'),
          path: `/campaign/campaign`,
          query: { id: groupId }
        },
        {
          name: "企微客户群活动",
        }
      ],
      mainPath: `/campaign/campaign?id=${groupId}`,
      isEdit: !!route.query.id,
    });

    const editEnable = ref(true);
    const changed = ref(false);
    const dataLoading = ref(false);

    const formRef = ref(null);
    const simulateDlgRef = ref(null);
    const timeRange = ref(null);  // 周期触达时间范围

    const owner = useBussinessUnitStore().marketingCenter;
    const entity = ref({
      groupLeader: '', // 推送方式字段：空字符串表示客户群，有值表示群主ID
      setting: {
        sendType: 'immediate',
        messageType: 'text',
        mentionAll: false,
        messageContent: '',
        customerGroupId: null,
        sendTime: null,
        pushMethod: null,
        groupOwnerId: null, // 新增群主ID字段
        reachType: 'flow', // 修改默认值为flow
        schedule: {
          date: null, // 定时时间
          cron: null, // cron表达式
          type: "NONE", // 定时类型：NONE,DELAY,DATE,CRON
          endTime: null, // 定时结束时间
          startTime: null, // 定时开始时间
        },
      }
    });

    const campaignCategories = ref([]);
    const customerGroups = ref([]);
    const customerGroupEstimate = ref(0);
    
    // 新增触达配置相关数据
    const dataModelFields = ref([]);
    const reachTypes = ref([
      { label: "营销云", value: "capability" },
      { label: "定制", value: "flow" }
    ]);
    const capabilities = ref([]);
    const reachChannels = ref([]);
    const contentList = ref([]);
    const silenceRules = ref([]);
    const contentText = ref("");

    const loadCategories = async () => {
      campaignCategories.value = await findCategoryList();
      reachChannels.value = await findNodeCategoryList(); // 加载触达渠道分类
    };

    

    const handleSelectCustomerGroup = (groupId) => {
      const group = customerGroups.value.find(g => g.id === groupId);
      if (group) {
        customerGroupEstimate.value = group.memberCount;
      }
    };

    const editEnabled = (status) => {
      return owner?.setting?.customizationSetting?.campaignEditableStatus.indexOf(
        status
      ) !== -1
    }

    // 新增：客户群弹窗相关变量
    const customerGroupModalVisible = ref(false);
    const searchForm = ref({
      keyword: ''
    });
    const loading = ref(false);
    const currentPage = ref(1);
    const pageSize = ref(20);
    const total = ref(0);
    const filteredCustomerGroups = ref([]);

    // 已选客户群列表
    const selectedGroups = ref([]);
    
    // 临时存储弹窗中选中的客户群
    const tempSelectedGroups = ref([]);

    // 群主数据
    const groupOwners = ref([]);
    
    // campaign group信息
    const campaignGroup = ref(null);

    // 加载客户群数据
    const loadCustomerGroups = async () => {
      loading.value = true;
      try {
        // 从API获取客户群数据
        const params = {
          code: 'funLoopLand', // 根据实际业务情况设置code
          page: currentPage.value,
          limit: pageSize.value
        };
        
        const response = await getChatInfo(params);
        
        // 转换API返回的数据格式以适配现有逻辑
        let customerGroupsData = response.list.map(item => ({
          id: item.chatId,
          name: item.name,
          groupOwnerId: item.ownerId,
          ownerName: item.ownerName,
          selected: tempSelectedGroups.value.some(sg => sg.id === item.chatId)
        }));
        
        // 使用ownerId和ownerName字段构建群主数据
        const uniqueOwners = [...new Map(response.list.map(item => [item.ownerId, { userId: item.ownerId, name: item.ownerName }])).values()];
        
        groupOwners.value = uniqueOwners;
        
        // 根据推送方式和群主过滤数据
        if (entity.value.setting.pushMethod === 'groupOwner' && entity.value.setting.groupOwnerId) {
          customerGroupsData = customerGroupsData.filter(group => 
            group.groupOwnerId === entity.value.setting.groupOwnerId
          );
        }
        
        // 应用搜索过滤 - 在前端根据关键字过滤数据
        if (searchForm.value.keyword) {
          customerGroupsData = customerGroupsData.filter(group => 
            group.name.toLowerCase().includes(searchForm.value.keyword.toLowerCase())
          );
        }
        
        total.value = customerGroupsData.length;
        filteredCustomerGroups.value = customerGroupsData;
        
      } finally {
        loading.value = false;
      }
    };

    // 搜索客户群
    const handleSearch = () => {
      currentPage.value = 1;
      loadCustomerGroups();
    };

    // 分页处理
    const handlePageChange = (page) => {
      currentPage.value = page;
      loadCustomerGroups();
    };

    const handlePageSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1;
      loadCustomerGroups();
    };

    // 切换选中状态
    const toggleSelect = (record, checked) => {
      if (checked) {
        // 添加到已选列表
        if (!tempSelectedGroups.value.some(g => g.id === record.id)) {
          tempSelectedGroups.value.push({
            id: record.id,
            name: record.name,
            groupOwnerId: record.groupOwnerId,
            ownerName: record.ownerName
          });
        }
      } else {
        // 从已选列表移除
        tempSelectedGroups.value = tempSelectedGroups.value.filter(g => g.id !== record.id);
      }

    };

    // 打开弹窗
    const openCustomerGroupModal = () => {
      customerGroupModalVisible.value = true;
      currentPage.value = 1; // 重置页码
      searchForm.value.keyword = ''; // 重置搜索关键字

      // 初始化临时选中列表为当前已选列表
      tempSelectedGroups.value = [...selectedGroups.value];

      loadCustomerGroups();
    };

    // 保存客户群
    const handleSaveCustomerGroups = () => {
      // 更新 entity 中的 customerGroupId
      entity.value.setting.customerGroupId = tempSelectedGroups.value.map(g => g.id);
      
      // 更新页面展示的已选客户群
      selectedGroups.value = [...tempSelectedGroups.value];
      
      customerGroupModalVisible.value = false;
      
    };

    // 取消
    const handleCancelCustomerGroups = () => {
      customerGroupModalVisible.value = false;

      // 恢复临时选中列表为原始状态
      tempSelectedGroups.value = [...selectedGroups.value];

      // 恢复当前显示的客户群选中状态
      filteredCustomerGroups.value = filteredCustomerGroups.value.map(group => ({
        ...group,
        selected: selectedGroups.value.some(sg => sg.id === group.id)
      }));
    };

    // 移除已选客户群
    const removeGroup = (id) => {
      selectedGroups.value = selectedGroups.value.filter(g => g.id !== id);

      // 更新 entity 中的 customerGroupId
      entity.value.setting.customerGroupId = selectedGroups.value.map(g => g.id);

      // 更新所有客户群数据中的选中状态

      // 同时更新弹窗中的选中状态
      filteredCustomerGroups.value = filteredCustomerGroups.value.map(group => ({
        ...group,
        selected: group.id === id ? false : group.selected
      }));

      // 同时更新临时选中列表
      tempSelectedGroups.value = tempSelectedGroups.value.filter(g => g.id !== id);
    };

    // 群主变更处理函数
    const handleGroupOwnerChange = () => {
      // 设置groupLeader为选中的群主ID
      entity.value.groupLeader = entity.value.setting.groupOwnerId || '';
      
      // 清空已选客户群
      selectedGroups.value = [];
      tempSelectedGroups.value = [];
      entity.value.setting.customerGroupId = [];

      // 重置分页和搜索条件
      currentPage.value = 1;
      searchForm.value.keyword = '';

      // 清空所有客户群的选中状态

      // 清空当前显示的客户群选中状态
      filteredCustomerGroups.value = filteredCustomerGroups.value.map(group => ({
        ...group,
        selected: false
      }));

      // 重新加载客户群数据
      if (customerGroupModalVisible.value) {
        loadCustomerGroups();
      }
    };
    
    // 推送方式变更处理函数
    const handlePushMethodChange = () => {
      // 根据推送方式设置groupLeader字段
      if (entity.value.setting.pushMethod === 'customerGroup') {
        entity.value.groupLeader = ''; // 客户群推送时置空
        entity.value.setting.groupOwnerId = null;
      } else if (entity.value.setting.pushMethod === 'groupOwner') {
        // 群主推送时，groupLeader将在选择群主时设置
        entity.value.groupLeader = '';
      }

      // 清空已选客户群
      selectedGroups.value = [];
      tempSelectedGroups.value = [];
      entity.value.setting.customerGroupId = [];

      // 重置分页和搜索条件
      currentPage.value = 1;
      searchForm.value.keyword = '';

      // 清空当前显示的客户群选中状态
      filteredCustomerGroups.value = filteredCustomerGroups.value.map(group => ({
        ...group,
        selected: false
      }));

    };

    // 绑定数据时初始化
    const bindData = async () => {
      // 获取campaign group信息
      campaignGroup.value = await getGroup(groupId);
      
      if (module.value.isEdit) {
        entity.value = await getCampaign(id);
        entity.value = entity.value ? entity.value : {};
        entity.value.rangeValue = [entity.value.startTime, entity.value.endTime];
        timeRange.value = [
          entity.value.setting.schedule.startTime,
          entity.value.setting.schedule.endTime,
        ];
        if (entity.value.setting.schedule.type === 'NONE') {
          entity.value.setting.schedule.type = null;
        }
        
        editEnable.value = editEnabled(entity.value.status);
        silenceRules.value = await findSilenceRuleList(); // 加载勿扰策略

        // 强制设置触达方式为"定制"
        entity.value.setting.reachType = 'flow';
      } else {
        // Initialize new campaign
        entity.value = {
          name: "",
          summary: "",
          type: "GROUP",
          groupId: groupId,
          category: null,
          tags: [],
          priority: 1,
          status: "DRAFT",
          startTime: null,
          endTime: null,
          rangeValue: [],
          groupLeader: '', // 推送方式字段：空字符串表示客户群
          setting: {
            sendType: 'immediate',
            messageType: 'text',
            mentionAll: false,
            messageContent: '',
            customerGroupId: null,
            sendTime: null,
            pushMethod: 'customerGroup', // 默认为客户群推送
            groupOwnerId: null, // 新增群主ID字段
            // reachType: null,
            reachType: 'flow', // 新建时默认选中“定制”
            schedule: {
              type: null,
              date: null,
              cron: null,
              startTime: null,
              endTime: null
            }
          }
        };
        
        // Handle template if provided
        const { templateId } = route.query;
        if (templateId) {
          entity.value.templateId = templateId;
        }
      }
      
      loadCategories();
      
      // 先加载客户群数据以获取群主信息
      await loadCustomerGroups();
      
      // 编辑模式下初始化pushMethod和groupLeader的关系
      if (module.value.isEdit) {
        // 检查接口返回的groupLeader字段，可能在根级别或setting中
        const groupLeaderValue = entity.value.groupLeader || entity.value.setting?.groupLeader || '';

        if (groupLeaderValue && groupLeaderValue !== '') {
          entity.value.setting.pushMethod = 'groupOwner';
          entity.value.setting.groupOwnerId = groupLeaderValue;
          // 同步更新根级别的groupLeader字段
          entity.value.groupLeader = groupLeaderValue;

          // 根据groupLeader找到对应的群主名称
          const owner = groupOwners.value.find(owner => owner.userId === groupLeaderValue);
          if (!owner) {
            // 如果群主列表中没有找到，添加到列表中
            groupOwners.value.push({
              userId: groupLeaderValue,
              name: '群主' // 默认名称，实际应该从接口获取
            });
          }
        } else {
          entity.value.setting.pushMethod = 'customerGroup';
          entity.value.groupLeader = '';
        }

      }
      
      if (entity.value.setting?.customerGroupId) {
        handleSelectCustomerGroup(entity.value.setting.customerGroupId);
      }
      
      // 加载触达配置相关数据
      const dataModel = await findCustomerModel();
      dataModelFields.value = formatFields(dataModel.fields, { path: "Customer" });
      if (entity.value.setting.reachType === 'flow') {
        handleSearchContent(null);
        showFlowContent();
      } else {
        handleSearchCapability(null);
        showContent();
      }

      // 初始化已选客户群
      if (entity.value.setting?.groupIds) {
        const ids = Array.isArray(entity.value.setting.groupIds) 
          ? entity.value.setting.groupIds 
          : [entity.value.setting.groupIds];
          
          try {
            const response = await getChatInfo({ 
              code: 'funLoopLand',
              page: 1,
              limit: 100 // 假设最多100个客户群
            });
            selectedGroups.value = response.list
              .filter(item => ids.includes(item.chatId))
              .map(item => ({
                id: item.chatId,
                name: item.name,
                groupOwnerId: item.ownerId,
                ownerName: item.ownerName
              }));
          } catch (error) {
            console.error('获取已选客户群信息失败:', error);
            selectedGroups.value = [];
          }
        } else {
          selectedGroups.value = [];
        }

      // 打印编辑模式下的客户群和触达配置信息
      if (module.value.isEdit) {
      }
    };

    const quit = async () => {
      await router.push({ path: module.value.mainPath, query: { id: groupId } });
    };

    const save = async () => {
      formRef.value.validate((err) => {
        if (!err) {
          // 根据推送方式设置groupLeader字段
          let groupLeaderValue = '';
          if (entity.value.setting.pushMethod === 'groupOwner' && entity.value.setting.groupOwnerId) {
            groupLeaderValue = entity.value.setting.groupOwnerId;
          }
          
          // 构建群组推送格式的数据
          const groupCampaignData = {
            ...entity.value,
            type: "GROUP", // 固定值
            campaignCode: campaignGroup.value?.code || "campaign", // 使用campaign group的code字段
            groupLeader: groupLeaderValue, // 根据推送方式设置groupLeader字段
            setting: {
              ...entity.value.setting,
              type: "GROUP", // 固定值
              sendType: "group", // 固定值
              reachType: "flow",
              groupIds: calculateGroupIds(), // 根据推送方式计算客户群IDs
              groupLeader: groupLeaderValue, // 根据推送方式设置groupLeader字段
            }
          };

          if (module.value.isEdit) {
            modifyCampaign(groupCampaignData).then(() => {
              quit();
            });
          } else {
            createCampaign(groupCampaignData).then(() => {
              quit();
            });
          }
        }
      });
      changed.value = false;
    };

    // 根据groupLeader计算groupIds
    const calculateGroupIds = () => {
      return selectedGroups.value.map(group => group.id.toString());
    };

    const handleRangeTime = () => {
      entity.value.startTime = entity.value.rangeValue[0];
      entity.value.endTime = entity.value.rangeValue[1];
    };

    const simulate = async () => {
      let debugInstanceId
      const debugInstances = await queryFlowInstance({ expression: `flowId eq ${id} AND engineType eq DEBUG AND status eq RUNNING` });
      if (debugInstances.length > 0) {
        debugInstanceId = debugInstances[0].id
      }
      
      if (changed.value) {
        Notification.warning({
          title: "活动变更未保存，模拟前请先保存流程",
        });
        return;
      }
      if (debugInstanceId) {
        simulateDlgRef.value.toogleSimulate(true, id, debugInstanceId);
      } else {
        await simulateDlgRef.value.show(id);
      }
    };

    // 新增触达配置相关方法
    const changeCapabilityType = async () => {
      entity.value.setting.capabilityId = null;
      await handleSearchCapability();
    };

    const handleSearchCapability = async (name) => {
      dataLoading.value = true;
      const params = { fields: "name,content,budgetSetting", expression: "" };
      params.expression = `type eq ${entity.value.setting.capabilityType} AND status eq ENABLED`;
      if (name) {
        params.expression += ` AND name like ${name}`;
      }
      capabilities.value = await findCommunicateList(params);
      dataLoading.value = false;
    };

    const changeFlowConfigId = async () => {
      entity.value.setting.flowContentId = null;
      await handleSearchContent();
    }

    const handleSearchContent = async (value) => {
      const params = { fields: "name,flowNodeId,flowTemplateId,status" };
      params.expression = `enabled in true`
      if (entity.value.setting.flowConfigId) {
        params.expression = `category eq ${entity.value.setting.flowChannel} AND ${params.expression}`
      }

      if (value) {
        params.expression = `( id like ${value} OR name like ${value} ) AND ${params.expression}`;
      }
      contentList.value = await findContentList(params);
      contentList.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
    };

    const showContent = async () => {
      if (entity.value.setting.flowContentId) {
        var campaign = await getCampaign(entity.value.setting.capabilityId);
        contentText.value = campaign.content;
      }
    };

    const showFlowContent = async () => {
      if (entity.value.setting.flowContentId) {
        var content = await getContent(entity.value.setting.flowContentId);
        contentText.value = content.content
        entity.value.setting.flowConfigId = content.flowNodeId;
      }
    };

    const setup = {
      t,
      formRef,
      simulateDlgRef,
      module,
      entity,
      editEnable,
      bindData,
      save,
      quit,
      campaignCategories,
      customerGroups,
      customerGroupEstimate,
      handleSelectCustomerGroup,
      handleRangeTime,
      simulate,
      simulateEnabled,
      frequencyOptions,
      timeRange,
      // 新增触达配置相关属性
      dataLoading,
      dataModelFields,
      treeFieldStruct,
      reachTypes,
      capabilityType, // 添加capabilityType到setup中
      capabilities,
      reachChannels,
      contentList,
      silenceRules,
      contentText,
      changeCapabilityType,
      handleSearchCapability,
      changeFlowConfigId,
      handleSearchContent,
      showContent,
      showFlowContent,
      customerGroupModalVisible,
      searchForm,
      loading,
      currentPage,
      pageSize,
      total,
      filteredCustomerGroups,
      selectedGroups,
      loadCustomerGroups,
      handleSearch,
      handlePageChange,
      handlePageSizeChange,
      toggleSelect,
      openCustomerGroupModal,
      handleSaveCustomerGroups,
      handleCancelCustomerGroups,
      removeGroup,
      groupOwners,
      handleGroupOwnerChange,
      handlePushMethodChange,
      calculateGroupIds,
      bindData
    };

    provide("edit", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.general-card {
  .form-title {
    font-weight: bold;
  }

  .form-item-panel {
    margin-left: 20px;
  }
}

.between-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .usage-type {
    color: #999;
    font-size: 12px;
  }
}

.estimate-number {
  color: #666;
  font-size: 12px;
  
  i {
    color: #1890ff;
    font-weight: bold;
  }
}

.flow-content {
  display: block;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}

.select-customer-group-btn{
    border-radius: 4px;
}

.selected-groups {
  :deep(.arco-list-header) {
    background-color: #efefef;
    padding: 8px 12px;
    font-size: 12px;
    // font-weight: bold;
  }
  
  :deep(.arco-list-item) {
    padding: 4px 12px;
    min-height: 12px;
    font-size: 12px;
    
    .arco-list-item-meta-title {
      font-size: 10px;
      line-height: 0;
    }
  }
}

.pagination-container {
  margin-top: 16px;
}

.easyflow-option {
  display: flex;
  flex-direction: column;
  
  .name {
    font-weight: 500;
  }
  
  .desc {
    font-size: 12px;
    color: #999;
  }
}
</style>