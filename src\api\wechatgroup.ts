import axios from 'axios';
import { tokenManager } from './auth';

// 请求参数接口
export interface ChatInfoRequest {
  code: string; // 企业微信编码
  limit?: number; // 每页记录数, 默认 10
  page?: number; // 页数，默认 1
}

// 客户群信息接口
export interface ChatInfo {
  chatId: string; // 群ID
  name: string; // 群名
  owner: string; // 群主， userId
  source: string; // 来源
}

// 响应数据接口
export interface ChatInfoResponse {
  totalCount: number; // 总条数
  pageSize: number; // 页数
  totalPage: number; // 总页数
  currPage: number; // 页码
  list: ChatInfo[];
}

// 获取企业微信群信息
export async function getChatInfo(params: ChatInfoRequest) {
  try {
    const token = await tokenManager.getValidToken();

    const response = await axios.post('/api/enterprise/wechat/getChatInfo', params, {
      headers: {
        'token': token,
        'Content-Type': 'application/json',
      },
    });

    if (!response) {
      throw new Error('获取企业微信群信息失败: 服务器响应格式错误');
    }

    return response;
  } catch (error: any) {
    console.error('获取企业微信群信息失败:', error);

    // 如果是token相关错误，清除token缓存
    if (error?.message && error.message.includes('token')) {
      tokenManager.clearToken();
    }

    // 如果是axios错误，提供更详细的错误信息
    if (error?.response) {
      console.error('服务器响应错误:', error.response.status, error.response.data);
      throw new Error(`获取企业微信群信息失败: 服务器错误 ${error.response.status}`);
    } else if (error?.request) {
      console.error('网络请求失败:', error.request);
      throw new Error('获取企业微信群信息失败: 网络连接错误');
    } else {
      throw error;
    }
  }
}