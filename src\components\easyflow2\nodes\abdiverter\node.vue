<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { shortDatetime } from "@/utils/date";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      brief.push({ sort: 0, label: "测试名称", value: data.groupName });

      if (data.groups) {
        for (let index = 0; index < data.groups.length; index++) {
          const element = data.groups[index];
          brief.push({
            sort: index + 2,
            label: element.name,
            value: data.groupType == "RATIO" ? `${element.ratio  }%` : element.number,
          });
        }
      }
      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    const item = nodeApi.find(item => { return item.type === 'ab_testing' })
    const setup = {
      title: item.name || "AbTesting",
      summary: item.name || "AbTestingNode",
      iconClass: item.icon || "icon-ABtestshezhi",
      nodeClass: "easyflow-node-end",
      headerColor: item.themeColor || "#fba980",
      headerBgColor: item.themeColor || "#fba980",
      background: item.background || "#fff9ef",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
