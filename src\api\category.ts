/**
 * 分组管理接口
 */
import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function createCategory(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/campaign-category`;
  return axios.post(uri, info) ;
}

export function modifyCategory(info: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/campaign-category`;
  return axios.put(uri, info);
}

export function deleteCategory(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/campaign-category/${id}`
  );
}

export function getCategory(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign-category/${id}`);
}

export function findCategoryList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign-category/list`, {
    params,
  });
}

export function findCategoryPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/campaign-category`, {
    params: {
      ...params,
      ...query,
    },
  });
}
