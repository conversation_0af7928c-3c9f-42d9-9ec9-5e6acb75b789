export default {
  "global.button.sync": '同步',
  "global.button.create": '新建',
  "global.button.edit": '编辑',
  "global.button.update": '修改',
  "global.button.delete": '删除',
  "global.button.submit": '提交',
  "global.button.cancel": '取消',
  "global.button.save": '保存',
  "global.button.confirm": '确认',
  "global.button.back": '返回',
  "global.button.next": '下一步',
  "global.button.previous": '上一步',
  "global.button.search": '搜索',
  "global.button.refresh": '刷新',
  "global.button.upload": '上传',
  "global.button.download": '下载',
  "global.button.close": '关闭',
  "global.button.apply": '应用',
  "global.button.reset": '重置',
  "global.button.add": '添加',
  "global.button.view": '查看',
  "global.button.more": '更多',
  "global.button.filter": '筛选',
  "global.button.sort": '排序',
  "global.button.export": '导出',
  "global.button.import": '导入',
  "global.button.query": '查询',
  "global.button.disable": '禁用',
  "global.button.enable": '启用',
  "global.button.expand": '展开',
  "global.button.retract": '收起',
  "global.button.list": '列表',
  "global.button.card": '卡片',
  "global.button.yes": '是',
  "global.button.no": '否',
  "global.button.operation": '操作',
  "global.button.group": '分组',
  "global.button.baseInfo": "基本信息",
  "global.button.and": '且',
  "global.button.or": '或',
  "global.button.saveTemplate": '保存模板',

  "global.button.design": '设计',
  "global.button.approval": "审批",
  "global.button.start": "启动",
  "global.button.pause": "暂停",
  "global.button.continueAction": "继续",
  "global.button.stop": "停止",
  "global.button.saveAs": "另存为",
  "global.button.operationRecord": "操作记录",
  "global.button.report": "报表",

  "global.tips.success.save": '保存成功',
  "global.tips.success.sync": '同步成功',
  "global.tips.success.edit": '编辑成功',
  "global.tips.success.export": '导出成功',
  "global.tips.success.import": '导入成功',
  "global.tips.success.apply": '应用成功',
  "global.tips.success.delete": '删除成功',
  "global.tips.success.upload": '上传成功',
  "global.tips.success.download": '下载成功',
  "global.tips.success.submit": '提交成功',
  "global.tips.success.createActivityMonitoringAudienceTaskSuccess": '创建活动监控人群任务成功',
  "global.tips.success.reportDataExport": '_报表数据导出',
  "global.tips.success.createFullCommunicationReportExportTaskSuccess": '创建全量沟通报表数据导出任务成功，可在任务管理中查看',
  "global.tips.success.fullCommunicationReportDataExport": "全量沟通报表数据导出",

  "global.tips.error.permission": '资源异常！',
  "global.tips.error.systemPrompt": "系统提示",
  "global.tips.error.permissionException": "权限异常",
  "global.tips.error.loginTimeoutOrNoPermission": "登录超时或没有权限，请重新登录或申请权限。",
  "global.tips.error.resourceException": "资源异常",
  "global.tips.error.systemException": "系统异常",
  "global.tips.error.networkErrorOrServerBusy": "网络连接错误或服务器正忙，请稍候重试...",


  "global.tips.warning.delete": '删除之后数据不可恢复，请确认是否删除?',

  "global.total": "共",

  "global.abtest.conversion": "转化",
  "global.abtest.revenue": "收入",
  "global.abtest.engagement": "签约",
  "global.abtest.up": "提升",
  "global.abtest.down": "降低",
  "global.abtest.upDays": "连续多日提升",
  "global.abtest.downDays": "连续多日降低",
  "editTemplate.willProceed":'您将进行',
  "editTemplate.willSubmit":'您将进行提交，是否确认？',
  "editTemplate.willSave":'您将进行保存，是否确认？',
  "editTemplate.isConfirm":'是否确认',
  "editTemplate.yes":'是',

  "global.task.status.submitted": '提交',
  "global.task.status.ready": '待运行',
  "global.task.status.success": '成功',
  "global.task.status.running": '运行中',
  "global.task.status.error": '失败',
  "global.task.status.expired": '已过期',

  "global.task.type.audienceComparisonTask": "人群比较任务",
  "global.task.type.audienceDeliveryLimitationVerificationTask": "人群投放限制验证任务",
  "global.task.type.activityMonitoringTask": "活动监控任务",
  "global.task.type.activityAudienceAppendTask": "活动人群追加任务",
  "global.task.type.exportProcessNodeRecord": "导出流程节点记录",
  "global.task.type.exportProcessTouchRecord": "导出流程链路触达记录",
  "global.task.type.exportActivityReportRecord": "导出活动报表记录",
  "global.task.type.exportProcessNodeTouchRecord": "导出流程节点触达记录",

  "global.task.audience.calculateAudience": "计算人群",
  "global.task.audience.importAudience": "导入人群",
  "global.task.audience.testAudience": "测试人群",
  "global.task.audience.realTrigger": "真实触发",
  "global.task.audience.select": "请选择",

  "global.task.campaign.audiencePackage": "人群包",
  "global.task.campaign.event": "事件",
  "global.task.campaign.flow": "流程",
  "global.task.campaign.customerGroup": "企微客户群",
  "global.task.campaign.acquisition": "拉新",
  "global.task.campaign.activation": "促活",
  "global.task.campaign.retention": "挽留",
  "global.task.campaign.draft": "草稿",
  "global.task.campaign.submitted": "已提交",
  "global.task.campaign.approved": "审核通过",
  "global.task.campaign.rejected": "审核拒绝",
  "global.task.campaign.stopped": "停止",
  "global.task.campaign.running": "运行中",
  "global.task.campaign.paused": "暂停",
  "global.task.campaign.finished": "完成",
  "global.task.campaign.error": "异常",

  "global.reach.none": "无",
  "global.reach.sms": "短信",
  "global.reach.mms": "彩信",
  "global.reach.email": "邮件",
  "global.reach.weChatOfficialAccount": "微信公众号",
  "global.reach.templateMessage": "模板消息",
  "global.reach.customerServiceMessage": "客服消息",
  "global.reach.broadcastMessage": "群发消息",
  // "global.reach.sms": "三方短信",
  // "global.reach.wechatEnterprise": "企业微信消息",
  // "global.reach.wechatGroup": "微信群发消息",

  "global.reach.status.sending": "发送中",
  "global.reach.status.sendSuccess": "发送成功",
  "global.reach.status.restricted": "被限制",
  "global.reach.status.sendError": "发送出错",
  "global.reach.status.postponeExecution": "推迟执行",
  "global.reach.status.silence": "勿扰时段",
  "global.reach.status.ignore": "忽略",
  "global.reach.status.received": "已接收",
  "global.reach.status.accepted": "接受",
  "global.reach.status.rejected": "拒绝",
  "global.reach.status.rejectChannelMessages": "拒绝渠道消息",
  "global.reach.status.blacklist": "黑名单",
  "global.reach.status.touchTimeout": "触达超时",

  "global.flow.receive": "接受",
  "global.flow.success": "成功",
  "global.flow.failure": "失败",
  "global.flow.identityFieldAssociation": "身份字段关联",
  "global.flow.queryConditionAssociation": "查询条件关联",
}