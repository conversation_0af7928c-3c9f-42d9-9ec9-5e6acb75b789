import { ref, onMounted } from 'vue'
import axios from "axios"
import { useBussinessUnitStore } from "@/store";

export default function () {
    const userBussinessUnitStore = useBussinessUnitStore();
    const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
    const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

    // 分页设置
    const pagingData = ref({
        page: 1,
        size: 10,
        total: 50,
        showPageSize: true,
        listUrl: '',
        params: {}
    })
    // 列表数据
    const dataList = ref([])

    // 切换分页
    const changePage = (e: any) => {
        pagingData.value.page = e
        getDataList()
    }
    // 切换条数
    const changeSizePage = (e: any) => {
        pagingData.value.page = 1
        pagingData.value.size = e
        getDataList()
    }

    const getDataList = () => {
        axios.get(`/api/ma-manage/${tenantId}/${buCode}${pagingData.value.listUrl}`, {
            params: {
                ...pagingData.value.params,
                page: pagingData.value.page,
                size: pagingData.value.size
            }
        }).then((res: any) => {
            pagingData.value.total = res.totalElements
            dataList.value = res.content
        })
    }

    onMounted(() => {
        getDataList()
    })
    return {
        pagingData,
        changePage,
        changeSizePage,
        getDataList,
        dataList
    }
}
