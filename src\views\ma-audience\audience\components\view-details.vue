<template>
  <div style="padding-bottom: 20px">
    <a-table v-if="columnsTable.length > 0" ref="table" :columns="columnsTable" :pagination="false" :bordered="false"
      :column-resizable="true" :data="dataSource"></a-table>
    <a-result v-else status="500" subtitle="客户模型没有可显示字段" />
    <div class="page-bottom">
      <!-- <span class="total">共{{ pagination.totalElements }}条</span> -->
      <span class="total">{{ t('global.total') }} : {{ pagination.totalElements }}</span>
      <a-pagination size="small" :total="pagination.total" :current="pagination.page" :page-size="pagination.size"
        show-page-size @change="changePage" @page-size-change="changeSizePage" />
    </div>

  </div>
</template>

<script>
import { ref, provide, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import { findAudienceCustomerPage } from "@/api/audience";
import { findCustomerModel } from "@/api/system";
import moment from "moment";

export default {
  setup() {
    // 路由API
    const route = useRoute();

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      totalElements: 0,
      showPageSize: true
    });
    // 过滤设置
    const filter = ref([]);

    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = ref([]);

    const columnsTable = ref([]);

    const loadCustomerModel = async () => {
      const data = await findCustomerModel();
      columnsTable.value = [];
      if (data) {
        data.fields.forEach((item) => {
          if (item.listDisplay) {
            columnsTable.value.push({
              title: item.aliasName,
              dataIndex: `${item.name}`,
              type: `${item.type}`,
              dateFormat: `${item?.options?.dateFormat}`,
              cellClass: "customer-column",
              cellStyle: { "white-space": "nowrap" },
              width: 100
            });
          }
          if (item.useForSearch) {
            filter.value.push({
              field: `${item.name}`,
              label: item.aliasName,
              type: `${item.type}`,
              dateFormat: `${item?.options?.dateFormat}`,
              component: "a-input",
              operate: "eq",
              placeholder: `请输入${item.aliasName}`,
              value: ""
            });
          }
        });
        pagination.value.showPageSize = columnsTable.value.length > 0;
      }
    };
    // 查询API
    const bindData = async () => {
      const expression = "";
      dataSource.value = [];
      await loadCustomerModel();
      entity.value = await findAudienceCustomerPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1
        },
        { expression },
        route.query.id
      );
      entity.value.content.forEach((item) => {
        const itemDate = columnsTable.value.filter((x) => {
          return x.type === "date";
        });
        itemDate.forEach((d) => {
          item[d.dataIndex] = moment(item[d.dataIndex]).format(
            item?.dateFormat || "YYYY-MM-DD"
          );
        });
      });
      dataSource.value = entity.value.content || [];

      pagination.value.total = entity.value.totalElements > 10000 ? 10000 : entity.value.totalElements;
      pagination.value.totalElements = entity.value.totalElements;

    };

    // 切换页数
    const changePage = async (e) => {
      pagination.value.page = e;
      bindData();
    };
    // 切换条数
    const changeSizePage = async (e) => {
      pagination.value.page = 1;
      pagination.value.size = e;
      bindData();
    };

    const getDataChange = (text, item) => {
      moment(text).format(item?.options?.dateFormat || "YYYY-MM-DD");
    };

    onMounted(async () => {
      await bindData();
    });

    const setup = {
      filter,
      entity,
      bindData,
      dataSource,
      pagination,
      getDataChange,
      columnsTable,
      changePage,
      changeSizePage
    };
    provide("main", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped>
:deep(.general-card) {
  margin: 0;
}

.customer-column {
  white-space: nowrap;
}

.page-bottom {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  align-items: center;

  .total {
    margin-top: 20px;
  }
}
</style>
