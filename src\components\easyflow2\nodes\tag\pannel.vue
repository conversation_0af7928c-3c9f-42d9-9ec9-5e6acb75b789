/** 添加新标签 */
<template>
  <div class="easyflow-pannel-tag">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="选择标签" :content-flex="false">
        <template v-for="(item, index) in entity.tags" :key="index">
          <div class="tag-line">
            <a-switch v-model="item.add" type="round" style="width: 80px">
              <template #checked>添加</template>
              <template #unchecked>删除</template>
            </a-switch>

            <a-select v-model="item.code" style="margin: 0 10px" placeholder="请选择标签" :loading="loading"
              :filter-option="false" :allow-search="true" @search="handleSearchTag">
              <a-option v-for="tag of tags" :key="tag.code" :value="tag.code">{{ tag.name }}</a-option>
            </a-select>
            <icon-delete size="30" @click="delTag(index)" />
          </div>
        </template>
      </a-form-item>
      <a-button type="outline" @click="addTag">添加标签操作</a-button>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { findTagList } from "@/api/audience";

const props = defineProps(["node"]);
const { node } = props;
const loading = ref(false);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;
const entity = ref({
  tags: []
});
const tags = ref([]);

const save = () => {
  for (let i = 0; i < entity.value.tags.length; i++) {
    if (!entity.value.tags[i].code) {
      entity.value.tags.splice(i, 1);
    }
  }
  return entity.value;
};

const handleSearchTag = async (name) => {
  loading.value = true;
  const params = {
    expression: "tagType eq hand AND tagGroupHandType eq fixed_value AND delete ne true"
  };
  if (name) {
    params.expression += `AND name like ${name}`;
  }
  tags.value = await findTagList(params);
  loading.value = false;
};
const addTag = () => {
  entity.value.tags.push({ add: true });
};
const delTag = (index) => {
  entity.value.tags.splice(index, 1);
};

defineExpose({
  save
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  handleSearchTag();
});
</script>

<style lang="less" scoped>
.tag-line {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 10px 0;
}

.arco-icon-delete {
  cursor: pointer;
}
</style>
