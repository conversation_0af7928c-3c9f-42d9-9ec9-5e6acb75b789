<template>
  <a-card :hoverable="hoverable">
    <a-typography-text class="title" style="margin-right: 10px">
      {{ title }}
    </a-typography-text>
    <div class="sub-title">
      {{ subtitle }}
    </div>
    <div class="summary">
      <slot name="summary">
        {{ summary }}
      </slot>
    </div>
    <div class="action">
      <slot name="action">
        <a-button size="mini" type="primary">123</a-button>
      </slot>
    </div>
  </a-card>
</template>

<script>
export default {
  name: "card-warp",
  props: {
    hoverable: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: "",
    },
    subtitle: {
      type: String,
      default: "",
    },
    summary: {
      type: String,
      default: "",
    },
  },
};
</script>

<style scoped lang="less">
.title {
  font-weight: bolder;
}
.sub-title {
  margin: 10px 0;
  font-size: 12px;
  color: #86909c;
}
.summary {
  color: #4e5969;
}
.action {
  margin-top: 20px;
}
</style>
