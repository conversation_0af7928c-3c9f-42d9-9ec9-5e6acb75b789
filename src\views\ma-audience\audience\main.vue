<template>
  <div>
    <module main>
      <template #action>
        <!-- 自定义 -->
        <a-button v-permission="['ma_menu.audience.create']" type="primary" @click="openAddModel">
          <template #icon>
            <icon-plus />
          </template>
          {{ t('global.button.create') }}
        </a-button>
        <a-button v-permission="['ma_menu.audience.synchronization']" type="primary" :loading="syncing"
          @click="syncFromDWH">
          {{ t('audience.button.data_warehouse_sync') }}
        </a-button>
        <a-button v-permission="['ma_menu.audience.compare']" type="primary" @click="openCompareModel">
          <template #icon>
            <icon-swap />
          </template>
          {{ t('audience.button.audience_comparison') }}
        </a-button>
      </template>
      <template #main>
        <a-table ref="table" :bordered="false" :data="dataSource" :pagination="false">
          <template #columns>
            <a-table-column :title="t('audience.column.id')" data-index="id" :width="150" />
            <a-table-column :title="t('audience.column.name')" data-index="name" :width="200" />
            <a-table-column :title="t('audience.column.selection_type')" data-index="selectType" :width="80">
              <template #cell="{ record }">
                {{ filters(selectTypes, record.selectType) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('audience.column.audience_usage')" data-index="usageType" :width="80">
              <template #cell="{ record }">
                {{ filters(usageTypes, record.usageType) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('audience.column.modifiable')" data-index="own" :width="80">
              <template #cell="{ record }">
                {{ filters(ownTypes, record.own) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('audience.column.description_info')" data-index="description" :width="200"
              :ellipsis="true" :tooltip="{ class: 'tooltip-content' }" />
            <a-table-column :title="t('audience.column.action')" align="center" :width="160">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.audience.view']" type="text" size="small"
                  @click="openViewPage(record)">{{ t('audience.operation.view') }}</a-button>
                <!-- <a-button type="text" size="small" @click="openAnalysePage(record)">分析</a-button> -->
                <a-button v-permission="['ma_menu.audience.modify']" type="text" size="small"
                  @click="openEditPage(record)">{{ t('audience.operation.edit') }}</a-button>
                <a-button v-permission="['ma_menu.audience.delete']" type="text" size="small"
                  @click="deleteData(record.id)">{{ t('audience.operation.delete') }}</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>

    <!-- 新增弹窗 -->
    <create-dlg ref="createDlgRef" />
    <compare-dlg ref="compareDlgRef" />
  </div>
</template>

<script>
import { ref, provide, getCurrentInstance } from "vue";
import { Notification, Modal } from "@arco-design/web-vue";
import { useRouter } from "vue-router";
import { findAudiencePage, deleteItem, syncAudienceFromDWH } from "@/api/audience";
import { selectTypes, usageTypes } from "@/constant/audience";
import { filters } from "@/utils/filter";
import compareDlg from "./components/compare-dlg.vue";
import createDlg from "./components/create-dlg.vue";
import { useUserDataRoleStore } from "@/store";
import reference from "@/components/reference/index.vue"

export default {
  components: {
    compareDlg,
    createDlg,
    reference
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    // 路由API
    const router = useRouter();

    // 模块设置
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          // name: "活动人群",
          name: t('audience.title'),
        },
      ],
      editConditionPath: "/audience/edit-condition",
      editManualPath: "/audience/edit-manual",
      viewPath: "/audience/audience/view",
      analysePath: "/audience/audience/analyse",
      showCard: false,
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true,
    });

    // 过滤设置
    const filter = ref([
      {
        field: "id",
        // label: "人群ID",
        label: t('audience.id'),
        component: "a-input",
        operate: "eq",
        // placeholder: "请输入人群ID",
        // placeholder: "请输入人群ID",
        placeholder: t('audience.reminder.input_audience_id'),
        comment: true,
        value: "",
      },
      {
        field: "name",
        // label: "人群名称",
        label: t('audience.name'),
        component: "a-input",
        operate: "like",
        // placeholder: "请输入人群名称",
        placeholder: t('audience.reminder.input_audience_name'),
        comment: true,
        value: "",
      },
    ]);

    const ownTypes = ref([
      // { label: "是", value: true },
      // { label: "否", value: false }
      { label: t('audience.bool.yes'), value: true },
      { label: t('audience.bool.no'), value: false }
    ]);
    // 列表数据
    const dataSource = ref([]);

    const syncing = ref(false);
    // Ref
    const createDlgRef = ref(null);
    const compareDlgRef = ref(null);

    // 查询API
    const bindData = async (expression) => {
      // let dataRole = useUserDataRoleStore().currentUserDataRole.map(
      //   (item) => item.id
      // );
      // if (expression) {
      //   expression = `${expression} AND dataRoles in '0,${dataRole.join(",")}'`;
      // } else {
      //   expression = `dataRoles in '0,${dataRole.join(",")}'`;
      // }
      const res = await findAudiencePage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        {
          expression,
          fields: "name,usageType,selectType,own,createdTime,description",
        }
      );
      dataSource.value = res.content;
      pagination.value.total = res.totalElements;
    };

    // 详情跳转
    const openEditPage = (row) => {
      const query = {};
      query[module.value.entityIdField] = row[module.value.entityIdField];
      const editPath =
        row.selectType === "CONDITION"
          ? module.value.editConditionPath
          : module.value.editManualPath;
      router.push({ path: editPath, query });
    };

    // 跳转查看页
    const openViewPage = (row) => {
      router.push({ path: module.value.viewPath, query: { id: row.id } });
    };

    // 跳转分析页
    const openAnalysePage = (row) => {
      router.push({ path: module.value.analysePath, query: { id: row.id } });
    };

    // 删除人群
    const deleteData = async (id) => {
      Modal.confirm({
        // title: "删除人群",
        title: t('audience.reminder.delete_audience'),
        // content: "删除之后数据不可恢复，请确认是否删除?",
        content: t('audience.reminder.sysn_audience'),
        onOk: async () => {
          await deleteItem(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1;
          }
          await bindData();
        },
      });
    };

    // 新建弹窗
    const openAddModel = () => {
      createDlgRef.value.show();
    };

    // 同步人群
    const syncFromDWH = async () => {
      try {
        syncing.value = true;
        await syncAudienceFromDWH();
        await bindData();
        // Notification.info({ title: "同步人群完成。"});
        Notification.info({ title: t('audience.reminder.sysn_audience') });
      } catch (e) {
        console.error(e);
      }
      syncing.value = false;
    };

    // 人群比较弹窗
    const openCompareModel = () => {
      compareDlgRef.value.show();
    };

    const setup = {
      t,
      module,
      filter,
      ownTypes,
      syncing,
      openEditPage,
      bindData,
      dataSource,
      pagination,
      compareDlgRef,
      createDlgRef,
      openAddModel,
      syncFromDWH,
      openCompareModel,
      deleteData,
      openViewPage,
      openAnalysePage,
      filters,
      selectTypes,
      usageTypes,
    };
    provide("main", setup);
    return setup;
  },
};
</script>

<style scoped lang="less"></style>
