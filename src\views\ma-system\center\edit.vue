<template>
  <module edit>
    <template #action>
        <a-button v-permission="['ma_menu.system.center.update']" type="primary" @click="updateCenter">
          {{t('global.button.update')}}
        </a-button>
      </template>
    <template #main>
      <a-spin style="display: block">
        <a-form ref="dataFormRef" layout="vertical" class="general-form" :model="entity">
          <a-space direction="vertical" :size="16">
            <a-card class="general-card">
              <h3>{{ t('systemSetting.marketingCenterConfig.ossSetting')}}</h3>
              <a-row class="grid-demo" :gutter="20">
                <a-col :span="8">
                  <a-form-item field="type" :label="t('systemSetting.marketingCenterConfig.type')" :rules="[{ required: true, message:  t('systemSetting.marketingCenterConfig.type') }]">
                    <a-select v-model="entity.setting.bucketSetting.type" allow-clear :placeholder=" t('systemSetting.marketingCenterConfig.type')">
                      <template #empty>
                        <a-empty :description=" t('systemSetting.marketingCenterConfig.noDataSource')" />
                      </template>
                      <a-option value="s3"> S3 </a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row class="grid-demo" :gutter="20">
                <a-col :span="8">
                  <a-form-item field="setting.bucketSetting.endpoint" :label=" t('systemSetting.marketingCenterConfig.endpoint')"
                    :rules="[{ required: true, message:  t('systemSetting.marketingCenterConfig.enterEndpoint') }]">
                    <a-input v-model="entity.setting.bucketSetting.endpoint" :placeholder=" t('systemSetting.marketingCenterConfig.enterAccessKey')" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="setting.bucketSetting.accessKey" :label=" t('systemSetting.marketingCenterConfig.accessKey')"
                    :rules="[{ required: true, message:  t('systemSetting.marketingCenterConfig.enterAccessKey')}]">
                    <a-input v-model="entity.setting.bucketSetting.accessKey" :placeholder=" t('systemSetting.marketingCenterConfig.enterAccessKey')" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">

                  <a-form-item field="setting.bucketSetting.secretKey" :label=" t('systemSetting.marketingCenterConfig.secretKey')"
                    :rules="[{ required: true, message:  t('systemSetting.marketingCenterConfig.enterSecretKey') }]">
                    <a-input-password v-model="entity.setting.bucketSetting.secretKey" :placeholder=" t('systemSetting.marketingCenterConfig.enterSecretKey')" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">

                  <a-form-item field="setting.bucketSetting.bucketName" :label=" t('systemSetting.marketingCenterConfig.bucketName')"
                    :rules="[{ required: true, message:  t('systemSetting.marketingCenterConfig.enterBucketName')}]">
                    <a-input v-model="entity.setting.bucketSetting.bucketName" :placeholder=" t('systemSetting.marketingCenterConfig.enterBucketName')" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="setting.bucketSetting.basePath" :label=" t('systemSetting.marketingCenterConfig.basePath')">
                    <a-input v-model="entity.setting.bucketSetting.basePath" :placeholder=" t('systemSetting.marketingCenterConfig.basePath')" />
                  </a-form-item>
                </a-col>
              </a-row>
              <h3>{{ t('systemSetting.marketingCenterConfig.flowSetting')}}</h3>
              <a-row class="grid-demo" :gutter="20">
                <a-col :span="8">
                  <a-form-item field="setting.flowSetting.flowTemplateSyncPublishId" :label=" t('systemSetting.marketingCenterConfig.flowTemplateSyncPublishId')"
                    label-col-flex="170px">
                    <a-select v-model="entity.setting.flowSetting.flowTemplateSyncPublishId" class="easyflow-select"
                      :placeholder="t('systemSetting.marketingCenterConfig.selectFlowTemplateSyncPublishId')" :loading="loading" :filter-option="false" @search="handleSearchFlow"
                      @change="handleSearchFlowStarts(entity.setting.flowSetting.flowTemplateSyncPublishId)">
                      <a-option v-for="item of flows" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="setting.flowSetting.flowTemplateSyncStartId" :label=" t('systemSetting.marketingCenterConfig.flowTemplateSyncStartId')" label-col-flex="170px">
                    <a-select v-model="entity.setting.flowSetting.flowTemplateSyncStartId" class="easyflow-select"
                      :placeholder="t('systemSetting.marketingCenterConfig.selectFlowTemplateSyncStartId')" :loading="loading" :filter-option="false">
                      <a-option v-for="item of starts" :key="item.taskId" :value="item.taskId">{{ item.name
                        }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <h3>{{ t('systemSetting.marketingCenterConfig.customSetting')}}</h3>
              <a-row class="grid-demo" :gutter="20">
                <a-col :span="10">
                  <a-form-item field="setting.customizationSetting.campaignEditableStatus" :label=" t('systemSetting.marketingCenterConfig.campaignEditableStatus')"
                    label-col-flex="170px">
                    <a-select v-model="entity.setting.customizationSetting.campaignEditableStatus" class="easyflow-select"
                    :placeholder="t('systemSetting.marketingCenterConfig.selectFlowTemplateSyncStartId')" :loading="loading" multiple :filter-option="false">
                      <a-option v-for="item of flowStatus" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item field="setting.customizationSetting.manualCampaignCode" :label=" t('systemSetting.marketingCenterConfig.manualCampaignCode')"
                    label-col-flex="170px">
                    <a-switch v-model="entity.setting.customizationSetting.manualCampaignCode" type="round" checked-value="ENABLED" unchecked-value="DISABLED"
                      class="form-switch">
                      <template #checked>{{t('global.button.yes')}}</template>
                      <template #unchecked>{{t('global.button.no')}}</template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item field="setting.customizationSetting.aiEnable" :label=" t('systemSetting.marketingCenterConfig.aiEnable')" label-col-flex="170px">
                    <a-switch v-model="entity.setting.customizationSetting.aiEnable" type="round" checked-value="ENABLED" unchecked-value="DISABLED" class="form-switch">
                      <template #checked>{{t('global.button.yes')}}</template>
                      <template #unchecked>{{t('global.button.no')}}</template>
                    </a-switch>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-card>
          </a-space>
        </a-form>
      </a-spin>
    </template>
  </module>
</template>

<script>
import { ref, provide,getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getMarketingCenter, saveMarketingCenter } from "@/api/marketing_center";
import { findFlowList, findFlowStarts } from "@/api/campaign";
import { Message } from "@arco-design/web-vue";
import { useBussinessUnitStore } from "@/store";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const route = useRoute();
    const router = useRouter();
    const queryValue = route.query.id;
    const userBussinessUnitStore = useBussinessUnitStore();

    const module = ref({
      entityIdField: "id",
      mainPath: "/system/setting",
      breadcrumb: [{
        name: t('menu.system'),
        path: '/system/setting'
      }, {
        name: t('systemSetting.basic.marketingCenterConfig'),
      }],
      isEdit: !!queryValue
    });

    const entity = ref({});
    const loading = ref(false);
    const flows = ref([]);
    const starts = ref([]);

    const flowStatus = ref([
      { id: 'DRAFT', name:  t('systemSetting.marketingCenterConfig.DRAFT')},
      { id: "COMMITTED", name:  t('systemSetting.marketingCenterConfig.COMMITTED') },
      { id: "REJECTED", name: t('systemSetting.marketingCenterConfig.REJECTED')},
      { id: "PAUSED", name:  t('systemSetting.marketingCenterConfig.PAUSED') }
    ]);

    const handleSearchFlow = async (name) => {
      loading.value = true;
      const params = { fields: "name" };
      params.expression = "status eq ENABLED";
      if (name) {
        params.expression += `AND name like ${name}`;
      }
      flows.value = await findFlowList(params);
      loading.value = false;
    };

    const handleSearchFlowStarts = async (publishId) => {
      starts.value = [];
      loading.value = true;
      starts.value = await findFlowStarts(publishId);
      loading.value = false;
    };


    const bindData = async () => {
      entity.value = await getMarketingCenter();
      await handleSearchFlow();
      if (entity.value.setting.flowSetting.flowTemplateSyncPublishId) {
        await handleSearchFlowStarts(entity.value.setting.flowSetting.flowTemplateSyncPublishId);
      }
    };

    const quit = () => {
      router.push({ path: module.value.mainPath });
    };

    const updateCenter = async () => {
      await saveMarketingCenter(entity.value);
      await userBussinessUnitStore.getBussinessUnit();
      Message.success(t('global.tips.success.submit'));
      quit();
    };


    const setup = {
      t,
      updateCenter,
      route,
      router,
      module,
      entity,
      loading,
      bindData,
      handleSearchFlow,
      handleSearchFlowStarts,
      flows,
      starts,
      flowStatus
    };
    provide("edit", setup);
    return setup;
  },
};
</script>
