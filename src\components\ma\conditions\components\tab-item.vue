<template>
  <div class="data-tabs">
    <template v-if="tabCondition.empty">
    </template>
    <template v-else-if="tabCondition.relation">
      <!-- 条件组 -->
      <div v-if="tabCondition.conditions.length > 1" class="or-change-and">
        <span @click="changeRelation(tabCondition)">
          <!-- {{ tabCondition.relation === "and" ? "且" : "或" }} -->
          {{ tabCondition.relation === "and" ? t('global.button.and') : t('global.button.or') }}
        </span>
      </div>
      <template v-for="(item, index) in tabCondition.conditions" :key="index">
        <div v-if="isEmptyArray(item.conditions)" class="field-line field-m">
          <FieldItem :key="index  + 'FieldItem'" :field="item" :data-model-fields="dataModelFields" :tags="tags"/>
          <a-space>
            <a-button v-if="showAddBtn(index, item)" class="btn" type="primary" size="mini"
              @click="addSameItem(item)">{{t('global.button.add')}}</a-button>
            <a-button class="btn" type="outline" size="mini" @click="addGrouping(item)">{{t('global.button.group')}}</a-button>
            <a-button class="btn" size="mini" status="danger" @click="removeItem(tabCondition, index)">{{t('global.button.delete')}}</a-button>
          </a-space>
        </div>
        <TabItem v-if="!isEmptyArray(item.conditions)" :tab-condition="item" :item-index="index"
          :on-change-data="removeData" :data-model-fields="dataModelFields" />
      </template>
    </template>
    <template v-else>
      <!-- 单个条件 -->
      <div class="field-line field-s">
        <FieldItem class="field-item" :field="tabCondition" :data-model-fields="dataModelFields" :tags="tags"/>
        <a-space>
          <a-button class="btn" type="primary" size="mini" @click="addConditionItem(tabCondition)">{{t('global.button.add')}}</a-button>
          <a-button class="btn" type="outline" size="mini" @click="addGrouping(tabCondition)">{{t('global.button.group')}}</a-button>
          <a-button class="btn" size="mini" status="danger" @click="removeItem(tabCondition, 0)">{{t('global.button.delete')}}</a-button>
        </a-space>
      </div>
    </template>

  </div>
</template>

<script setup>
import { ref, watch , getCurrentInstance} from "vue";
import { clone, isEmptyArray } from "@/utils/common"
import FieldItem from "./field-item.vue";

const {
      proxy: { t }
    } = getCurrentInstance()

const props = defineProps({
  onChangeData: Function,
  tabCondition: Object,
  itemIndex: Number,
  dataModelFields: Array,
  tags: Array
});

// 判断是否显示添加
const showAddBtn = (index, item) => {
  const showIndx = props.tabCondition.conditions.findIndex(x => {
    return isEmptyArray(x.conditions)
  })
  return index === showIndx
}

// 切换并且
const changeRelation = (item) => {
  item.relation = item.relation === "and" ? "or" : "and";
};

const removeData = (data, index) => {
  props.tabCondition.conditions[index] = data
  props.onChangeData(props.tabCondition, props.itemIndex)
}

// 添加分组
const addGrouping = (_item) => {
  _item.conditions = [{ ...clone(_item) }];
  _item.name = "";
  _item.type = "";
  _item.operator = "";
  _item.value = "";
  _item.expressionType = "";
  _item.expression = "";
  _item.relation = "and";
  _item.conditions.push({
    name: "",
    type: "",
    operator: "",
    value: "",
    expressionType: "",
    expression: "",
    conditions: [],
  });
};

// 添加下级
const addConditionItem = (_item) => {
  if (isEmptyArray(_item.conditions)) {
    addGrouping(_item)
  } else {
    _item.conditions.push({
      name: "",
      type: "",
      operator: "",
      value: "",
      expressionType: "",
      expression: "",
      conditions: [],
    });
  }
};

// 添加同级
const addSameItem = (_item) => {
  props.tabCondition.conditions.push({
    name: "",
    type: "",
    operator: "",
    value: "",
    expressionType: "",
    expression: "",
    conditions: [],
  });
};

// 删除
const removeItem = (condition, index) => {
  condition.conditions.splice(index, 1);
  if (condition.conditions.length === 1) {
    props.onChangeData(condition.conditions[0], props.itemIndex)
  } else if (condition.conditions.length === 0) {
    props.onChangeData({ "empty": true }, props.itemIndex)
  }
};
</script>

<style lang="less" scoped>
.field-line {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  .field-item {
    width: 85%;
  }
}

.data-tabs {
  position: relative;
  padding-left: 30px;
}

.or-change-and {
  width: 25px;
  border: 2px solid rgb(var(--primary-6));
  border-right: 0;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  height: calc(100% + 10px);
  display: flex;
  align-items: center;
  position: absolute;
  top: -5px;
  left: 5px;
  bottom: -5px;

  span {
    border: 1px solid rgb(var(--primary-6));
    background-color: #ffffff;
    padding: 1px 3px;
    font-weight: bold;
    margin-left: -12px;
    font-size: 12px;
    cursor: pointer;
  }
}
</style>
