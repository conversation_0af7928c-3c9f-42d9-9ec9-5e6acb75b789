{"name": "ma", "description": "MA UI", "version": "1.1.0.RELEASE", "private": true, "author": "Lianwei DMCS Product Team", "license": "MIT", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "report": "cross-env REPORT=true npm run build", "lint-staged": "npx lint-staged", "prepare": "husky install"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write"], "*.vue": ["stylelint --fix", "prettier --write"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@antv/x6": "^1.32.2", "@antv/x6-vue-shape": "^1.4.0", "@arco-design/web-vue": "^2.50.0", "@tinymce/tinymce-vue": "^4.0.5", "@types/mockjs": "^1.0.4", "@vueuse/core": "^7.3.0", "axios": "^0.24.0", "dayjs": "^1.10.7", "default-passive-events": "^2.0.0", "echarts": "^5.2.2", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.29.3", "nprogress": "^0.2.0", "pinia": "^2.0.9", "query-string": "^7.0.1", "screenfull": "^6.0.2", "tinymce": "^5.10.2", "vue": "^3.2.31", "vue-cal": "^4.2.0", "vue-echarts": "^6.0.0", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.2.0-beta.17", "vue-router": "^4.0.14", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^12.0.1", "@types/lodash": "^4.14.177", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "@vitejs/plugin-vue": "^1.9.4", "@vitejs/plugin-vue-jsx": "^1.2.0", "@vue/babel-plugin-jsx": "^1.1.1", "cross-env": "^7.0.3", "eslint": "^8.7.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^2.4.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.3.0", "husky": "^7.0.4", "jsencrypt": "^3.3.2", "less": "^4.1.2", "lint-staged": "^11.2.6", "mockjs": "^1.1.0", "prettier": "^2.2.1", "rollup-plugin-visualizer": "^5.6.0", "stylelint": "^13.8.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^4.1.0", "typescript": "^4.5.5", "unplugin-vue-components": "^0.17.21", "vite": "^2.6.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.3.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-style-import": "1.4.1", "vite-svg-loader": "^3.1.0", "vue-tsc": "^0.30.5"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}}