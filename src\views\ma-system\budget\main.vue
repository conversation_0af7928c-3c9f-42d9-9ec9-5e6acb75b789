/** 营销预算设置，包括年度预算，补充预算，调整预算等 */
<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <a-button type="primary" @click="addItem()">
          <template #icon>
            <icon-plus />
          </template>
          新建
        </a-button>
      </template>
      <template #main>
        <a-table ref="table" :bordered="false" :data="dataSource" :pagination="false">
          <template #columns>
            <a-table-column title="财年预算名称" data-index="name" />
            <a-table-column title="财年" data-index="annual" />
            <a-table-column title="总预算" data-index="amount">
              <template #cell="{ record }">
                {{ formatNumber(record.amount) }}
              </template>
            </a-table-column>
            <a-table-column title="状态" data-index="status">
              <template #cell="{ record }">
                <a-tag v-if="record.status === 'PLAN'" color="#165dff">计划</a-tag>
                <a-tag v-if="record.status === 'VALID'" color="#00b42a">有效</a-tag>
                <a-tag v-if="record.status === 'INVALID'" color="#86909c">无效</a-tag>
              </template>
            </a-table-column>
            <a-table-column title="默认财年" data-index="defaultAnnualBudget">
              <template #cell="{ record }">
                  <a-tag v-if="record.defaultAnnualBudget" color="#00b42a">是</a-tag>
                  <a-tag v-if="!record.defaultAnnualBudget" style="cursor: pointer;" color="#86909c" @click="setDefaultAnnualBudget(record)">否</a-tag>
              </template>
            </a-table-column>
            <a-table-column title="操作" :align="'center'">
              <template #cell="{ record }">
                <a-button type="text" size="small" @click="addItem(record)">编辑</a-button>
                <a-button type="text" size="small" @click="deleteData(record.id)">删除</a-button>
                <!-- <a-button type="text" size="small" @click="setDefaultAnnualBudget(record)">默认</a-button> -->
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>

    <!--  新增数据  -->
    <a-modal v-model:visible="addItemVisible" @cancel="addItemVisible = false" @before-ok="handleOk">
      <template #title>{{ dataForm.id ? "编辑预算" : "新增预算" }}</template>
      <a-form ref="formRef" :model="dataForm">
        <a-form-item field="name" label="预算名称" :rules="[{ required: true, message: '不能为空' }]" label-col-flex="70px">
          <a-input v-model="dataForm.name" placeholder="请输入预算名称" />
        </a-form-item>
        <a-form-item field="annual" label="财年" :rules="[{ required: true, message: '不能为空' }]" label-col-flex="70px">
          <a-year-picker v-model="dataForm.annual" style="width: 100%" placeholder="请选择年份" />
        </a-form-item>
        <a-form-item field="amount" label="总预算" :rules="[{ required: true, message: '不能为空' }]" label-col-flex="70px">
          <a-input-number v-model="dataForm.amount" placeholder="请输入总预算" :precision="2" :formatter="formatter"
            :parser="parser" :min="0">
            <template #prefix>￥</template>
          </a-input-number>
        </a-form-item>
        <a-form-item field="status" label="状态" :rules="[{ required: true, message: '不能为空' }]" label-col-flex="70px">
          <a-radio-group v-model="dataForm.status">
            <a-radio value="PLAN">计划</a-radio>
            <a-radio value="VALID">有效</a-radio>
            <a-radio value="INVALID">无效</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { provide, ref } from "vue";
import { useRoute } from "vue-router";
import { Modal } from "@arco-design/web-vue";
import { formatNumber } from "@/utils/filter";
import {
  getAnnualfindPage,
  setAnnualBudgetItem,
  deleteAnnualItem,
  setDefaultAnnualBudgetItem,
} from "@/api/budget";
import { modalCommit } from "@/utils/modal";

export default {
  setup() {
    const route = useRoute();
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: "系统设置",
          path: "/system/setting",
        },
        {
          name: "预算财年设置",
        },
      ],
      showCard: false,
    });
    const filter = ref([
      {
        field: "name",
        label: "财年预算名称",
        component: "a-input",
        operate: "like",
        placeholder: "请输入财年预算名称",
        comment: true,
        value: "",
      },
    ]);
    const dataSource = ref([]);
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true,
    });
    const formRef = ref({});

    // 获取列表
    const bindData = async (expression) => {
      const pageData = await getAnnualfindPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        {
          expression,
        }
      );
      dataSource.value = pageData.content;
      pagination.value.total = pageData.totalElements;
    };

    // 新增数据
    const addItemVisible = ref(false);
    const dataForm = ref({});
    const addItem = (item) => {
      dataForm.value = item
        ? JSON.parse(JSON.stringify(item))
        : {
          amount: 0,
          annual: "",
          defaultAnnualBudget: false,
          name: "",
          remarks: "",
          status: "PLAN",
        };
      formRef.value.clearValidate();
      addItemVisible.value = true;
    };
    const handleOk = (done) => {
      modalCommit(formRef, done, async () => {
        await setAnnualBudgetItem(dataForm.value);
        await bindData();
      });
    };

    // 删除
    const deleteData = async (id) => {
      Modal.confirm({
        title: "删除预算",
        content: "删除之后数据不可恢复，请确认是否删除?",
        onOk: async () => {
          await deleteAnnualItem(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page--;
          }
          await bindData();
        },
      });
    };
    const formatter = (value) => {
      const values = value.split(".");
      values[0] = values[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      return values.join(".");
    };

    const parser = (value) => {
      return value.replace(/,/g, "");
    };
    // 设置默认
    const setDefaultAnnualBudget = (item) => {
      Modal.confirm({
        title: "设置默认财年",
        content: `将${item.annual}设置为默认财年，是否确定?`,
        onOk: async () => {
          await setDefaultAnnualBudgetItem(item.id);
          await bindData();
        },
      });
    };

    const setup = {
      module,
      filter,
      formRef,
      dataSource,
      addItemVisible,
      dataForm,
      pagination,
      bindData,
      addItem,
      handleOk,
      deleteData,
      formatter,
      parser,
      formatNumber,
      setDefaultAnnualBudget,
    };
    provide("main", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped></style>
