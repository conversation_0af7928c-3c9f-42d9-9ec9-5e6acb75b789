<template>
  <BasicNode/>
</template>

<script>
import {provide} from "vue";
import {findTagList} from "@/api/audience";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (data.tags) {
        const addCodes = data.tags.filter((it) => it.add && it.code).map((it) => it.code);
        if (addCodes.length > 0) {
          const params = {expression: `code in ${  addCodes}`};
          const addTags = await findTagList(params);
          if (addTags && addCodes.length > 0) {
            const addTagNames = addTags.map((it) => it.name);
            brief.push({sort: 0, label: "增加标签", value: addTagNames});
          }
        }
        const delCodes = data.tags.filter((it) => !it.add && it.code).map((it) => it.code);
        if (delCodes.length > 0) {
          const params = {expression: `code in ${  delCodes}`};
          const delTags = await findTagList(params);
          if (delTags && delCodes.length > 0) {
            const delTagNames = delTags.map((it) => it.name);
            brief.push({sort: 1, label: "去掉标签", value: delTagNames});
          }
        }
      }
      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    const item = nodeApi.find(item => { return item.type === 'tag' })
    const setup = {
      title: item.name || "客户标签",
      summary: item.name || "客户标签节点",
      iconClass: item.icon || "icon-tag",
      nodeClass: "easyflow-node-tag",
      headerColor: item.themeColor|| "#00b42a",
      headerBgColor: item.themeColor || "#00b42a",
      background: item.background || "#e8ffea",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
