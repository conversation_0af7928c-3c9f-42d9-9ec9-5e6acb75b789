<template>
  <div class="easyflow-pannel-outbound">
    <a-form
      class="easyflow-pannel-form pannel-form"
      layout="vertical"
      :model="entity"
      :disabled="!editEnable"
    >
      <a-form-item label="人群交集" :content-flex="false">

      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";

const props = defineProps(["node"]);
const node = props.node;
const loading = ref(false);
const pannelInject = inject("pannel");
const editEnable = pannelInject.editEnable;
const entity = ref({

});

const save = () => {
  return entity.value;
};

defineExpose({
  save,
});
onMounted(() => {
  Object.assign(entity.value, node.data);
});
</script>


<style lang="less" scoped>
.audience-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 10px 0;
}
.arco-icon-delete {
  cursor: pointer;
}
</style>
