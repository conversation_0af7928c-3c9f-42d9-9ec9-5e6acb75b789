<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { shortDatetime } from "@/utils/date";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";
import { findAudienceItem, getCrowdSnapshot } from "@/api/audience";
import moment from "moment";


export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup () {
    const getBrief = async (data) => {
      const brief = [];
      if(data.audienceId) {
        if(!data.audienceName){
          const audience = await findAudienceItem(data.audienceId);
          data.audienceName = audience.name;
        }
        brief.push({ sort: 0, label: "人群", value: data.audienceName });
      }

      if (data.snapshotId) {
        if(!data.snapshotName){
          const { content } = await getCrowdSnapshot({ expression: `id eq ${data.snapshotId}` });
          if (content.length > 0) {
            data.snapshotName = moment(content[0].createDate).format('YYYY-MM-DD HH:mm:ss')
          }
        }
        brief.push({ sort: 0, label: "快照", value: data.snapshotName });
      }

      if (data.schedule?.type === "DATE") {
        brief.push({ sort: 1, label: "频次", value: "单次" });
        brief.push({
          sort: 2,
          label: "时间",
          value: shortDatetime(data.schedule.date),
        });
      } else if (data.schedule?.type === "CRON") {
        brief.push({ sort: 1, label: "频次", value: "多次" });
        brief.push({
          sort: 2,
          label: "开始时间",
          value: shortDatetime(data.schedule.startTime),
        });
        brief.push({
          sort: 3,
          label: "结束时间",
          value: shortDatetime(data.schedule.endTime),
        });
      }

      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'audience_receive' })
    const config = {
      title: item.name || "人群包",
      summary: item.name || "人群包节点",
      iconClass: item.icon || "icon-user-info",
      nodeClass: "easyflow-node-audience",
      headerColor: item.themeColor || "#39bcc5",
      headerBgColor: item.themeColor || "#39bcc5",
      background: item.background || "#f2fcfa",
      getBrief,
    };

    provide("node", config);
    return config;
  },
  data () {
    return {
      monitor: false,
    };
  },
};
</script>
