export default {
  "menu.dashboard": "Dashboard",
  "menu.dashboard.workplace": "Dashboard",
  "menu.activity": "activity monitoring",
  "menu.dashboard.workplace.home": "Dashboard-chart",
  menu:{
    title: "Dashboard",
    activityMonitoring: "Campaign Monitoring",
    all: "All",
    status: {
      executing: "Executing",
      paused: "Paused",
      completed: "Completed",
      pendingApproval: "Pending Approval",
      draft: "Draft",
    }
  },
  searchTable: {
    form:{
      activityName: "Campaign Name",
      canvasNumber: "Canvas Number",
      activityCanvas: "Campaign Canvas",
      status: "Status",
      startDate: "Start Date",
      endDate: "End Date"
    },
    operation:{
      search: "Search",
      reset: "Reset",
      collapse: "Collapse",
      expand: "Expand",
      view: "View",
    },
    columns:{
      activityNumber: "Campaign Number",
      activityName: "Campaign Name",
      canvasNumber: "Canvas Number",
      activityCanvas: "Campaign Canvas",
      status: "Status",
      activityCategory: "Campaign Category",
      startDate: "Start Date",
      endDate: "End Date",
      action: "Actions",
    },
    reminder: {
      input_activity_name: "Enter the Campaign name",
      input_canvas_number: "Enter the canvas number",
      input_activity_canvas: "Enter the Campaign canvas",
      select_status: "Select the status",
      select_start_date: "Select the start date",
      select_end_date: "Select the end date"
    }
  },
};


