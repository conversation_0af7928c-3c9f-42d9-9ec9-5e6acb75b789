<template>
  <div class="field-item">
    <div class="item-input field-select">
      <!-- 字段名选择 -->
      <span v-if="item.type" class="type-text">{{ item.type }}</span>
      <a-tree-select
        v-model="item.name"
        :allow-search="true"
        :data="dataModelFields"
        allow-clear
        :filter-tree-node="filterTreeNode"
        :field-names="treeFieldStruct"
        placeholder="请选择字段"
        @change="changeTreeSelect($event, item)"
      >
      </a-tree-select>
    </div>
    <div class="item-input">
      <!-- 操作符选择 -->
      <a-select
        v-model="item.operator"
        class="item-input"
        placeholder="操作符"
        allow-clear
        @change="changeOperator(item)"
      >
        <template v-if="item.purpose == 'tag'">
          <a-option value="include" label="包含"></a-option>
          <a-option value="exclude" label="不包含"></a-option>
        </template>
        <template v-if="item.purpose != 'tag'">
          <template v-if="item.type != null && item.purpose != 'tags'">
            <a-option value="empty" label="是否为空"></a-option>
          </template>
          <template v-if="item.type != null && !isNested(item.type)">
            <a-option value="eq" label="等于"></a-option>
            <a-option value="ne" label="不等于"></a-option>
          </template>
          <template v-if="isText(item.type)">
            <a-option value="like" label="模糊匹配"></a-option>
          </template>
          <template v-if="item.array">
            <a-option value="include" label="包含"></a-option>
            <a-option value="exclude" label="不包含"></a-option>
          </template>
          <template v-if="comparable(item.type)">
            <a-option value="lt" label="小于"></a-option>
            <a-option value="le" label="小于等于"></a-option>
            <a-option value="ge" label="大于等于"></a-option>
            <a-option value="gt" label="大于"></a-option>
          </template>
          <template v-if="item.type == 'date'">
            <a-option value="af" label="相对时间大于等于"></a-option>
            <a-option value="bf" label="相对时间小于"></a-option>
            <a-option value="monthEq" label="月份在"></a-option>
            <a-option value="date" label="日期在"></a-option>
            <a-option value="monthPeriod" label="时间在"></a-option>
            <a-option value="in" label="在...范围内"></a-option>
            <a-option value="nin" label="不在...范围内"></a-option>
          </template>
        </template>
      </a-select>
    </div>
    <div class="item-input">
      <template v-if="item.purpose == 'tag'">
        <a-select
          v-model="item.value"
          style="margin: 0 10px"
          placeholder="请选择标签"
          allow-search
          allow-clear
        >
          <a-option v-for="tag of tags" :key="tag.code" :value="tag.code">{{
            tag.name
          }}</a-option>
        </a-select>
      </template>
      <!-- 值选择 -->
      <template
        v-else-if="
          item.type === 'date' &&
          ['eq', 'ne', 'lt', 'le', 'ge', 'gt', 'in', 'nin'].includes(
            item.operator
          )
        "
      >
        <a-date-picker
          v-model="item.value"
          style="width: 100%"
          placeholder="请选择时间"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
        />
      </template>
      <template
        v-else-if="item.type === 'date' && ['af', 'bf'].includes(item.operator)"
      >
        <a-select v-model="timeDirection" placeholder="时间">
          <a-option value="+">未来</a-option>
          <a-option value="-">过去</a-option>
        </a-select>
        <a-input-number
          v-model="timeValue"
          :min="1"
          placeholder="数值"
          mode="button"
          :precision="0"
        />
        <a-select v-model="timeUnit" placeholder="单位">
          <a-option value="y">年</a-option>
          <a-option value="M">月</a-option>
          <a-option value="d">日</a-option>
          <a-option value="!y">整年</a-option>
          <a-option value="!M">整月</a-option>
          <a-option value="!d">整日</a-option>
        </a-select>
      </template>
      <template
        v-else-if="item.type === 'date' && ['monthEq'].includes(item.operator)"
      >
        <a-select v-model="item.value" placeholder="单位">
          <a-option value="1">1月</a-option>
          <a-option value="2">2月</a-option>
          <a-option value="3">3月</a-option>
          <a-option value="4">4月</a-option>
          <a-option value="5">5月</a-option>
          <a-option value="6">6月</a-option>
          <a-option value="7">7月</a-option>
          <a-option value="8">8月</a-option>
          <a-option value="9">9月</a-option>
          <a-option value="10">10月</a-option>
          <a-option value="11">11月</a-option>
          <a-option value="12">12月</a-option>
        </a-select>
      </template>
      <template
        v-else-if="item.type === 'date' && ['date'].includes(item.operator)"
      >
        <a-select v-model="timeDirection" placeholder="时间">
          <a-option value="+">未来</a-option>
          <a-option value="-">过去</a-option>
        </a-select>
        <a-input-number
          v-model="timeValue"
          :min="1"
          placeholder="数值"
          mode="button"
          :precision="0"
        />
        <a-select v-model="timeUnit" placeholder="单位">
          <a-option value="M">月</a-option>
          <a-option value="d">日</a-option>
        </a-select>
      </template>
      <template
        v-else-if="
          item.type === 'date' && ['monthPeriod'].includes(item.operator)
        "
      >
        <a-select v-model="timeDirection" placeholder="时间">
          <a-option value="+">未来</a-option>
          <a-option value="-">过去</a-option>
        </a-select>
        <a-input-number
          v-model="timeValue"
          :min="1"
          placeholder="数值"
          mode="button"
          :precision="0"
        />
        <a-select v-model="timeUnit" placeholder="单位">
          <a-option value="F">月上旬</a-option>
          <a-option value="L">月下旬</a-option>
        </a-select>
      </template>
      <template
        v-else-if="item.type === 'boolean' || item.operator === 'empty'"
      >
        <a-select v-model="item.value" placeholder="请选择类型">
          <a-option value="true">是</a-option>
          <a-option value="false">否</a-option>
        </a-select>
      </template>
      <template v-else>
        <a-tooltip :content="item.value">
          <a-input
            v-if="!item.dynamic"
            v-model="item.value"
            class="item-input"
            placeholder="请输入值"
            allow-clear
          >
            <template #prefix>
              <icon-pen-fill class="item-icon" @click="item.dynamic = true" />
            </template>
          </a-input>
          <a-tree-select
            v-if="item.dynamic"
            v-model="item.value"
            :allow-search="true"
            :data="dataModelFields"
            allow-clear
            class="item-input"
            :field-names="treeFieldStruct"
            placeholder="请选择字段"
          >
            <template #prefix>
              <icon-mind-mapping
                class="item-icon"
                @click="item.dynamic = false"
              />
            </template>
          </a-tree-select>
        </a-tooltip>
        <!-- <a-button :type="filterType(item.fix)" size="small" @click="item.fix = !item.fix"/> -->
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { treeFieldStruct } from "@/constant/common";
import { isNested, isText, isDate, comparable } from "@/utils/field";

// const emit = defineEmits(['onChangeData'])

const props = defineProps({
  addEnabled: Boolean,
  dataModelFields: {
    // 字段选择
    type: Array,
    default: () => {
      return [];
    },
  },
  tags: {
    type: Array,
    default: () => {
      return [];
    },
  },
  field: {
    type: Object,
    default: () => {
      return {
        name: "",
        type: "",
        operator: "",
        array: false,
        dynamic: false,
        value: "",
      };
    },
  },
});
const loading = ref(false);
const item = ref(props.field);

// const tags = ref([]);
const filterType = (fix) => {
  if (fix) {
    return "primary";
  }
  return "dashed";
};

// 获取字段数据
const getCurItem = (data, val) => {
  let result = null;
  if (!data) return; // return; 中断执行
  for (const i in data) {
    if (result !== null) break;
    const item = data[i];
    if (item.path === val) {
      result = item;
      break;
    } else if (item?.fields?.length) {
      result = getCurItem(item.fields, val);
    }
  }
  return result;
};

const filterTreeNode = (searchValue, nodeData) => {
  return nodeData.label.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
};

// 选择字段
const changeTreeSelect = (val, _item) => {
  const x = getCurItem(props.dataModelFields, val);
  _item.array = x?.array;
  _item.type = x?.type;
  _item.fieldType = x?.type;
  _item.aliasName = x?.aliasName;
  _item.purpose = x?.purpose;
  // 清除数据
  _item.name = x?.path;
  _item.operator = "";
  _item.value = "";
};

// 条件选择时清空
const changeOperator = (_item) => {
  _item.value = "";
};

const timeDirection = ref("");
const timeValue = ref(null);
const timeUnit = ref("");

// 监听三个变量的变化
watch(
  [timeDirection, timeValue, timeUnit],
  ([dir, val, unit]) => {
    if (dir && val && unit) {
      if (dir === "+") {
        dir = "";
      }
      if (unit === "!y" || unit === "!M" || unit === "!d") {
        const [symbol, timeUnit] = unit.split(""); // 分解符号和单位
        item.value.value = `${symbol}${dir}${val}${timeUnit}`;
      } else {
        item.value.value = `${dir}${val}${unit}`;
      }
    } else {
      // item.value.value = ""; // 清空无效输入
    }
  },
  { immediate: true }
);
</script>

<style lang="less" scoped>
.field-select {
  max-width: 150px;
}

.field-item {
  display: flex;
  align-items: center;

  .btn {
    margin-right: 5px;

    &:last-child {
      margin-right: 0;
    }
  }

  .item-input {
    margin-right: 5px;
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;

    &:nth-child(2) {
      flex: 0.3;
      min-width: 120px;
    }

    .type-text {
      background-color: rgb(var(--primary-6));
      font-size: 8px;
      color: #ffffff;
      position: absolute;
      top: -18px;
      right: 0;
      padding: 2px 5px;
      zoom: 0.7;
    }

    .item-icon {
      color: rgb(var(--primary-5));
      cursor: pointer;
    }
  }
}
</style>
