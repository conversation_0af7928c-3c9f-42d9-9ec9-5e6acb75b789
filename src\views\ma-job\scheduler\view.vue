/**
*by:<EMAIL> on 2022/10/18 0021
*/
<template>
  <module view>
    <template #main>
      <div v-if="columnsTable.length > 0" class="table-body">
        <div class="table-item">
          <a-table :columns="columnsTable" :pagination="false" :bordered="false" :column-resizable="true"
            :data="dataSource?.leftAudienceList" />
        </div>
        <div class="table-item">
          <a-table :columns="columnsTable" :pagination="false" :bordered="false" :column-resizable="true"
            :data="dataSource?.rightAudienceList" />
        </div>
      </div>

      <a-result v-else status="500" subtitle="客户模型没有可显示字段"></a-result>
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { findViewPage, findTaskItem } from "@/api/task";
import { findCustomerModel } from "@/api/system";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    // 路由API
    const route = useRoute();
    // 模块设置
    const module = ref({
      entityName: "",
      breadcrumb: [
        {
          name: "任务管理",
          path: "/task/list",
        },
        {
          name: "",
        },
      ],
      mainPath: "/task/list",
    });

    // 过滤设置
    const filter = ref([]);

    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = ref({
      leftAudienceList: [],
      rightAudienceList: [],
    });

    // 查询API
    const bindData = async () => {
      const task = await findTaskItem(route.query.id);
      module.value.entityName = `${task.name}`;
      module.value.breadcrumb[1].name = module.value.entityName;
      dataSource.value = await findViewPage(route.query.id);
    };

    const columnsTable = ref([]);
    findCustomerModel().then((data) => {
      if (data) {
        data.forEach((item) => {
          columnsTable.value.push({
            title: item.aliasName,
            dataIndex: `${item.name}`,
            width: 100,
          });
        });
      }
    });

    const setup = {
      t,
      module,
      filter,
      entity,
      bindData,
      dataSource,
      columnsTable,
    };
    provide("view", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
:deep(.general-card) {
  margin: 0;
}

.table-body {
  display: flex;
  align-items: flex-start;
  padding: 20px;

  .table-item {
    width: calc(50% - 10px);

    &:last-child {
      margin-left: 10px;
    }

    &:first-child {
      margin-right: 10px;
    }
  }
}
</style>
