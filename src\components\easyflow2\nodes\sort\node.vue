<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      brief.push({ sort: 0, label: "排序方式", value: data.sortType === "ASC" ? "从小到大" : "从大到小" });
      brief.push({ sort: 1, label: "排序字段", value: data.sortFieldAliasName });

      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'sort' })
    const setup = {
      title: item.name || "排序",
      summary: item.name || "排序节点",
      iconClass: item.icon || "icon-sort-fill",
      nodeClass: "easyflow-node-sort",
      headerColor: item.themeColor|| "#fba980",
      headerBgColor: item.themeColor || "#fba980",
      background: item.background || "#fff9ef",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
