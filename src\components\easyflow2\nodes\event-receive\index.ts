import { Graph } from "@antv/x6";
import { getPorts, rect } from "@/components/easyflow2/components/node";
import EventReceiveNode from "./node.vue";
import EventReceivePannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "event_receive",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<EventReceiveNode />`,
      components: {
        EventReceiveNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("EventReceiveNode", nodeData.node, true);
};

const getModelId = (data: any) => {
  const result = new Set();
  if(data.behaviorModelId){
    result.add(`Behavior:${data.behaviorModelId}`);
  }
  if (data.mappingCustomer) {
    result.add("Customer:customer");
  }
  return result;
};

const EventReceive = {
  type: "event_receive",
  name: "接收事件",
  shape: "EventReceiveNode",
  iconClass: "icon-event",
  color: "#ffffff",
  themebg: "#39BCC5",
  skippable: false,
  pannel: EventReceivePannel,
  help: Help,
  registerNode,
  getModelId,
  nodeData,
  auth: [
    "export_task_record"
  ]
};

export default EventReceive;
