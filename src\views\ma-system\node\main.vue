/** {{$t('systemSetting.canvasNodeManagement.huoDongFenLei')}} */
<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <a-button v-permission="['ma_menu.system.node-config.create']" type="primary" @click="editItem()">
          <template #icon>
            <icon-plus />
          </template>
          {{ t('global.button.create')}}
        </a-button>
      </template>
      <template #main>
        <a-table :bordered="false" :data="dataSource" :pagination="false">
          <template #columns>
            <a-table-column :title="t('systemSetting.canvasNodeManagement.bianHao')" data-index="id" />
            <a-table-column :title="t('systemSetting.canvasNodeManagement.tuBiao')" data-index="icon">
              <template #cell="{ record }">
                <div :class="[`iconfont ${record.icon}`]"></div>
              </template>
            </a-table-column>
            <a-table-column :title="t('systemSetting.canvasNodeManagement.jieDianMingCheng')" data-index="name" />
            <a-table-column :title="t('systemSetting.canvasNodeManagement.leiXing')" data-index="category">
              <template #cell="{ record }">
                {{ record.category === 'SYSTEM' ? t('systemSetting.canvasNodeManagement.xiTong') : t('systemSetting.canvasNodeManagement.kuoZhan') }}
              </template>
            </a-table-column>
            <a-table-column :title="t('systemSetting.canvasNodeManagement.fenLei')" data-index="groupId">
              <template #cell="{ record }">
                {{ filters(nodeGroups, record.groupId) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('systemSetting.canvasNodeManagement.zhuangTai')" data-index="enabled">
              <template #cell="{ record }">
                {{ filters(booleanStatus, record.enabled) }}
              </template>
            </a-table-column>
            <a-table-column :title="t('systemSetting.canvasNodeManagement.paiXu')" data-index="index" />
            <a-table-column :title="t('systemSetting.canvasNodeManagement.beiZhu')" data-index="summary" :ellipsis="true" :tooltip="{ class: 'tooltip-content' }" />
            <a-table-column :title="t('global.button.operation')" :align="'center'">
              <template #cell="{ record }">
                <a-button v-permission="['ma_menu.system.node-config.modify']" type="text" size="small"
                  @click="editItem(record.id)">{{t('global.button.edit')}}</a-button>
                <a-button v-if="record.category !== 'SYSTEM'" v-permission="['ma_menu.system.node-config.delete']" type="text"
                  size="small" @click="deleteData(record.id)">{{t('global.button.delete')}}</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
  </div>
</template>

<script>
import { provide, ref ,getCurrentInstance} from "vue";
import { useRouter } from "vue-router";
import { Modal } from "@arco-design/web-vue";
import { findNodeGroupList } from "@/api/node-group";
import { findNodePage, deleteNode } from "@/api/node";
import { filters } from "@/utils/filter";
import { booleanStatus } from "@/constant/common";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          name: t('menu.system'),
          path: "/system/setting"
        },
        {
          name: t('systemSetting.canvasNodeManagement.canvasNodeManagement')
        }
      ],
      editPath: "/system/node-config/edit",
      createPath: "/system/node-config/edit"
    });

    const editCategoryRef = ref(null);
    const dataSource = ref([]);
    const nodeGroups = ref([]);
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true
    });
    const filter = ref([
      {
        field: "name",
        label: t('systemSetting.canvasNodeManagement.jieDianMingCheng'),
        component: "a-input",
        operate: "like",
        placeholder: t('systemSetting.canvasNodeManagement.QSRFLMC'),
        comment: true,
        value: ""
      },
      {
        field: "enabled",
        label: t('systemSetting.canvasNodeManagement.zhuangTai'),
        component: "a-select",
        operate: "eq",
        dataSource: [
          { value: "true", label: t('global.button.enable') },
          { value: "false", label: t('global.button.disable') }
        ],
        placeholder: t('systemSetting.canvasNodeManagement.qingXuanZeZhuangTai'),
        value: ""
      },
      {
        field: "category",
        label: t('systemSetting.canvasNodeManagement.leiXing'),
        component: "a-select",
        operate: "eq",
        dataSource: [
          { value: "SYSTEM", label: t('systemSetting.canvasNodeManagement.xiTong') },
          { value: "FLOW_TEMPLATE", label: t('systemSetting.canvasNodeManagement.kuoZhan') }
        ],
        placeholder: t('systemSetting.canvasNodeManagement.leiXing'),
        value: ""
      }
    ]);

    const getNodeGroupList = async () => {
      const res = await findNodeGroupList();
      nodeGroups.value = res.map((item) => {
        return { value: item.id, label: item.name };
      });
    };

    const bindData = async (expression) => {
      await getNodeGroupList();
      const pageData = await findNodePage(
        {
          ...pagination.value,
          page: pagination.value.page - 1
        },
        {
          expression
        }
      );
      // pageData.content.map((item) => {
      //   if (item.icon !== "<>") {
      //     item.icon = item.icon.replace('width="200"', 'width="45"');
      //     item.icon = item.icon.replace('height="200"', 'height="45"');
      //   }
      //   return "";
      // });
      dataSource.value = pageData.content;
      pagination.value.total = pageData.totalElements;
    };

    // const create = () => router.push({ path: module.value.createPath });
    const editItem = async (id) => {
      const query = { id };
      router.push({ path: module.value.editPath, query });
      // if (item) {
      //   await editCategoryRef.value.showEdit(item);
      // } else {
      //   await editCategoryRef.value.showCreate();
      // }
      // await bindData();
    };
    const deleteData = async (id) => {
      Modal.confirm({
        title: t('global.button.delete'),
        content: t('global.tips.warning.delete'),
        onOk: async () => {
          await deleteNode(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1;
          }
          await bindData();
        }
      });
    };

    const setup = {
      t,
      module,
      filter,
      editCategoryRef,
      dataSource,
      pagination,
      bindData,
      // create,
      editItem,
      deleteData,
      filters,
      nodeGroups,
      booleanStatus
    };
    provide("main", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped></style>
