<template>
    <a-modal v-model:visible="modelVisible" title="保存画布模板" modal-class="save-template-model" @before-ok="handleOk"
    @before-cancel="handleCancel">
  <a-form ref="dataFormRef"  :model="entity">
    <a-form-item label="模板名称" field="name">
      <a-input v-model="entity.name" placeholder="请输入模板名称" />
    </a-form-item>
    <a-form-item label="模板分类" field="category">
      <a-input v-model="entity.category" placeholder="请输入模板分类" />
    </a-form-item>
    <a-form-item label="模板标签" field="tags">
      <a-select v-model="entity.tags" placeholder="请输入标签，按回车添加" allow-create allow-clear multiple />
    </a-form-item>
    <a-form-item label="描述" field="summary">
      <a-input v-model="entity.summary" placeholder="请输入模板描述" />
    </a-form-item>
  </a-form>
</a-modal>
</template>

<script setup>
import { ref } from "vue";
import { modalCommit } from "@/utils/modal";
import { createTemplate } from "@/api/campaign";

const modelVisible = ref(false);
const dataFormRef = ref();
const callback = ref(null);

const entity = ref({
  setting: {
    type: "FLOW",
    flowData: ""
  }
});

const show = (flowData) => {
  modelVisible.value = true;
  entity.value.setting.flowData = flowData;
  return new Promise(async (resolve, reject) => {
    callback.value = resolve;
  });
};

const getData = () => {
  return entity.value;
};

const handleOk = async (done) => {
  const res = await dataFormRef.value.validate();
  if (res) {
    done(false);
    return;
  }

  entity.value.timestamp = new Date();
  await createTemplate(entity.value);
  callback.value(entity.value);
};

const handleCancel = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    callback.value();
  });
};

defineExpose({
  show,
  getData
});
</script>
<style  lang="less" scoped>

</style>
