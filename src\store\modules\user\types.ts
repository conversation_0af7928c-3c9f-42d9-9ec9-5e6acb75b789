import { grantedApplication } from "@/types/global";
export type RoleType = "" | "*" | "admin" | "user";
export interface UserState {
  info?: object;
  bussinessUnit?: any;
  authorized?: boolean;
  loginPage: string;
  platformUserPreference?: string;
  systemImgUrl?: string;
  userAuthInfo: {
    email: string;
    realName: string;
    tenantId: string;
    username: string;
    tenantName: string;
    authorities: any[];
    menus: any[];
    grantedApplications: grantedApplication[];
  };
}
