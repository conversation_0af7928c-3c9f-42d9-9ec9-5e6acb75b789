<template>
  <a-table :bordered="false" :data="dataSource" :pagination="false">
    <template #columns>
      <a-table-column :title="t('systemSetting.behaviorEvent.listenerId')" data-index="id" :width="150" />
      <a-table-column :title="t('systemSetting.behaviorEvent.listenerType')" data-index="type" :width="200" />
      <a-table-column :title="t('systemSetting.behaviorEvent.listenerFlowId')" data-index="setting.flowId" :width="200" />
      <a-table-column :title="t('systemSetting.behaviorEvent.listenerStartTime')" data-index="startTime" :width="80" />
      <a-table-column :title="t('systemSetting.behaviorEvent.listenerEndTime')" data-index="endTime" :width="80" />
      <a-table-column :title="t('global.button.operation')" :width="80" />
    </template>
  </a-table>
</template>

<script setup>
import { ref, inject, onMounted, getCurrentInstance } from "vue";

const {
      proxy: { t }
    } = getCurrentInstance()
const dataSource = ref();

const findListenerList = () => {

};

</script>

<style lang="less" scoped></style>
