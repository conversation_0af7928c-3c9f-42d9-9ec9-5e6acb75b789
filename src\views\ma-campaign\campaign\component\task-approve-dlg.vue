<template>
  <a-modal v-model:visible="modelVisible" title="审批活动" modal-class="campaign-approve-model" @before-ok="handleOk"
    @before-cancel="handleCancel">
    <a-form ref="dataFormRef" :model="dataForm">
      <a-form-item label="审批结果" field="type" :rules="[{ required: true, message: '请选择审批结果' }]">
        <a-select v-model="dataForm.type" placeholder="请选择审批结果">
          <a-option value="APPROVE">通过</a-option>
          <a-option value="REJECT">拒绝</a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="提交说明" field="note" :help="noteValidateText" :validate-status="status">
        <a-textarea v-model="dataForm.note" placeholder="请输入提交审批说明" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { modalCommit } from "@/utils/modal";
import { Modal } from "@arco-design/web-vue";
import { approveCampaign } from "@/api/campaign";

const callback = ref(null);
const modelVisible = ref(false);
const dataFormRef = ref();
const dataForm = ref({});
const status = ref("validating");
const noteValidateText = ref("");

const show = (campaignId) => {
  modelVisible.value = true;
  dataForm.value = {};
  dataForm.value.campaignId = campaignId;
  return new Promise(async (resolve, reject) => {
    callback.value = resolve;
  });
};

const handleOk = async (done) => {
  const res = await dataFormRef.value.validate();
  if (res) {
    done(false);
    return;
  }
  if (dataForm.value.type == "REJECT" && !dataForm.value.note) {
    status.value = "error";
    noteValidateText.value = "审批拒绝请填写原因";
    done(false);
  } else {
    dataForm.value.timestamp = new Date();
    await approveCampaign(dataForm.value);
    callback.value(dataForm.value);
  }
};

const handleCancel = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    callback.value();
  });
};
defineExpose({
  show,
});
</script>
