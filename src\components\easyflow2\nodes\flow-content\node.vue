<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { getContent } from "@/api/flow-content";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  props: ['nodeItem'],
  setup(props) {
    const getBrief = async (data) => {
      const brief = [];
      if(!data.flowContentName){
        const itemCure =  await getContent(data.flowContentId)
        data.flowContentName = itemCure ? itemCure.name : '';
      }
      brief.push({ sort: 0, label: "触达内容", value: data.flowContentName });
      brief.push({
        sort: 1,
        label: "频次限制",
        value: data.limit ? "是" : "否",
      });

      return brief;
    };
    const item = props?.nodeItem || JSON.parse(localStorage.getItem('nodeItem'))
    const config = {
      title: item?.name || "flow",
      summary: item?.name || "flow节点",
      iconClass: item?.icon || "icon-start",
      nodeClass: "easyflow-node-event-receive",
      headerColor: item?.themeColor || "#39bcc5",
      headerBgColor: item?.themeColor || "#39bcc5",
      background: item?.background || "#f2fcfa",
      getBrief,
    };

    provide("node", config);
    return config;
  },
  data() {
    return {
      monitor: false,
    };
  },
};
</script>
