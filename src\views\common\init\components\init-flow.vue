<template>
  <a-alert type="warning">
    Flow相关配置。
  </a-alert>
  <a-form ref="formRef" :model="formData">
    <a-form-item field="setting.publishId" label="Flow模板同步流程" label-col-flex="170px">
      <a-select v-model="formData.flowTemplateSyncPublishId" class="easyflow-select" placeholder="请选择Flow模板同步流程" :loading="loading"
        :filter-option="false" @search="handleSearchFlow" @change="handleSearchFlowStarts(formData.flowTemplateSyncPublishId)">
        <a-option v-for="item of flows" :key="item.id" :value="item.id">{{ item.name }}</a-option>
      </a-select>
    </a-form-item>
    <a-form-item field="startId" label="流程起点" label-col-flex="170px">
      <a-select v-model="formData.flowTemplateSyncStartId" class="easyflow-select" placeholder="请选择Flow流程开始节点"
        :loading="loading" :filter-option="false">
        <a-option v-for="item of starts" :key="item.taskId" :value="item.taskId">{{ item.name }}</a-option>
      </a-select>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { findFlowList, findFlowStarts } from "@/api/campaign";

const props = defineProps(["setting"]);
const formRef = ref(null);
const formData = ref(props.setting);
const loading = ref(false);
const flows = ref([]);
const starts = ref([]);

const handleSearchFlow = async (name) => {
  loading.value = true;
  const params = { fields: "name" };
  params.expression = "status eq ENABLED";
  if (name) {
    params.expression += `AND name like ${name}`;
  }
  flows.value = await findFlowList(params);
  loading.value = false;
};

const handleSearchFlowStarts = async (publishId) => {
  starts.value = [];
  // entity.value.setting.startId = "";
  loading.value = true;
  starts.value = await findFlowStarts(publishId);
  loading.value = false;
};

const commit = async (entity) => {
  if (await formRef.value.validate()) {
    return false;
  }
  entity.value.setting.flowSetting = formData.value;
  return true;
}

onMounted(async () => {
  await handleSearchFlow();
  if (formData.value.flowTemplateSyncPublishId) {
    await handleSearchFlowStarts(formData.value.flowTemplateSyncPublishId);
  }
});

defineExpose({
  commit
})
</script>

<style lang="less" scoped>

.arco-alert {
    margin: 10px;
  }

</style>
