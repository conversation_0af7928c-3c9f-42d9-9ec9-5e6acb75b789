import { defineStore } from "pinia";
import {
  login as userLogin,
  logout as userLogout,
  getUserInfo,
  LoginData,
} from "@/api/user";
import { setToken, clearToken } from "@/utils/auth";
import { removeRouteListener } from "@/utils/route-listener";
import { UserState } from "./types";
import useUserDataRoleStore from "../user-data-role";

const useUserStore = defineStore("user", {
  state: (): UserState => ({
    loginPage: "",
    systemImgUrl: "",
    userAuthInfo: {
      email: "",
      realName: "",
      tenantId: "",
      username: "",
      tenantName: "",
      menus: [],
      authorities: [],
      grantedApplications: [],
    },
    authorized: false,
    platformUserPreference: "",
  }),

  getters: {
    userInfo(state: UserState): UserState {
      return { ...state };
    },
  },

  actions: {
    // 设置角色
    switchRoles() {},
    // 设置用户信息
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
    },

    // 重置用户信息
    resetInfo() {
      this.$reset();
    },

    // 获取用户信息
    async info() {
      const res: any = await getUserInfo();
      const useDataRole = useUserDataRoleStore();
      useDataRole.getUserDataRole();
      localStorage.setItem("userAuthInfo", JSON.stringify(res.userAuthInfo));
      this.setInfo(res);
    },

    // 登陆
    async login(loginForm: LoginData) {
      try {
        const token: any = await userLogin(loginForm);
        setToken(token);
      } catch (err) {
        clearToken();
        throw err;
      }
    },

    // 退出
    async logout() {
      await userLogout();

      this.resetInfo();
      clearToken();
      removeRouteListener();
    },
  },
});

export default useUserStore;
