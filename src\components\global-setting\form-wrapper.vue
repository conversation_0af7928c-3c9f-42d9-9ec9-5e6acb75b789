<template>
  <a-input-number
    v-if="type === 'number'"
    :style="{ width: '80px' }"
    size="small"
    :default-value="defaultValue"
    @change="handleChange"
  />
  <a-switch
    v-else
    :default-checked="defaultValue"
    size="small"
    @change="handleChange"
  />
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: () => "",
    },
    name: {
      type: String,
      default: () => "",
    },
    defaultValue: {
      type: [String, Boolean, Number],
      default: () => "",
    },
  },
  setup(props) {
    const emit = defineEmits(["inputChange"]);
    return {
      emit,
    };
  },
  methods: {
    handleChange(value) {
      this.emit("inputChange", {
        value,
        key: this.name,
      });
    },
  },
};
</script>
