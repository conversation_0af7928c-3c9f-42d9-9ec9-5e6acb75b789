<template>
  <div class="easyflow-pannel-abtest">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="分流器名称">
        <a-input v-model="entity.groupName" />
      </a-form-item>
      <a-form-item label="分组" :content-flex="false">
        <div v-show="false">
          根据
          <a-radio-group v-model="entity.groupType" type="button" size="mini">
            <a-radio border value="RATIO">比率</a-radio>
            <a-radio border value="NUMBER">绝对值</a-radio>
          </a-radio-group>
          计算样本数量
        </div>
        <div v-if="entity.groups.length < 1" class="tip">请先连接后续流程</div>
        <template v-for="(item, index) in entity.groups" :key="index">
          <a-row class="group-item" :gutter="24">
            <a-col :span="6" class="item-label">
              {{ item.name }}
            </a-col>
            <a-col :span="18">
              <a-input-number v-if="entity.groupType == 'RATIO'" v-model="item.ratio" :precision="0">
                <template #append> % </template>
              </a-input-number>
              <a-input-number v-if="entity.groupType == 'NUMBER'" v-model="item.number" :precision="0" />
            </a-col>
          </a-row>
        </template>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { findMetricList } from "@/api/analysis";
import { uuid } from "@/utils/uuid";

const flow = inject("flow");
const props = defineProps(["node", "connections"]);
const loading = ref(false);
const { node } = props;
const { connections } = props;
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const entity = ref({
  groupType: "RATIO",
  groups: [],
});
const metrics = ref([]);
const save = () => {
  entity.value.groups.forEach((it) => {
    if (!it.id) {
      it.id = uuid(20);
    }
  });
  return entity.value;
};
const addItem = (outgoing, name) => {
  return {
    name,
    outgoing,
  };
};
const handleSearchMetric = async (name) => {
  loading.value = true;
  const params = { fields: "name" };
  if (name) {
    params.expression = `name like ${name}`;
  }
  metrics.value = await findMetricList(params);
  loading.value = false;
};

defineExpose({
  save,
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  // handleSearchMetric();
  if (!entity.value.endTime) {
    entity.value.endTime = flow.entity.endTime;
  }
  // 去除无效分支
  if (connections.outgoings) {
    entity.value.groups = connections.outgoings.map((it) => {
      const found = entity.value.groups.find((item, index) => {
        return item.outgoing === it.id;
      });
      if (found) {
        found.name = it.name;
        return found;
      }
      return addItem(it.id, it.name);

    });
  } else {
    entity.value.filters = [];
  }
});
</script>


<style lang="less" scoped>
.group-item {
  margin: 10px 0;

  .item-label {
    text-align: right;
    padding-top: 4px;
    padding-bottom: 4px;
  }

}
.tip {
  font-size: 14px;
  color: #aaa;
  margin-top: 5px;
}
</style>
