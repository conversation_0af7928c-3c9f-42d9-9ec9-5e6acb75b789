import axios from 'axios';
import { HttpResponse } from './interceptor';

// 鉴权请求参数接口
export interface AuthTokenDTO {
  appId: string;
  appSecret: string;
}

// 鉴权响应接口
export interface AuthTokenResponse {
  msg: string;
  code: number;
  expire: number;
  token: string;
}

// 获取token
export function getAuthToken(params: AuthTokenDTO): Promise<AuthTokenResponse> {
  return axios.post('/api/auth/token', params, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// token缓存管理
class TokenManager {
  private token: string | null = null;
  private expireTime: number = 0;
  private readonly appId = 'WAHLAP_F6754955AC0D4E91B47E31402EF90892';
  private readonly appSecret = 'D2377F5335594FE99EADBC4D034557D0';

  // 检查token是否有效
  private isTokenValid(): boolean {
    return this.token !== null && Date.now() < this.expireTime;
  }

  // 获取有效的token
  async getValidToken(): Promise<string> {
    if (this.isTokenValid()) {
      return this.token!;
    }

    try {
      //console.log('正在获取token，参数:', { appId: this.appId, appSecret: this.appSecret });

      const response = await getAuthToken({
        appId: this.appId,
        appSecret: this.appSecret,
      });

      //console.log('获取token响应:', response);

      // 检查响应是否存在（注意：axios拦截器已经返回了response.data）
      if (!response) {
        throw new Error('获取token失败: 服务器响应格式错误');
      }

      // 检查响应状态码
      if (response.code === 0) {
        // 检查token是否存在
        if (!response.token) {
          throw new Error('获取token失败: 服务器未返回token');
        }

        this.token = response.token;
        // 设置过期时间，提前5分钟刷新
        // const expireSeconds = response.expire || 3600; // 默认60分钟
        const expireSeconds = 3600;
        this.expireTime = Date.now() + (expireSeconds - 300) * 1000;
        return this.token!; // 添加非空断言，因为我们已经检查过了
      } else {
        const errorMsg = response.msg || '未知错误';
        throw new Error(`获取token失败: ${errorMsg}`);
      }
    } catch (error: any) {
      console.error('获取token失败:', error);
      // 如果是axios错误，提供更详细的错误信息
      if (error?.response) {
        // 服务器响应了错误状态码
        console.error('服务器响应错误:', error.response.status, error.response.data);
        throw new Error(`获取token失败: 服务器错误 ${error.response.status}`);
      } else if (error?.request) {
        // 请求已发出但没有收到响应
        console.error('网络请求失败:', error.request);
        throw new Error('获取token失败: 网络连接错误');
      } else {
        // 其他错误
        throw error;
      }
    }
  }

  // 清除token缓存
  clearToken(): void {
    this.token = null;
    this.expireTime = 0;
  }
}

// 导出token管理器实例
export const tokenManager = new TokenManager();