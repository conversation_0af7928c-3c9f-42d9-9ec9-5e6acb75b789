<template>
  <div class="easyflow-pannel-filter">
    <div class="filter-config">
      <a-form :model="entity" :disabled="!editEnable" auto-label-width>
        <a-form-item label="支持默认分支">
          <a-checkbox v-model="entity.defaultEnable" class="budget" value="1" />
        </a-form-item>
        <a-form-item label="分支策略">
          <a-select v-model="entity.filterStrategy" class="easyflow-select" :options="filterStrategies" placeholder="请选择分支策略">
            <template #option="{ data }">
              <div class="easyflow-option">
                <span class="name">{{ data?.label }}</span>
                <i class="desc">{{ data.raw?.note }}</i>
              </div>
            </template>
          </a-select>
        </a-form-item>
        <a-form-item label="分支路径" :content-flex="false">
          <div v-if="entity.filters.length < 1" class="tip">请先连接后续流程</div>
          <template v-for="(item, index) in entity.filters" :key="index">
            <div class="input-tab">
              <conditions v-if="flowModel" v-model:condition="entity.filters[index].conditionJson"
                :name="item ? item.name : `节点${index + 1}`" :data-model="flowModel" :tags="tags" class="condition-card" />
              <!-- <h5>标签条件</h5> -->
              <!-- <TagSelect v-if="flowModel" :is-edit="editEnable" :filter="entity.filters[index].tagFilter" /> -->
              <div class="branch-line">
                <div v-if="entity.defaultEnable" class="branch-item">
                  <span class="label">默认</span><a-switch v-model="item.defaultFilter" @change="changeDefault(item)" />
                </div>
                <div v-if="entity.filterStrategy == 'PRIORITY'" class="branch-item">
                  <span class="label">优先级</span><a-input-number v-model="item.priority"
                    style="width: 100px"></a-input-number>
                </div>
              </div>
            </div>
          </template>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, watch, onMounted } from "vue";
import Conditions from "@/components/ma/conditions/index.vue";
// import TagSelect from "@/components/ma/tag-select/index.vue";
import { getFlowModelsByIds } from "@/api/campaign";
import { findTagList } from "@/api/audience";

const props = defineProps(["node", "connections", "easyflow"]);
const { node } = props;
const { connections } = props;
const { easyflow } = props;
const conditionJson = ref({empty: true});
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;
const entity = ref({
  defaultEnable: true,
  filterStrategy: "PRIORITY",
  filters: [],
});
const flowModel = ref(null);
const tags = ref([]);
const branchs = ref([]);

const filterStrategies = ref([
  {
    label: "优先级",
    note: "条件同时满足时，按分支优先级选择分支",
    value: "PRIORITY",
  },
  {
    label: "并行",
    note: "条件同时满足时，执行所有满足条件分支",
    value: "PARALLEL",
  },
]);

// 新增节点
const addItem = (outgoing) => {
  return {
    name: outgoing.name,
    outgoing: outgoing.id,
    iconClass: outgoing.iconClass,
    conditionJson: {empty: true},
    defaultFilter: false,
    priority: 0,
  };
};

const save = () => {
  return entity.value;
};
const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = {
    ...modelFields,
  };
};

const handleSearchTag = async (name) => {
  const params = {
    expression: "delete ne true"
  };
  if (name && name.trim()) {
    params.expression += ` AND name like ${name}`;
  }
  tags.value = await findTagList(params);
};

const changeDefault = (item) => {
  entity.value.filters.forEach((it) => (it.defaultFilter = false));
  item.defaultFilter = true;
};

const refreshBranchs = (outgoings) => {
  branchs.value = outgoings.map((it) => {
    return {
      id: it.id,
      type: it.type,
      iconClass: it.iconClass,
    };
  });
};

const refreshFilter = async () => {
  // 去除无效分支
  if (connections.outgoings) {
    refreshBranchs(connections.outgoings);
    entity.value.filters = connections.outgoings.map((it) => {
      const found = entity.value.filters.find((item, index) => {
        return item.outgoing === it.id;
      });
      if (found) {
        found.name = it.name;
        return found;
      }
      return addItem(it);
    });
    await handelFlowModel();
    await handleSearchTag();
  } else {
    entity.value.filters = [];
  }
};


defineExpose({
  save,
});

onMounted(async () => {
  Object.assign(entity.value, node.data);
  await refreshFilter();
});
</script>

<style lang="less" scoped>
.easyflow-pannel-filter {
  .filter-config {
    margin: 10px;
  }

  .branch-title {
    margin: 0 10px;
  }

  .branch-line {
    padding-left: 30px;
    display: flex;
    flex-flow: row wrap;

    .branch-item {
      width: 50%;

      .label {
        margin-right: 20px;
      }
    }
  }

  .input-name {
    font-size: 14px;
    color: #666666;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
  }

  .input-item {
    margin-bottom: 15px;
  }

  .input-top {
    border-bottom: 1px solid #efefef;
    margin-bottom: 15px;
  }

  .input-tab {
    margin-bottom: 20px;
    background-color: #ffffff;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    padding: 10px;

    .name {
      color: rgb(var(--primary-6));
      font-size: 14px;
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
  }

  .field-select {
    max-width: 150px;
  }

  .tip {
    font-size: 14px;
    color: #aaa;
    margin-top: 5px;
  }
}
</style>
