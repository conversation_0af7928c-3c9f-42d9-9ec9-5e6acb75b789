/** *by:<EMAIL> on 2022/8/10 0010 */
<template>
  <div v-if="isEditVisible" class="AddEditChart">
    <a-modal v-model:visible="isEditVisible" @ok="handleOk" width="900px">
      <template #title>添加新的图表组件</template>
      <module main>
        <template v-slot:filter></template>
        <template v-slot:search></template>
        <template v-slot:context></template>
        <template v-slot:main>
          <a-table
            ref="table"
            :bordered="false"
            :data="dataSource"
            v-model:selectedKeys="selectedKeys"
            row-key="id"
            :row-selection="{
              type: 'checkbox',
              showCheckedAll: true,
              onlyCurrent: false,
            }"
            :pagination="false"
          >
            <template #columns>
              <a-table-column title="图表名称" data-index="name" />
              <a-table-column title="图表类型" data-index="type">
                <template #cell="{ record }">
                  <i
                    class="iconfont"
                    :class="[`icon-chart-${record.type.toLowerCase()}`]"
                  />
                  {{ sourceFilter(record.type) }}
                </template>
              </a-table-column>
            </template>
          </a-table>
        </template>
      </module>
    </a-modal>
  </div>
</template>

<script>
import { computed, defineComponent, provide, ref } from "vue";
import { getChartList, setDataItemChart } from "@/api/dashboard";
import { useBussinessUnitStore } from "@/store";
import { uuid } from "@/utils/uuid.ts";

export default defineComponent({
  name: "AddEditChart",
  emits: ["change"],
  setup(props, { emit }) {
    const isEditVisible = ref(false);
    const userTc = useBussinessUnitStore();
    const buCode = userTc.currentBussinessUnit?.code;
    const tenantId = userTc.currentBussinessUnit?.tenantId;

    // 模块设置
    const module = ref({
      entityIdField: "id",
      showCard: false,
      showBtn: false,
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true,
    });

    // 过滤设置
    const filter = ref([
      {
        field: "name",
        label: "图表名称",
        component: "a-input",
        operate: "like",
        placeholder: "请输入图表名称",
        comment: true,
        value: "",
      },
    ]);

    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    // 查询API
    const bindData = async (expression) => {
      entity.value = await getChartList(
        {
          ...pagination.value,
          page: pagination.value.page - 1,
        },
        {
          expression: expression
            ? expression + ` AND workspaceId eq ${tenantId}_${buCode}`
            : `workspaceId eq ${tenantId}_${buCode}`,
        }
      );
      pagination.value.total = entity.value.totalElements;
    };

    // 确定选择
    const selectedKeys = ref([]);
    const handleOk = () => {
      let saveData = {
        charts: [],
        globalFilter: { values: {}, ranges: {} },
      };
      dataSource.value.forEach((item) => {
        if (selectedKeys.value.includes(item.id)) {
          saveData.charts.push({
            i: uuid(0),
            id: item.id,
          });
        }
      });

      setDataItemChart(saveData).then((res) => {
        isEditVisible.value = false;
        emit("change", res);
      });
    };

    // 筛选
    const sourceFilter = (v) => {
      switch (v) {
        case "Metric":
          return "指标卡";
        case "Pie":
          return "饼图";
        case "Bar":
          return "柱状图";
        case "Funnel":
          return "漏斗图";
        default:
          return "-";
      }
    };

    const setup = {
      module,
      filter,
      entity,
      bindData,
      dataSource,
      pagination,
      isEditVisible,
      handleOk,
      sourceFilter,
      selectedKeys,
    };
    provide("main", setup);
    return setup;
  },
});
</script>
<style lang="less" scoped>
.AddEditChart {
}
:deep(.general-card) {
  min-height: auto;
  margin: 0;
  padding: 0;
}
.iconfont {
  margin-right: 5px;
  color: #999999;
}
</style>
