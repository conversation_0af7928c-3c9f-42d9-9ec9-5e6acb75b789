import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import SortNode from "./node.vue";
import SortPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "sort",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<SortNode />`,
      components: {
        SortNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("SortNode", nodeData.node, true);
};

const Sort = {
  type: "sort",
  name: "排序",
  shape: "SortNode",
  iconClass: "icon-sort-fill",
  registerNode,
  pannel: SortPannel,
  help: Help,
  skippable: false,
  auth: [
    "export_task_record"
  ]
};

export default Sort;
