/**
*by:<EMAIL> on 2022/7/4 0004
*/
<template>
  <div class="form-number">
    <template v-for="(item, index) in rangeSize" :key="item">
      <div class="item-tag">
        {{flag}}{{flag === 'R' ? rangeSize - index : item}}:
        <a-tag size="large" class="tag" color="#e1bee7">
          {{index === 0
            ? 0
            : `${dataList[index] ? dataList[index] + 1 : 0}${(flag !== 'R' && index === dataList.length - 1) ? '+' : ''}`}}
        </a-tag>
        <span class="range-start iconfont icon-forward"></span>
        <a-input-number
            class="item-input"
            :min="dataList[index] + 1"
            :max="flag === 'R' ? entity.period - rangeSize + item : 100000000"
            @change="changeInput"
            v-if="index !== dataList.length - 1"
            v-model="dataList[item]"></a-input-number>
        <a-input-number
            class="item-input"
            v-else-if="flag === 'R'"
            disabled
            v-model="entity.period"></a-input-number>
        <a-input
            class="item-input"
            v-else
            disabled
            default-value="∞"></a-input>
      </div>
    </template>
  </div>
</template>

<script>
import {defineComponent, computed, watch} from 'vue';

export default defineComponent({
  name: '',
  components: {},
  props: {
    targetNumber: {
      type: Array,
      default: () => [],
    },
    flag: {
      type: String,
      default: "LV",
    },
    entity: {
      type: [Array, Object],
      default: () => [],
    },
    rangeSize: {
      type: Number,
      default: 5,
    },
  },
  setup(props, {emit}) {
    const dataList = computed(() => { return props.targetNumber })

    const changeInput = () => {
      dataList.value.forEach((item, index) => {
        if (item > dataList.value[index + 1]) {
          dataList.value[index + 1] = item + 1
        }
      })
      emit('update:targetNumber', dataList.value)
    }
    return {
      dataList,
      changeInput
    }
  },
});
</script>
<style lang="less" scoped>
.form-number{
  .item-tag{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    width: 200px;
  }
  .tag{
    width: 45px;
    justify-content: center;
  }
  .range-start{
    font-weight: bold;
    color: #999999;
  }
  .item-input {
    width: 90px;
  }
}
</style>
