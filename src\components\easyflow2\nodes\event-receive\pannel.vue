<template>
  <div class="easyflow-pannel-start">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <!-- 事件 -->
      <a-form-item key="behaviorId" label="选择事件" :rules="[{ match: /one/, message: 'must select one' }]">
        <a-select v-model="entity.behaviorId" placeholder="请选择事件" allow-search :loading="loading" :filter-option="false"
          :show-extra-options="false" @search="handleSearchBehavior" @change="handelChangeBehavior">
          <a-option v-for="item of behaviors" :key="item.id" :value="item.id">{{ item.name }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="时间范围" :rules="[{ required: true, message: '不能为空' }]">
        <a-range-picker v-model="eventTimeRange" show-time :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
          format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" @ok="handleEventRangeTime" />
        <template #label>
          时间范围
          <a-tooltip>
            <template #content>
              <p>
                活动在运行期间接收指定的时间范围内的行为，超出时间范围的行为数据不处理。
              </p>
            </template>
            <icon-info-circle-fill />
          </a-tooltip>
        </template>
      </a-form-item>
      <a-form-item key="mappingCustomer" label="关联客户">
        <a-switch v-model="entity.mappingCustomer" type="round">
          <template #checked>开</template>
          <template #unchecked>关</template>
        </a-switch>
        <template #label>
          关联客户
          <a-tooltip>
            <template #content>
              <p>活动接收到客户的行为数据时，主动根据行为中的客户信息查找完整的客户数据</p>
              <p>注意： 当客户的行为数据无法找到对应的客户，则不需要关联客户</p>
            </template>
            <icon-info-circle-fill />
          </a-tooltip>
        </template>
      </a-form-item>
      <a-form-item label="去重">
        <a-space>
          <a-switch v-model="entity.unique" type="round" />
          <a-tree-select v-if="entity.unique" v-model="entity.uniqueFields" :multiple="true" :allow-search="true" :data="dataModelFields"
            allow-clear :field-names="treeFieldStruct" placeholder="请选择字段" />
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch, computed } from "vue";
import { findBehaviorEventList, findBehaviorModelItem } from "@/api/behavior";
import { treeFieldStruct } from "@/constant/common"
import { formatFields } from "@/utils/field"
import EasyFlow from "../..";

const { node, easyflow } = defineProps(["node", "easyflow"]);
const timeRange = ref(null);
const eventTimeRange = ref(null);
const pannelInject = inject("pannel");
const flow = inject("flow");
const { editEnable } = pannelInject;

const entity = ref({
  eventCode: null, // 事件代码
  duplicate: false, // 是否允许重复
  duplicateNum: 1, // 允许重复次数
  behaviorId: null, // 行为事件Id
  behaviorModelId: null, // 行为模型Id
  mappingCustomer: false,
  schedule: {
    date: null, // 定时时间
    cron: null, // cron表达式
    type: "DATE", // 定时类型：NONE,DELAY,DATE,CRON
    endTime: null, // 定时结束时间
    startTime: null, // 定时开始时间
    // duration: "PT8H6M12.345S", // 延时时长，格式：PnDTnHnMn.nS，参考 https://blog.csdn.net/weixin_49114503/article/details/121681862
  },
});

const behaviors = ref(null); // 行为模型列表
const loading = ref(false);
const dataModelFields = ref(null);

const handleEventRangeTime = () => {
  entity.value.eventStartTime = eventTimeRange.value[0];
  entity.value.eventEndTime = eventTimeRange.value[1];
};

const handleSearchBehavior = async (value) => {
  const params = { fields: "name,modelId,status" };
  params.expression = `status in ENABLED`
  if (value) {
    params.expression = `name like ${value} AND ${params.expression}`;
  }
  behaviors.value = await findBehaviorEventList(params);
};

const findBehaviorModel = async (id) => {
  if(id){
    const dataModel = await findBehaviorModelItem(id);
    dataModelFields.value = formatFields(dataModel.fields);
  }
};

const handelChangeBehavior = async (id) => {
  const behavior = behaviors.value.find((it) => it.id === id);
  if (behavior) {
    entity.value.behaviorModelId = behavior.modelId;
    entity.value.eventName = behavior.name;
    await findBehaviorModel(behavior.modelId);
  }
};

const save = () => {
  return entity.value;
};


defineExpose({
  save,
});

onMounted(() => {
  Object.assign(entity.value, node.data);
  timeRange.value = [
    entity.value.schedule.startTime,
    entity.value.schedule.endTime,
  ];
  eventTimeRange.value = [
    entity.value.eventStartTime,
    entity.value.eventEndTime,
  ];
  handleSearchBehavior();
  findBehaviorModel(entity.value.behaviorModelId);
});

</script>

<style lang="less" scoped>
.easyflow-pannel-start {
  .easyradio-group {
    width: 100%;

    .easyradio {
      width: 50%;
    }
  }

  .repeat-div {
    margin-top: 10px;
    justify-content: space-between;
    display: flex;
  }

  .form-date {
    width: 200px;
    margin: 0 10px;
  }

  .repeat-select {
    margin-right: 20px;
    width: 120px;
  }

  .form-file {
    width: 100%;
    height: 90px;
    display: flex;
    cursor: pointer;
    font-size: 12px;
    color: #999999;
    align-items: center;
    justify-content: center;
    border: 1px dashed #dddddd;

    &:hover {
      opacity: 0.8;
      border: 1px dashed #999999;
    }
  }

  .between {
    display: flex;
    justify-content: space-between;
  }
}

.between-option {
  display: flex;
  justify-content: space-between;
}

.usage-type {
  color: lightseagreen;
}

.form-item-select {
  position: relative;

  .estimate-number {
    position: absolute;
    top: 14px;
    right: 0;
    zoom: 0.8;
    padding: 0 5px;
    background-color: #3370ff;
    color: #ffffff;
  }
}
</style>
