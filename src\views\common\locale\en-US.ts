export default {
  "init.systemEnvironmentCheck": "System Environment Check",
  "init.shardedStorageConfiguration": "Distributed Storage Configuration",
  "init.ossStorageConfiguration": "Object Storage Service (OSS) Configuration",
  "init.dataWarehouseConfiguration": "Data Warehouse Configuration",
  "init.scheduledTaskConfiguration": "Cron Job Configuration",
  "init.analysisReportConfiguration": "Analytics Reporting Configuration",
  "init.flowConfiguration": "Workflow Configuration",
  "init.deployment": "Deployment",

  init: {
    button: {
      start: "Start Initialization",
      previous: "Previous Step",
      next: "Next Step",
    },
    storageConfiguration: {
      source: "Data Source",
      subDatabase: "Sub-database",
      connectionString: "Connection String"
    },
    ossConfiguration: {
      type: "OSS Type",
      address: "Service Address",
      accessKey: "Access Key",
      accessSecret: "Access Secret",
      storageBucket: "Storage Bucket",
      baseDir: "Base Directory"
    },
    warehouseConfiguration: {
      type: "Data Warehouse Type",
      dataModel: "Custom Data Model",
      customerModel: "Customer Model"
    },
    reminder: {
      start: "Your current business unit has not been initialized yet. Click the button below to start the initialization.",
      envCheck: "You have completed all the preliminary system initializations.",
      storageConfig: "If you need a sub-database, you can choose to configure independent storage information. The timed task data generated when this product runs will be stored separately for you.",
      mongoConn: "Please enter the MongoDB connection string.",
      esConn: "Please enter the ElasticSearch connection string.",
      warehouseConfig: "Data warehouse configuration.",
      ossType: "Please enter the OSS type.",
      ossAddress: "Please enter the service address.",
      accessKey: "Please enter the access key.",
      accessSecret: "Please enter the access secret.",
      storageBucket: "Please enter the storage bucket.",
      baseDir: "Please enter the base directory.",
      datawarehouseType: "Please select the data warehouse type.",
      dataModel: "Please select the custom data model.",
      customerModel: "Please select the customer model."
    }
  }
};
