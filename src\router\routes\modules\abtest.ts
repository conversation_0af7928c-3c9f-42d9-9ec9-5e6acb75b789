const config = {
  path: "ab",
  name: "ab",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.abtest",
    requiresAuth: true,
    icon: "icon-experiment",
    order: 46,
    parentMenu:true,
  },
  children: [
    {
      path: "abtest",
      name: "AB",
      component: () => import("@/views/ma-abtest/abtest/main.vue"),
      meta: {
        type:'menu',
        locale: "menu.abtest.abtest",
        requiresAuth: true,
        roles: ["ma_menu.abtest"],
      },
    },
    {
      path: "abtest/view",
      name: "AbView",
      component: () => import("@/views/ma-abtest/abtest/view.vue"),
      meta: {
        locale: "menu.abtest.edit",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
    {
      path: "abtest/analyse",
      name: "AbAnalyse",
      component: () => import("@/views/ma-abtest/abtest/analyse.vue"),
      meta: {
        locale: "menu.abtest.analyse",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"],
      },
    },
  ],
};

export default config;
