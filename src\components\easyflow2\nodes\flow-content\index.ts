import { Graph } from "@antv/x6";
import { ref } from "vue";
import { getPorts, rect } from "@/components/easyflow2/components/node";
import FlowContentNode from "./node.vue";
import FlowContentPannel from "./pannel.vue";
import Help from "./help.vue";

let nodeItem: any = {};
const nodeData = {
  type: "flow_content",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<FlowContentNode :nodeItem='nodeItem' />`,
      components: {
        FlowContentNode,
      },
      data() {
        return {
          nodeItem: nodeItem,
        };
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("FlowContentNode", nodeData.node, true);
};

const changeItem = (id: any) => {
  let nodelist: any = JSON.parse(localStorage.getItem("nodeItem") || '') || {};
  nodeItem = nodelist[id];
};

const FlowContent = {
  type: "flow_content",
  name: "flow测试",
  shape: "FlowContentNode",
  iconClass: "icon-event",
  color: "#ffffff",
  themebg: "#39BCC5",
  skippable: false,
  pannel: FlowContentPannel,
  help: Help,
  registerNode,
  nodeData,
  changeItem,
  auth: [
    "export_task_reach_record"
  ]
};

export default FlowContent;
