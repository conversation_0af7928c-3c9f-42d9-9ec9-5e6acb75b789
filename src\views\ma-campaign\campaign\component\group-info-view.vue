<template>
  <div class="campaign-info">
    <h3 class="campaign-name">{{ props.groupInfo.name }}</h3>
    <div class="campaign-extra">
      <div class="campaign-code">{{t('campaign.canvas.code')}}: {{ props.groupInfo.code }}</div>
      <div class="campaign-des">{{t('campaign.canvas.remark')}}: {{ props.groupInfo.summary }}</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, getCurrentInstance } from "vue";

const {
      proxy: { t }
    } = getCurrentInstance();

const props = defineProps({
  groupInfo: {
    type: Object,
    default: () => {}
  }
});
</script>

<style lang="less" scoped>
.campaign-info {
  margin-top: -25px;
  margin-bottom: 30px;
  .campaign-extra {
    margin-top: 10px;
    display: flex;
    .campaign-code {
      margin-right: 45px;
    }
  }
}
</style>
