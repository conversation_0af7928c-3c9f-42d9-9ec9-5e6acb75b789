<template>
  <div style="padding-bottom: 20px">
    <a-row class="snapshot-filter" :gutter="24">
      <a-col :span="10">
        <a-select
          v-model="snapshotId"
          placeholder="请选择快照"
          :loading="loading"
          :filter-option="false"
          @change="querySnapshot"
        >
          <a-option
            v-for="item of snapshots"
            :key="item.value"
            :value="item.value"
            >{{ item.label }}</a-option
          >
        </a-select>
      </a-col>
    </a-row>
    <a-table
      v-if="columnsTable.length > 0"
      ref="table"
      :columns="columnsTable"
      :pagination="false"
      :bordered="false"
      :data="dataSource"
    />
    <a-result v-else status="500" subtitle="客户模型没有可显示字段"></a-result>
    <a-pagination
      size="small"
      :total="pagination.total"
      :current="pagination.page"
      :page-size="pagination.size"
      show-total
      show-page-size
      @change="changePage"
      @page-size-change="changeSizePage"
    />
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import moment from "moment";
import { useRoute } from "vue-router";
import { findSnapshotCustomerPage, getCrowdSnapshot } from "@/api/audience";
import { findCustomerModel } from "@/api/system";

// 路由API
const route = useRoute();

// 分页设置
const pagination = ref({
  page: 1,
  size: 20,
  total: 0,
  showPageSize: true
});
const snapshotId = ref(null);
const snapshots = ref([]);
const loading = ref(false);
// 过滤设置
const filter = ref([
  {
    field: "audienceId",
    label: "快照历史",
    component: "a-select",
    operate: "eq",
    placeholder: "请选择",
    value: ""
  }
]);

// 数据设置
const entity = ref({});

// 列表数据
const dataSource = computed(() => entity.value.content || []);

// 查询API
const bindData = async (id) => {
  entity.value = await findSnapshotCustomerPage(
    {
      ...pagination.value,
      page: pagination.value.page - 1
    },
    id ? id.split(" eq ")[1] : route.query.id
  );

  pagination.value.total = entity.value.totalElements;
};

const querySnapshot = async (id) => {
  entity.value = await findSnapshotCustomerPage(
    {
      ...pagination.value,
      page: 0
    },
    id
  );
  pagination.value.total = entity.value.totalElements;
};

const columnsTable = ref([]);
findCustomerModel().then((data) => {
  if (data) {
    data.fields.forEach((item) => {
      if (item.listDisplay) {
        columnsTable.value.push({
          title: item.aliasName,
          dataIndex: `${item.name}`,
          cellStyle: { "white-space": "nowrap" }
        });
      }
    });

    pagination.value.showPageSize = columnsTable.value.length > 0;
  }
});
// 切换页数
const changePage = async (e) => {
  pagination.value.page = e;
  bindData();
};
// 切换条数
const changeSizePage = async (e) => {
  pagination.value.page = 1;
  pagination.value.size = e;
  bindData();
};
// 获取人群快照历史记录 {expression: `audienceId eq ${route.query.id}`}
getCrowdSnapshot({ expression: `audienceId eq ${route.query.id}` }).then(
  (res) => {
    res.content.forEach((item) => {
      snapshots.value.push({
        value: item.id,
        label: moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")
      });
    });

    if (res.content.length === 0) {
      return false;
    }
    filter.value.value = res.content[0].id;
    bindData(`id eq ${res.content[0].id}`);
  }
);
</script>

<style lang="less" scoped>
.snapshot-filter {
  margin: 10px 0;
}

:deep(.general-card) {
  margin: 0;
}
</style>
