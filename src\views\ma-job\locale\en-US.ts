export default {
    "menu.task": "task",
    "menu.task.list": "Task List",
    task:{
        taskManagement: "Task Management",
        taskType: "Task Type",
        audienceComparisonTask: "Audience Comparison Task",
        audienceDeliveryLimitValidationTask: "Audience Delivery Limit Validation Task",
        activityMonitoringTask: "Campaign Monitoring Task",
        activityAudienceAdditionTask: "Campaign Audience Addition Task",
        exportProcessNodeRecords: "Export Process Node Records",
        exportProcessLinkReachRecords: "Export Process Link Reach Records",
        exportActivityReportTask: "Export Campaign Report Task",
        exportProcessNodeReachRecords: "Export Process Node Reach Records",
        taskStatus: "Task Status",
        submitted: "Submitted",
        pendingRun: "Pending Run",
        successful: "Successful",
        running: "Running",
        failed: "Failed",
        expired: "Expired",
        taskName: "Task Name",
        creationTime: "Creation Time",
        action: "Actions",
        fileList: "File List",
        fileName: "File Name",
        fileSize: "File Size",
        fileCount: "Number of Entries",
        popup:{
            deleteTitle: "Delete Behavior Task",
          },
        reminder: {
            taskType: "Select task type",
            taskStatus: "Select task status",
          }
    },

};
