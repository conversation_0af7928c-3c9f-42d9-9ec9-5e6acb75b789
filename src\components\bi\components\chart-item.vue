/** *by:<EMAIL> on 2022/8/4 0004 */
<template>
  <div ref="chartsRef" class="ChartItem">
    <template v-if="dataItem?.data">
      <Chart
        v-if="dataItem.data.type !== 'Metric' && dataItem.data.type !== 'ChartTitle'"
        :height="height"
        :option="chartOption"
      />
      <div v-else-if="dataItem.data.type === 'Metric'" class="metric-chart">
        <div class="title-name">
          {{ dataItem.data.dataSet.tables[0].metricName }}
        </div>
        <div class="tag-item">
          <i class="iconfont" :class="[dataItem.data.setting.icon]"></i>
          <span>{{ dataItem.data.dataSet.tables[0].rows[0].values[0] }}</span>
        </div>
      </div>
      <div v-else-if="dataItem.data.type === 'ChartTitle'" class="metric-chart">
        <div class="title-name">
          {{ dataItem.data.setting.title.text }}
        </div>
      </div>
    </template>
    <a-empty v-else> 您尚未配置该图表 </a-empty>
  </div>
</template>

<script>
import { defineComponent,ref } from "vue";
import useChartOption from "@/hooks/chart-option";
import { filterOption } from "../option/index";

export default defineComponent({
  name: "ChartItem",
  props: {
    height: {
      type: String,
      default: '62px',
    },
    dataItem: {
      type: [Object, Array],
      default: {},
    },
  },
  setup(props, { emit }) {
    const chartsRef = ref(null)
    const { chartOption } = useChartOption(() => {
      const option = filterOption(
        props.dataItem.data,
        props.dataItem.data.type
      );
      let title = props.dataItem?.data?.setting?.title?.text ||
          props.dataItem?.data?.dataSet?.tables[0]?.metricName
      return {
        title: {
          text: title
        },
        ...option,
      };
    });
    return {
      chartsRef,
      chartOption,
    };
  },
});
</script>
<style scoped lang="less">
.metric-chart {
  .title-name {
    font-size: 18px;
    font-weight: bold;
    color: #464646;
  }
  .tag-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 40px;
    margin-top: 10px;

    .iconfont {
      font-size: 60px;
      color: #5470c6;
    }
  }
}
</style>
