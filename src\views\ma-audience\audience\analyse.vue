/**
*by:<EMAIL> on 2022/8/15 0015
*/
<template>
  <module view>
    <template #main>
      <div class="AudienceAnalyse">
        <div v-for="(item, index) in dataList" :key="item.id" class="chart-item">
          <div class="top-name">
            <div class="left-name">
              <div class="name-number">{{ item.name }}
                <span>{{ item.number }}</span>人
              </div>
              <div class="tags">
                <template v-for="c in item.list" :key="item.name">
                  <a-tag checkable :default-checked="c.is" class="tag" color="red" @click="c.is = !c.is">{{ c.name
                  }}</a-tag>
                </template>
              </div>
            </div>
            <a-button v-if="index === 0" size="large" :disabled="dataList.length > 1" type="dashed"
              @click="curId = ''; addVisible = true">
              <template #icon>
                <icon-plus />
              </template>
              {{t('audience.analysis.craete_compare_audience')}}
            </a-button>
            <a-button v-else type="outline" status="danger" @click="removeItem(item, index)">{{t('global.button.delete')}}</a-button>
          </div>
          <div class="grid-demo">
            <a-row :gutter="10">
              <template v-for="c in item.list" :key="c.i">
                <a-col v-if="c.is" class="grid-col" :lg="24" :xl="dataList.length > 1 ? 24 : 12">
                  <ChartItem class="grid-item" :height="c.h * 32 + 'px'" :data-item="c" />
                </a-col>
              </template>
            </a-row>
          </div>
        </div>

        <!--  添加  -->
        <a-modal v-model:visible="addVisible" @before-ok="addChartItem">
          <template #title>{{t('audience.analysis.craete_compare_audience')}}</template>
          <a-select v-model="curId" :placeholder="t('audience.reminder.selectComparisonAudience')">
            <a-option v-for="item in rqList" :key="item.id" :disabled="item.isDisabled" :value="item.id"
              :label="item.name" />
          </a-select>
        </a-modal>
      </div>
    </template>
  </module>
</template>

<script>
import { ref, reactive, onMounted, provide, getCurrentInstance } from 'vue';
import {
  countAudience,
  findAudienceList
} from '@/api/audience'
import {
  getAnalysisAudience,
  getChangeList,
} from '@/api/analysis'
import { uuid } from '@/utils/uuid'
import { useRoute } from 'vue-router'
import ChartItem from "@/components/bi/components/chart-item.vue";

export default {
  name: 'AudienceAnalyse',
  components: {
    ChartItem
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()

    const route = useRoute()
    const dataList = ref([])
    const rqList = ref([])
    const module = ref({
      entityIdField: "id",
      // entityName: `设计活动流程图`,
      entityName: t('audience.analysis.designCampaignFlowchart'),
      breadcrumb: [
        {
          // name: "活动人群",
          name: t('audience.title'),
          path: "/audience/main",
        },
        {
          // name: "活动人群-分析",
          title: t('audience.analysis.title')
        },
      ],
      mainPath: "/audience/main"
    })

    // 获取显示数据
    const chartType = reactive([
      { name: '性别', value: 'gender', is: true, h: 8, w: 6, x: 0, y: 0, static: false },
      { name: '出生日期', value: 'birthday', is: true, h: 8, w: 6, x: 0, y: 6, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 6, y: 0, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 6, y: 6, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 12, y: 0, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 12, y: 6, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 18, y: 0, static: false },
      { name: '--', value: '--', is: false, h: 8, w: 6, x: 18, y: 6, static: false }
    ])


    const getChartList = (id) => {
      return countAudience(id).then(x => {
        // 获取需要显示的图形
        getChangeList({ expression: "useType eq audience" }).then(gs => {
          const list = []
          gs.forEach((item, index) => {
            chartType[index].name = item.name
            chartType[index].value = item.type
            list.push(getAnalysisAudience(id, 'audience', chartType[index].value))
          })
          Promise.all(list).then(res => {
            const analysisList = {
              number: 0,
              name: '',
              id,
              list: []
            }
            const curIndex = rqList.value.findIndex(y => {
              return y.id === id
            })
            rqList.value[curIndex].isDisabled = true
            analysisList.number = x
            analysisList.name = rqList.value[curIndex].name
            res.forEach((c, index) => {
              // if (!c.dataSet) { return false }
              analysisList.list.push({
                data: c,
                i: uuid(22),
                id: uuid(null),
                ...chartType[index]
              })
            })
            dataList.value.push(analysisList)
          })
        })

      })
    }

    // 添加对比人群
    const addVisible = ref(false)
    const curId = ref('')
    const addChartItem = (done) => {
      Promise.all([getChartList(curId.value)]).then(() => {
        done()
      })
    }

    // 删除人群
    const removeItem = (item, index) => {
      const curIndex = rqList.value.findIndex(y => {
        return y.id === item.id
      })
      rqList.value[curIndex].isDisabled = false
      dataList.value.splice(index, 1)
    }

    onMounted(() => {
      // 获取人群列表
      findAudienceList().then(res => {
        rqList.value = res
      }).then(() => {
        getChartList(route.query.id)
      })
    })

    const setup = {
      module,
      dataList,
      getChartList,
      chartType,
      rqList,
      addVisible,
      addChartItem,
      curId,
      removeItem
    };
    provide("view", setup);
    return setup;
  },
}
</script>

<style lang="less" scoped>
.AudienceAnalyse {
  margin: 16px 20px;
  background-color: #ffffff;
  display: flex;

  .grid-demo {
    .grid-col {
      margin-top: 10px;
    }

    .grid-item {
      text-align: center;
      background-color: var(--color-bg-1);
      padding: 16px 16px;
      border: 1px solid var(--color-neutral-3);
      border-radius: var(--border-radius-small);
    }
  }

  .vue-grid-item:not(.vue-grid-placeholder) {
    background-color: var(--color-bg-1);
    border: 1px solid #999999;
    padding: 16px 16px;
    border: 1px solid var(--color-neutral-3);
    border-radius: var(--border-radius-small);
  }

  .chart-item {
    border-right: 1px solid var(--color-neutral-3);
    padding-right: 10px;
    margin-right: 10px;
    margin-bottom: 10px;
    flex: 1;

    &:last-child {
      border: 0;
      padding-right: 0;
      margin-right: 0;
    }
  }

  .card-item {
    margin-bottom: 16px;
  }

  .filter-form {
    margin-top: 20px;
  }

  &.screen-full {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: var(--color-fill-2);
  }

  .top-name {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .name-number {
      font-size: 16px;
      margin-bottom: 10px;

      span {
        font-size: 24px;
        margin-left: 20px;
      }
    }

    .right-btn {
      padding: 16px 25px;
      border: 1px dashed #999999;
      cursor: pointer;
    }
  }

  .tags {
    .tag {
      margin-right: 5px;
    }
  }
}
</style>
