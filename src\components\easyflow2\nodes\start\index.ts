import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import StartNode from "./node.vue";
import StartPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "start",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<StartNode />`,
      components: {
        StartNode,
      },
    },
    ports: getPorts(false, false, true, false),
  },
};
const registerNode = () => {
  Graph.registerNode("StartNode", nodeData.node, true);
};

const Start = {
  type: "start",
  name: "开始",
  shape: "StartNode",
  iconClass: "icon-start",
  color: "#ffffff",
  themebg: "#39BCC5",
  fixed: true,
  skippable: false,
  pannel: StartPannel,
  help: Help,
  registerNode,
};

export default Start;
