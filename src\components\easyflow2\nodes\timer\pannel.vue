<template>
  <div class="easyflow-pannel-timer">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="定时器类型">
        <a-radio-group v-model="entity.schedule.type" type="button">
          <a-radio border value="DATE">单次</a-radio>
          <a-radio border value="CRON">多次</a-radio>
          <a-radio border value="DELAY">延时</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="entity.schedule.type == 'DATE'" label="单次定时器配置">
        <a-date-picker v-model="entity.schedule.date" :show-time="true" value-format="YYYY-MM-DDTHH:mm:ss.000ZZ"
          class="form-date" />
      </a-form-item>

      <a-form-item v-if="entity.schedule.type == 'CRON'" label="多次定时器配置" :content-flex="false">
        <a-range-picker v-model="timeRange" show-time :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
          format="YYYY-MM-DD" value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" @change="handleRangeTime" />
        <a-input v-model="entity.schedule.cron" class="cron-input" placeholder="请输入CRON表达式">
          <template #prepend> CRON </template>
        </a-input>
      </a-form-item>

      <a-form-item v-if="entity.schedule.type == 'DELAY'" label="延时定时器配置" :content-flex="false">
        <a-input-number v-model="delay.day" class="delay-input" placeholder="请输入延时天数" :min="0">
          <template #append> 天 </template>
        </a-input-number>
        <a-input-number v-model="delay.hour" class="delay-input" placeholder="请输入延时小时数" :min="0" :max="24">
          <template #append> 小时 </template>
        </a-input-number>
        <a-input-number v-model="delay.minute" class="delay-input" placeholder="请输入延时分钟数" :min="0" :max="59">
          <template #append> 分钟 </template>
        </a-input-number>
        <a-input-number v-model="delay.second" class="delay-input" placeholder="请输入延时秒数" :min="0" :max="59">
          <template #append> 秒 </template>
        </a-input-number>
      </a-form-item>

      <!-- 事件相关配置 -->
      <!-- <a-form-item label="事件触发">
        <a-switch v-model="entity.eventEnabled" type="round" />
      </a-form-item> -->

      <div v-if="entity.eventEnabled">
        <a-form-item key="behaviorId" label="选择事件" :rules="[{ match: /one/, message: 'must select one' }]">
          <a-select v-model="entity.behaviorId" placeholder="请选择事件" allow-search :loading="loading" :filter-option="false"
            :show-extra-options="false" @search="handleSearchBehavior" @change="handelChangeBehavior">
            <a-option v-for="item of behaviors" :key="item.id" :value="item.id">{{
              item.name
            }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="事件时间范围" :rules="[{ required: true, message: '不能为空' }]">
          <a-range-picker v-model="eventTimeRange" show-time
            :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }" format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" @ok="handleEventRangeTime" />
        </a-form-item>
        <a-form-item label="事件路径" :content-flex="false">
          <template v-for="(item, index) in entity.branches" :key="index">
            <a-row class="branch-item" :gutter="24">
              <a-col :span="14" class="item-label">
                {{ item.name }}
              </a-col>
              <a-col :span="10">
                <a-radio-group v-model="item.type" type="button">
                  <a-radio border value="EVENT">事件</a-radio>
                  <a-radio border value="TIMEOUT">定时</a-radio>
                </a-radio-group>
              </a-col>
            </a-row>
          </template>
        </a-form-item>
      </div>

      <a-form-item label="去重">
        <a-space>
          <a-switch v-model="entity.unique" type="round" />
          <a-tree-select v-if="entity.unique" v-model="entity.uniqueFields" :multiple="true" :allow-search="true" :data="flowModel"
            allow-clear :field-names="treeFieldStruct" placeholder="请选择去重字段" />
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { findBehaviorEventList } from "@/api/behavior";
import { getFlowModelsByIds } from "@/api/campaign";
import { formatFields } from "@/utils/field"
import { treeFieldStruct } from "@/constant/common"

const { node, connections, easyflow } = defineProps(["node", "connections", "easyflow"]);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;
const entity = ref({
  branches: [],
  eventEnabled: false,
  schedule: {
    type: "DATE",
  },
});
const delay = ref({
  day: 0,
  hour: 0,
  minute: 0,
  second: 0,
});
const timeRange = ref([]);
const eventTimeRange = ref(null);
const behaviors = ref(null); // 行为模型列表
const flowModel = ref(null);


const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = formatFields(modelFields.fields, "");
};

const handleSearchBehavior = async (value) => {
  const params = { fields: "name,modelId" };
  if (value) {
    params.expression = `name like ${value}`;
  }
  behaviors.value = await findBehaviorEventList(params);
};

const handelChangeBehavior = (id) => {
  const behavior = behaviors.value.find((it) => it.id === id);
  if (behavior) {
    entity.value.behaviorModelId = behavior.modelId;
  }
};


const handleRangeTime = () => {
  entity.value.schedule.startTime = timeRange.value[0];
  entity.value.schedule.endTime = timeRange.value[1];
};

const handleEventRangeTime = () => {
  entity.value.eventStartTime = eventTimeRange.value[0];
  entity.value.eventEndTime = eventTimeRange.value[1];
};

const refreshBranchs = () => {
  // 去除无效分支
  if (connections.outgoings) {
    entity.value.branches = connections.outgoings.map((it) => {
      const found = entity.value.branches.find((item, index) => {
        return item.outgoing === it.id;
      });
      if (found) {
        found.name = it.name;
        return found;
      }
      return {
        name: it.name,
        outgoing: it.id,
        type: "EVENT",
      };

    });
  } else {
    entity.value.branches = [];
  }
};
const getDuration = () => {
  let duration = "P";
  if (delay.value.day > 0) {
    duration += `${delay.value.day}D`;
  }
  let time = "";
  if (delay.value.hour > 0) {
    time += `${delay.value.hour}H`;
  }
  if (delay.value.minute > 0) {
    time += `${delay.value.minute}M`;
  }
  if (delay.value.second > 0) {
    time += `${delay.value.second}S`;
  }
  if (time !== "") {
    duration += `T${time}`;
  }
  return duration;
};

const getDelay = (duration) => {
  let tmp = duration.replaceAll(/(P|T)/g, "");
  let index;
  if ((index = tmp.indexOf("D")) > 0) {
    delay.value.day = parseInt(tmp.substr(0, index));
    tmp = tmp.substr(index);
  }
  if ((index = tmp.indexOf("H")) > 0) {
    delay.value.hour = parseInt(tmp.substr(0, index));
    tmp = tmp.substr(index);
  }
  if ((index = tmp.indexOf("M")) > 0) {
    delay.value.minute = parseInt(tmp.substr(0, index));
    tmp = tmp.substr(index);
  }
  if ((index = tmp.indexOf("S")) > 0) {
    delay.value.second = parseInt(tmp.substr(0, index));
  }
};

const save = () => {
  if (entity.value.schedule.type === "DELAY") {
    entity.value.schedule.duration = getDuration();
  }
  return entity.value;
};

defineExpose({
  save,
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  if (entity.value.schedule.type === "DELAY") {
    getDelay(entity.value.schedule.duration);
  }
  if (entity.value.schedule.type === "CRON") {
    timeRange.value = [
      entity.value.schedule.startTime,
      entity.value.schedule.endTime,
    ];
  }
  handleSearchBehavior();
  refreshBranchs();
  handelFlowModel();
});
</script>

<style lang="less" scoped>
.easyflow-pannel-timer {
  .delay-input {
    margin: 5px 0;
    width: 60%;
  }
}
</style>
