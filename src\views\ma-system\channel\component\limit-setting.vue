<template>
  <div>
    <h3 class="form-title">{{ t('systemSetting.communicateChannel.limitSetting.gouTongXianZhiPeiZhi') }}</h3>
    <a-form ref="formRef" layout="vertical" class="general-form" :model="entity">
      <a-card class="general-card">
        <a-row :gutter="20">
          <a-col :span="4">
            <a-form-item :label="t('systemSetting.communicateChannel.limitSetting.wuRaoKaiGuan')">
              <a-switch v-model="entity.silenceSetting.enabled" type="round">
                <template #checked>{{ t('systemSetting.communicateChannel.limitSetting.kai') }}</template>
                <template #unchecked>{{ t('systemSetting.communicateChannel.limitSetting.guan') }}</template>
              </a-switch>
            </a-form-item>
          </a-col>
          <a-col v-if="entity.silenceSetting.enabled" :span="8">
            <a-form-item :label="t('systemSetting.communicateChannel.limitSetting.wuRaoChuLiLeiXing')"
              :rules="[{ required: true, message: t('systemSetting.communicateChannel.limitSetting.QXZWRCLLX') }]"
              field="silenceSetting.silenceType">
              <a-select v-model="entity.silenceSetting.silenceType"
                :placeholder="t('systemSetting.communicateChannel.limitSetting.QXZWRCLLX')">
                <a-option v-for="item of silenceTypes" :key="item.id" :label="item.label" :value="item.id" />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-if="entity.silenceSetting.enabled" :span="6">
            <a-form-item :label="t('systemSetting.communicateChannel.limitSetting.kaiShiGouTongShiJian')">
              <a-time-picker v-model="entity.silenceSetting.startTime" disable-confirm default-value="00:00:00"
                @select="selectTime" />
            </a-form-item>
          </a-col>
          <a-col v-if="entity.silenceSetting.enabled" :span="6">
            <a-form-item field="endTime"
              :label="t('systemSetting.communicateChannel.limitSetting.jieShuGouTongShiJian')">
              <a-time-picker v-model="entity.silenceSetting.endTime" disable-confirm default-value="23:59:59"
                @select="selectTime" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
      <a-card class="general-card">
        <span class="form-title">{{ t('systemSetting.communicateChannel.limitSetting.gouTongPinCiXianZhi') }}</span>
        <a-button type="outline" @click="addFrequency">{{ t('systemSetting.communicateChannel.limitSetting.tianJia')
          }}</a-button>
        <template v-for="(item, index) in entity.limitSetting.frequency" :key="index">
          <div class="f-item">
            <a-input-number v-model="item.days" class="f-input"
              :placeholder="t('systemSetting.communicateChannel.limitSetting.QSRZQTS')" />
            <span class="f-unit">{{ $t('systemSetting.communicateChannel.limitSetting.tian') }}</span>
            <a-input-number v-model="item.times" class="f-input"
              :placeholder="t('systemSetting.communicateChannel.limitSetting.QSRZQCS')" />
            <span class="f-unit">{{ $t('systemSetting.communicateChannel.limitSetting.ci') }}</span>
            <a-button type="outline" @click="delFrequency(index)">{{
              t('systemSetting.communicateChannel.limitSetting.shanChu') }}</a-button>
          </div>
        </template>
      </a-card>
    </a-form>
  </div>
</template>

<script setup>
import { ref, watch, getCurrentInstance } from "vue";

const emit = defineEmits(["update:entity"]);
const { setting } = defineProps(["setting"])
const entity = ref(setting);
if (!entity.value.silenceSetting) {
  entity.value.silenceSetting = {
    enabled: false
  }
}
const {
  proxy: { t }
} = getCurrentInstance()
const silenceTypes = ref([
  { id: "DELAY", label: t('systemSetting.communicateChannel.limitSetting.yanShiFaSong') },
  { id: "SKIP", label: t('systemSetting.communicateChannel.limitSetting.buFaSong') },
]);
const selectTime = (valueString, value) => {
};

const addFrequency = () => {
  if (!entity.value.limitSetting.frequency) {
    entity.value.limitSetting.frequency = [];
  }
  entity.value.limitSetting.frequency.push({});
};

const delFrequency = (index) => {
  entity.value.limitSetting.frequency.splice(index, 1);
};

watch(
  entity,
  (newData, oldData) => {
    emit("update:entity", newData);
  },
  { immediate: true, deep: true }
);
</script>

<style lang="less" scoped>
.form-title {
  margin: 0 20px;
}

.f-item {
  display: flex;
  width: 100%;
  margin-top: 10px;

  .f-input {
    margin-right: 5px;
    width: 150px;
  }

  .f-unit {
    padding: 6px;
  }
}
</style>
