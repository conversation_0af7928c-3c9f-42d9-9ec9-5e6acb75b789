<template>
  <div class="flow-node-right">
    <!-- <flow-delete @mouseup="onMouseup" :deleteNode="deleteNode"></flow-delete> -->
  </div>
</template>

<script>
// delete 关键字无法直接使用，采用flowDeletef
import flowDelete from "./delete.vue";
export default {
  inject: {
    node: {
      default: null,
    },
    flow: {
      default: null,
    }
  },
  components: {
    "flow-delete": flowDelete,
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    deleteNode(){
      this.node.removeNode();
    },
    handleDeleteClick(e) {
      e.stopPropagation();
      this.node.removeNode();
    },
    onMouseup(e){
      e.stopPropagation()
    }
  },
};
</script>

<style lang="less" scoped>
.flow-node-right {
  top: 0px;
  width: 20px;
  right: -20px;
  height: 100%;
  display: none;
  padding-top: 6px;
  position: absolute;
  align-items: center;
  box-sizing: border-box;
  flex-direction: column;
}
</style>
