<template>
  <module edit>
    <template #action></template>
    <template #main>
      <a-row class="grid-demo" :gutter="20">
        <a-col :span="8">
          <a-form ref="formRef" layout="vertical" :model="entity" disabled>
            <a-space direction="vertical" :size="16">
              <a-card class="general-card">
                <template #title> {{ module.entityName }}</template>
                <a-row :gutter="80">
                  <a-col :span="24">
                    <a-form-item :label="t('systemSetting.behaviorModel.modelName')" :rules="[
                      { required: true, message: t('systemSetting.behaviorModel.notAllowNull')},
                      {
                        match: /^[a-z0-9_]*$/,
                        message: t('systemSetting.behaviorModel.modelNameLimit')
                      }
                    ]" :validate-trigger="['input']" field="name">
                      <a-input v-model="entity.name" :placeholder="t('systemSetting.behaviorModel.enterModelName')">
                      </a-input>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="80">
                  <a-col :span="24">
                    <a-form-item :label="t('systemSetting.behaviorModel.modelAliasName')" field="entity.aliasName">
                      <a-input v-model="entity.aliasName" :placeholder="t('systemSetting.behaviorModel.enterMmodelAliasName')">
                      </a-input>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="80">
                  <a-col :span="24">
                    <a-form-item :label="t('systemSetting.behaviorModel.behaviorIdentifyField')" field="options.behaviorIdentifyField" :rules="[
                      { required: true, message: t('systemSetting.behaviorModel.behaviorIdentifyField') }
                    ]">
                      <a-tree-select v-model="entity.options.behaviorIdentifyField" :data="entity.fields" :field-names="{
                        key: 'path',
                        title: 'aliasName',
                        children: 'fields'
                      }"></a-tree-select>
                      <icon-arrow-right size="32" />
                      <a-tree-select v-model="entity.options.customerIdentifyField" :data="custoemrModelIdentifyFields"
                        :field-names="{
                          key: 'path',
                          title: 'aliasName',
                          children: 'fields'
                        }"></a-tree-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="80">
                  <a-col :span="24">
                    <a-form-item :label="t('systemSetting.behaviorModel.primaryKey')" field="pkName" :rules="[{ required: true, message: '请选择主键' }]">
                      <a-tree-select v-model="entity.pkName" :data="entity.fields" style="margin-right: 10px"
                        :field-names="{
                          key: 'path',
                          title: 'aliasName',
                          children: 'fields'
                        }"></a-tree-select>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-card>
            </a-space>
          </a-form>
        </a-col>
        <a-col :span="16">
          <a-card class="general-card" style="height: calc(100vh - 160px)" :title="t('systemSetting.behaviorModel.behaviorModelSetting')" :bordered="false">
            <BehaviorModelConfig ref="behaviorModelRef" v-model:data-model="entity" :is-behavior="true" />
          </a-card>
        </a-col>
      </a-row>
    </template>
  </module>
</template>

<script>
import { ref, provide,getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  findCustomerModel,
  getBehaviorModel,
  saveBehaviorModel
} from "@/api/system";
import { getFieldByPath, formatFields } from "@/utils/field";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const route = useRoute();
    const router = useRouter();
    const queryValue = route.query.id;
    const isEdit = !!queryValue;

    const module = ref({
      entityIdField: "id",
      mainPath: "/system/behavior-model",
      disabledSave: false,
      breadcrumb: [
        {
          name: "系统设置",
          path: "/system/setting"
        },
        {
          name: "行为模型",
          path: "/system/behavior-model"
        },
        {
          name: isEdit ? "查看行为模型" : "编辑行为模型"
        }
      ]
    });

    const entity = ref({
      aliasName: "",
      fields: [],
      extend: [],
      options: {},
      dwhType: "lianwei_cdp",
      own: true
    });

    const customerModel = ref({
      fields: []
    });

    const custoemrModelIdentifyFields = ref([]);

    const formRef = ref({});
    const behaviorModelRef = ref(null);

    const bindData = async () => {
      // #region 初始化数据
      if (queryValue) {
        entity.value = await getBehaviorModel(queryValue);
        entity.value.fields = formatFields(entity.value.fields);
      }

      customerModel.value = await findCustomerModel();
      custoemrModelIdentifyFields.value =
        customerModel.value.identifyFields.map((it) => {
          return {
            path: it,
            aliasName: getFieldByPath(customerModel.value.fields, it).aliasName
          };
        });
    };

    // #endregion
    const quit = () => {
      router.push({ path: module.value.mainPath });
    };

    // const save = async () => {
    //   const vForm = await formRef.value.validate();
    //   const vBModel = await behaviorModelRef.value.validate();
    //   if (vForm) {
    //     console.log(`vForm${vForm}`);
    //     return false;
    //   }
    //   if (vBModel) {
    //     console.log(`vBModel${vBModel}`);
    //     return false;
    //   }
    //   await saveBehaviorModel(entity.value);
    //   quit();
    // };

    const setup = {
      t,
      bindData,
      // save,
      route,
      router,
      module,
      entity,
      formRef,
      behaviorModelRef,
      custoemrModelIdentifyFields
    };
    provide("edit", setup);
    return setup;
  }
};
</script>
