<template>
  <div class="easyflow-pannel-extend-data">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="数据来源">
        <a-select v-model="entity.modelId" placeholder="请选择行为模型" allow-search :loading="loading" :filter-option="false"
          :show-extra-options="false" @search="handleSearchBehaviorModel" @change="handelChangeBehaviorModel">
          <a-option v-for="item of behaviorModels" :key="item.id" :value="item.id">{{ `${item.aliasName} [${item.name}]` }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="关联方式">
        <a-select v-model="entity.joinType" placeholder="请选择关联方式">
          <a-option v-for="item of joinTypes" :key="item.value" :value="item.value">{{ item.text }}</a-option>
          <!-- <a-option value="QUERY">查询条件关联</a-option> -->
        </a-select>
      </a-form-item>
      <a-form-item v-if="entity.joinType == 'QUERY'" label="查询语句">
        <a-textarea v-model="entity.queryContent" placeholder="请输入查询语句" />
      </a-form-item>
      <template v-for="(item, index) in templateData" :key="index">
        {{ item.source }}:
        <a-tree-select v-model="item.fieldName" :allow-search="true" :data="flowModel" allow-clear
          :field-names="treeFieldStruct" placeholder="请选择字段" />
      </template>
    </a-form>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, watch } from 'vue'
import { treeFieldStruct } from "@/constant/common"
import { getFlowModelsByIds } from "@/api/campaign";
import { findBehaviorModelList } from "@/api/behavior";
import { joinTypes } from "@/constant/flow";
import { formatFields } from "@/utils/field"
import { filters } from "@/utils/filter";

const props = defineProps(["node", "easyflow"]);
const { node, easyflow } = props;
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const loading = ref(false);
const entity = ref({
  queryContent: ""
});
const templateData = ref([]);
const flowModel = ref(null);
const behaviorModels = ref([]);

const handelFlowModel = async () => {
  const nodeChain = easyflow.getNodeChain(node.id);
  const models = easyflow.getModels(nodeChain);
  const modelFields = await getFlowModelsByIds(models);
  flowModel.value = formatFields(modelFields.fields, "");
};


const save = () => {
  return entity.value;
};

const findIn = (source) => {
  for (let i = 0; i < templateData.value.length; i++) {
    const element = templateData.value[i];
    if (source === element.source) {
      return element;
    }
  }
}

const handleSearchBehaviorModel = async (name) => {
  const params = { fields: "name,aliasName" };
  // params.expression = `status in ENABLED`
  if (name) {
    params.expression = `name like ${name}`;
  }
  behaviorModels.value = await findBehaviorModelList(params);
};

const handelChangeBehaviorModel = () => {
  entity.value.modelAliasName = filters(behaviorModels.value, entity.value.modelId, "aliasName", "id");
};

watch(() => entity.value.queryContent, (newVal) => {
  const newList = [];
  if (newVal) {
    newVal.replace(/\${.*?}/g, (str) => {
      const key = str.match(/\${(\S*)}/)[1];
      const item = findIn(key);
      if (item) {
        newList.push(item);
      } else {
        newList.push({ source: key });
      }
    });
    templateData.value.splice(0, templateData.value.length, ...newList);
  }
}, { immediate: true })

defineExpose({
  save,
});

onMounted(() => {
  Object.assign(entity.value, node.data);
  handelFlowModel();
  handleSearchBehaviorModel();
});
</script>


<style lang="less" scoped></style>
