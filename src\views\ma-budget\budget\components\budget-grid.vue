<template>
  <a-card class="general-card" :header-style="{ paddingBottom: '0' }" :body-style="{ paddingTop: '26px' }">
    <a-row v-if="filter.length > 0" class="filter-row">
      <a-col :flex="1">
        <slot name="filter">
          <a-form class="filter-form" :model="formModel" auto-label-width label-align="right">
            <a-row :gutter="16">
              <template :key="filterIndex" v-for="(filterItem, filterIndex) in filter">
                <a-col v-if="filterIndex < openFilterNumber" :span="8">
                  <a-form-item :field="filterItem.field" :label="filterItem.label">
                    <component :is="filterItem.component" v-model="formModel[filterItem.field]"
                      :options="filterItem.dataSource" :allow-clear="filterItem.allowClear"
                      :placeholder="filterItem.placeholder"></component>
                  </a-form-item>
                </a-col>
              </template>
            </a-row>
          </a-form>
        </slot>
      </a-col>
      <a-col :flex="'86px'" style="text-align: right; padding-left: 20px">
        <a-space :size="18">
          <a-button type="primary" @click="search">
            <template #icon>
              <icon-search />
            </template>
            查询
          </a-button>
          <a-button @click="reset">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
          <a-button v-if="filter.length >= 3" @click="onOpenFilter" type="text">
            {{ openFilterNumber === 3 ? "展开" : "收起" }}
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="main.create">
            <template #icon>
              <icon-plus />
            </template>
            新建
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <a-table ref="table" :bordered="false" :data="dataSource" :pagination="false">
      <template #columns>
        <a-table-column title="预算名称" data-index="name" />
        <a-table-column title="计划预算" data-index="name2" />
        <a-table-column title="已使用" data-index="name2" />
        <a-table-column title="已确认" data-index="name2" />
        <a-table-column title="结余" data-index="name2" />
        <a-table-column title="开始时间" data-index="startTime">
          <template #cell="{ record }">
            {{ $moment(record.startTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </a-table-column>
        <a-table-column title="结束时间" data-index="endTime">
          <template #cell="{ record }">
            {{ $moment(record.endTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </a-table-column>
        <a-table-column title="操作" align="center">
          <template #cell="{ record }">
            <a-button @click="detail(record)" type="text" size="small">收起</a-button>
            <a-button @click="deleteData(record.id)" type="text" size="small">删除</a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-card>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { findPage, deleteItem } from "@/api/budget";
import { useRouter } from "vue-router";
import { Modal } from "@arco-design/web-vue";
const main = inject("main");
const router = useRouter();
const module = ref({
  entityIdField: "id",
  entityName: "预算管理",
  editPath: "/budget/budget/edit",
  viewPath: "/budget/budget/view",
  createPath: "/budget/budget/edit",
});
const dataSource = ref([]);
const filter = ref([
  {
    field: "name",
    label: "名称",
    component: "a-input",
    operate: "like",
    placeholder: "请输入预算名称",
    comment: true,
    value: "",
  },
  {
    field: "status",
    label: "状态",
    component: "a-input",
    operate: "eq",
    placeholder: "请输入预算状态",
    value: "",
  },
]);
const openFilterNumber = ref(3);
const onOpenFilter = () => {
  openFilterNumber.value = openFilterNumber.value === 3 ? filter.length : 3;
};
const formModel = ref({});
const pagination = ref({
  page: '1',
  size: '10',
  total: 0,
  showPageSize: true
})

const bindData = async (expression) => {
  const pageData = await findPage({
    ...pagination.value,
    page: pagination.value.page - 1
  }, { expression: expression, fields: "name,campaignId,estimatedAmount,realAmount,startTime,endTime,status,summary" })
  dataSource.value = pageData.content
  pagination.value.total = pageData.totalElements
}

const search = async () => {
  const expression = [];

  filter.value.forEach((f) => {
    if (formModel.value[f.field]) {
      expression.push(
        `${f.field} ${f.operate} ${formModel.value[f.field]}`
      );
    }
  });
  if (expression.length > 1) {
    return await bindData(expression.join(" AND "));
  } else if (expression.length == 1) {
    return await bindData(expression.toString());
  } else {
    return await bindData();
  }
}
const reset = () => {
  formModel.value = generateFormModel()
  bindData()
}
const generateFormModel = () => {
  const localFilter = {};
  filter?.value?.forEach((f) => (localFilter[f.field] = f.value));
  return localFilter;
};
const campaignFilter = (data) => { return data };

const detail = (data) => {
  const query = {};
  query[module.value.entityIdField] = data.id;
  router.push({ path: module.value.editPath, query: query });
}
const deleteData = async (id) => {
  Modal.confirm({
    title: "删除模板",
    content: "删除之后数据不可恢复，请确认是否删除?",
    onOk: async () => {
      await deleteItem(id)
      if (dataSource.value.length === 1 &&
        pagination.value.page > 1) {
        pagination.value.page--
      }
      await bindData()
    },
  });
};
defineExpose({});
onMounted(async () => {
  await bindData();
});
</script>
<style lang="less" scoped>
.filter-row {
  border-bottom: 1px solid var(--color-neutral-3);
  margin-bottom: 15px;
}
</style>
