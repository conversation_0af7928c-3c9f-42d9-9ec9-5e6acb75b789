<template>
  <module edit>
    <template #action>
      <a-button type="outline" @click="showSaveTemplateDlg">{{ t('global.button.saveTemplate') }}</a-button>
      <!-- <a-button type="outline" @click="showDebugHistoryDlg">模拟历史</a-button> -->
      <a-button type="primary" @click="showBasicConfig">{{ t('global.button.baseInfo') }}</a-button>
    </template>
    <template #main>
      <div v-if="entity != null" class="flow-edit">
        <easyflow ref="flowRef" :entity="entity" :flow-id="entity.id" :flow_data="entity.setting.flowData"
          :simulate="simulate" :simulate-event="simulateEvent" :monitor="monitor" :validate="validate"
          :status="entity.status" :started="entity.started" :on-change="onChange" :edit-enable="editEnable"
           />
      </div>
      <SimulateDlg ref="simulateDlgRef" />
      <SimulateEventDlg ref="simulateEventDlgRef" />
      <SelectTimeRangeDlg ref="selectTimeRangeDlgRef" />
      <SimulateDataViewDlg ref="simulateDataViewDlgRef" />
      <task-edit-drawer ref="taskDrawerRef" :save-task="modifyBasic" :editable="true" />
      <save-template-dlg ref="saveTemplateRef" />
    </template>
  </module>
</template>

<script>
import { useBussinessUnitStore } from "@/store";
import EasyFlow from "@/components/easyflow2";
import SelectTimeRangeDlg from "@/components/modal-dlg/select-time-range-dlg.vue";

import { useRoute, useRouter, onBeforeRouteLeave } from "vue-router";
import { h, ref, provide, onMounted, onUnmounted, getCurrentInstance } from "vue";
import { Modal, Notification } from "@arco-design/web-vue";

import EventBus from "@/utils/eventbus";
import { filters } from "@/utils/filter";

import { campaignStatus,simulateEnabled } from "@/constant/campaign";
import {
  getCampaign,
  modifyCampaign,
  flowValidate,
  flowStopDebug,
  getDebugRecord,
  flowStepOver,
  queryFlowInstance,
} from "@/api/campaign";
import { monitorCampaign } from "@/api/monitor"
import SimulateDataViewDlg from "./component/simulate-data-view-dlg.vue";
import SimulateEventDlg from "./component/simulate-event-dlg.vue";
import SimulateDlg from "./component/simulate-dlg.vue";
import TaskEditDrawer from "./component/task-edit-drawer.vue";
import SaveTemplateDlg from "./component/save-template-dlg.vue";

export default {
  components: {
    easyflow: EasyFlow,
    SimulateDlg,
    SimulateEventDlg,
    SimulateDataViewDlg,
    SelectTimeRangeDlg,
    TaskEditDrawer,
    SaveTemplateDlg
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const { id, groupId } = route.query;

    const owner = useBussinessUnitStore().marketingCenter;

    const module = ref({
      entityIdField: "id",
      entityName: "",
      breadcrumb: [
        {
          // name: "营销活动",
          name: t('campaign.title'),
          path: "/campaign/group",
        },
        {
          // name: "活动流程",
          name: t('campaign.flow.title'),
          path: `/campaign/campaign`,
          query: { id: groupId }
        },
        {
          // name: "流程活动",
          name: t('campaign.flow.campaign'),
          path: `/campaign/campaign/edit-flow`,
          query: { id, groupId }
        }
      ],
      mainPath: `/campaign/campaign?id=${groupId}`,
      mainQuery: { id: groupId },
      viewPath: "/campaign/campaign/view-flow",
      debugInstanceId: ""
    });

    // REF
    const flowRef = ref(null);
    const simulateDlgRef = ref(null);
    const simulateEventDlgRef = ref(null);
    const selectTimeRangeDlgRef = ref(null);
    const simulateDataViewDlgRef = ref(null);
    const taskDrawerRef = ref(null);
    const saveTemplateRef = ref();

    const entity = ref(null); // Campaign实体对象
    const changed = ref(false); // 流程是否变更
    const editEnable = ref(false); // 流程是否可编辑

    /**
     * 显示基本信息窗口
     */
    const showBasicConfig = () => {
      taskDrawerRef.value.editTask(entity.value);
    }
    const showSaveTemplateDlg = () => {
      saveTemplateRef.value.show(entity.value.setting.flowData);
    }
    const showDebugHistoryDlg = () => {
      // TODO 显示模拟历史
    }
    const modifyBasic = (task) => {
      entity.value = task;
    }

    /**
     * 检查流程基本信息
     */
    const checkBasicInfo = async () => {
      const endTime = new Date(entity.value.endTime);
      if (endTime < new Date()) {
        const res = await selectTimeRangeDlgRef.value.show(
          "活动已过期，请重新选择活动时间",
          [entity.value.startTime, entity.value.endTime]
        );
        if (res) {
          entity.value.startTime = res[0];
          entity.value.endTime = res[1];
        }
      }
    };

    const save = async () => {
      if (entity.value.status === "RUNNING") {
        Notification.warning({
          title: "保存营销活动",
          content: "营销活动正在运行，请先暂停活动！",
        });
        return;
      }
      if (entity.value.status === "FINISHED" || entity.value.status === "STOP") {
        Notification.warning({
          title: "保存营销活动",
          content: "营销活动已经结束，不能保存变更！",
        });
        return;
      }
      // 检查活动状态
      const runningInstances = await queryFlowInstance({expression: `flowId eq ${id} AND status eq RUNNING`});
      if (runningInstances.length > 0){
        Notification.warning({
          title: "保存营销活动",
          content: "营销活动正在执行，不能保存变更！",
        });
        return;
      }
      await checkBasicInfo();

      flowRef.value.save();
      entity.value.setting.flowData = JSON.stringify(flowRef.value.getData());
      await modifyCampaign(entity.value);
      Notification.info({
        title: "保存营销活动",
        content: "营销活动保存成功。",
      });
      changed.value = false;
    };

    // 加载流程数据
    const bindData = async () => {
      entity.value = await getCampaign(id);
      editEnable.value = owner?.setting?.customizationSetting?.campaignEditableStatus.indexOf(entity.value.status) !== -1;

      if (!editEnable.value) {
        router.push({ path: module.value.viewPath, query: { id } });
      }
      // 调整流程显示内容
      module.value.entityName = `${entity.value.name} - [${filters(
        campaignStatus,
        entity.value.status
      )}]`;
    };

    // 校验流程
    const validate = async (succSilence) => {
      flowRef.value.save();
      const messages = await flowValidate(id, flowRef.value.getData());
      if (messages.length <= 0) {
        if (!succSilence) {
          Notification.info({
            title: "校验成功",
          });
        }
        return true;
      }
      const messageItems = [];
      for (let i = 0; i < messages.length; i += 1) {
        messageItems.push(
          h("ListItem", { style: "display: list-item; margin: 10px 0" }, [
            messages[i],
          ])
        );
      }
      const contentList = h("List", { class: "notify-list" }, messageItems);
      Notification.warning({
        title: "校验失败",
        content: () => contentList,
        closable: true,
        duration: 100000,
      });
      return false;
    };

    // 模拟流程
    const simulate = async () => {

      // 查询当前模拟状态
      const debugInstances = await queryFlowInstance({expression: `flowId eq ${id} AND engineType eq DEBUG AND status eq RUNNING`});
      if(debugInstances.length > 0){
        module.value.debugInstanceId = debugInstances[0].id
      } else {
        // 检查保存
        if (changed.value) {
          Notification.warning({
            title: "活动变更未保存，模拟前请先保存流程",
          });
          return;
        }

        const audienceNodes = flowRef.value.getNode("audience_receive");
        module.value.debugInstanceId = await simulateDlgRef.value.show(id, entity.value.type, audienceNodes);
      }
      // 校验流程
      // if (!(await validate(true))) {
      //   return;
      // }
      if (module.value.debugInstanceId) {
        flowRef.value.toogleSimulate(true);
      }
      return module.value.debugInstanceId;
    };

    const simulateEvent = async () => {
      const behaviorNodes = flowRef.value.getNode("event_receive");
      simulateEventDlgRef.value.show({
        flowId: id,
        instanceId: module.value.debugInstanceId,
        behaviorNodes
      });
    };

    // 单步调试流程
    const stepOver = async (event) => {
      await flowStepOver({
        instanceId: module.value.debugInstanceId,
        taskId: event.taskId,
        all: event.all,
      });
    };

    // 结束模拟流程
    const debugStop = async () => {
      await flowStopDebug(module.value.debugInstanceId);
      flowRef.value.toogleSimulate(false);
      flowRef.value.toogleMonitor(false);
    };

    // 刷新模拟记录
    const refreshDebugRecord = async () => {
      const record = await getDebugRecord(module.value.debugInstanceId);
      const result = {};
      for (let index = 0; index < record.length; index += 1) {
        const element = record[index];
        result[element.id] = {
          pending: element.pending,
          total: element.total,
        };
      }
      return result;
    };

    /**
     * 流程图变更时触发
     */
    const onChange = async (args) => {
      if (changed.value === false) {
        changed.value = true;
      }
    };

    /**
     * 显示模拟数据
     */
    const viewSimData = (params) => {
      simulateDataViewDlgRef.value.show({
        instanceId: module.value.debugInstanceId,
        taskId: params.taskId,
      });
    };

    const monitor = async (enabled, instanceId, engineType) => {
      let monitorData = { tasks: [] };
      if (enabled) {
        monitorData = await monitorCampaign({ flowId: id, instanceId, engineType });
      }
      monitorData.enabled = enabled;
      return monitorData;
    };

    onMounted(async () => {
      EventBus.listen("stepOver", stepOver);
      EventBus.listen("debugStop", debugStop);
      EventBus.listen("viewSimData", viewSimData);
      // 延时1秒刷新流程模拟状态
      // setTimeout(async () => {
      //   const inst = await getDebugInstance(id);
      //   if (inst) {
      //     debugInstanceId.value = inst.id;
      //     flowRef.value.toogleSimulate(true);
      //   }
      // }, 1000);
    });

    onUnmounted(() => {
      EventBus.unlisten("stepOver");
      EventBus.unlisten("debugStop");
      EventBus.unlisten("viewSimData");
    })
    onBeforeRouteLeave(async (to, from, next) => {
      // if (changed.value) {
      //   await Modal.confirm({
      //     title: "是否确定离开",
      //     content: "活动变更未保存，是否确定离开？",

      //     onBeforeOk: () => {
      //     if(flowRef.value) {
      //     flowRef.value.toogleSimulate(false);
      //     flowRef.value.toogleMonitor(false);
      //     }
      //     next();
      //     },
      //     onBeforeCancel: () => {
      //       next(false);
      //     },
      //   });

      // }else{
      //     next();
      // }
      if(flowRef.value) {
      flowRef.value.toogleSimulate(false);
      flowRef.value.toogleMonitor(false);
      }
      next();

    });
    const setup = {
      module,
      entity,
      save,
      bindData,
      flowRef,
      simulateDlgRef,
      saveTemplateRef,
      showSaveTemplateDlg,
      showDebugHistoryDlg,
      simulateEventDlgRef,
      simulateDataViewDlgRef,
      selectTimeRangeDlgRef,
      showBasicConfig,
      modifyBasic,
      taskDrawerRef,
      simulate,
      simulateEvent,
      monitor,
      validate,
      onChange,
      editEnable,
      refreshDebugRecord,
      viewSimData,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.flow-edit {
  height: calc(100vh - 160px);
  overflow: hidden;
}

.notify-list {
  .item {
    display: block;
  }
}
</style>
