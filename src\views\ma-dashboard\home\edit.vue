/** *by:<EMAIL> on 2022/8/8 0008 */
<template>
  <module edit>
    <template v-slot:main>
      <div class="dashboard-edit">
        <a-tabs type="capsule" @tab-click="tabClick">
          <a-tab-pane key="1" title="基本信息">
            <a-form :model="dataForm" ref="dataFormRef">
              <a-form-item
                field="name"
                label-col-flex="90px"
                :rules="[{ required: true, message: '请输入仪表盘名称' }]"
                :validate-trigger="['change', 'input']"
                label="仪表盘名称"
              >
                <a-input
                  v-model="dataForm.name"
                  placeholder="请输入仪表盘名称"
                />
              </a-form-item>
              <a-form-item
                field="description"
                label-col-flex="90px"
                label="仪表板说明"
              >
                <a-textarea
                  v-model="dataForm.description"
                  placeholder="请输入仪表板说明"
                />
              </a-form-item>
            </a-form>
          </a-tab-pane>
          <a-tab-pane key="2" title="图表布局">
            <div v-if="curTab === '2'" class="btn-list">
              <a-button
                class="item-btn"
                size="small"
                type="primary"
                @click="onAddEditChartItem"
              >
                <template #icon> <i class="iconfont icon-chart"></i> </template
                >添加图表</a-button
              >
              <a-button type="primary" size="small" @click="deldeteChartItem()">
                <template #icon> <i class="iconfont icon-empty"></i> </template
                >清空图表</a-button
              >
            </div>
            <grid-layout
              v-if="dataForm.visualizes.length > 0"
              v-model:layout="dataForm.visualizes"
              :col-num="12"
              :row-height="32"
              is-draggable
              is-resizable
              vertical-compact
              use-css-transforms
            >
              <grid-item
                v-for="item in dataForm.visualizes"
                :static="item.static"
                :x="item.x"
                :y="item.y"
                :w="item.w"
                :h="item.h"
                :i="item.i"
                :key="item.i"
              >
                <icon-close
                  class="btn-remove"
                  @click="deldeteChartItem(item.i)"
                />
                <ChartItem :height="item.h * 32 + 'px'" :dataItem="item" />
              </grid-item>
            </grid-layout>
            <a-empty
              v-else
              description="点击上方工具栏中的按钮，添加图表至当前仪表板"
            />
          </a-tab-pane>
          <a-tab-pane key="3" title="全局过滤">
            <TableEditChartFilter
              :dataList="dataForm.filterControls"
              @change="changeFilterControls"
            ></TableEditChartFilter>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!--  添加图表   -->
      <AddEditChartItem
        ref="addEditChartItemRef"
        @change="changeEditItem"
      ></AddEditChartItem>
    </template>
  </module>
</template>

<script>
import { provide, ref } from "vue";
import { getDataList, putItemChart } from "@/api/dashboard";
import { useRoute, useRouter } from "vue-router";
import { useBussinessUnitStore } from "@/store";
import { Message, Modal } from "@arco-design/web-vue";
import { GirdLayout, GridItem } from "vue-grid-layout";
import ChartItem from "@/components/bi/components/chart-item.vue";
import AddEditChartItem from "./components/add-edit-chart-item.vue";
import TableEditChartFilter from "./components/table-edit-chart-filter.vue";

export default {
  name: "dashboard-edit",
  components: {
    ChartItem,
    GirdLayout,
    GridItem,
    AddEditChartItem,
    TableEditChartFilter,
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const module = ref({
      editPath: "/dashboard/edit",
      mainPath: "/dashboard/home",
      breadcrumb: [
        {
          name: "工作台",
          path: "/dashboard/home",
        },
        {
          name: "编辑仪表盘",
        },
      ],
    });

    const id = ref(route.query.id);
    const dataForm = ref({ visualizes: [] });
    const dataFormRef = ref(null);

    // 获取数据
    const bindData = () => {
      let visualizes = [];
      getDataList(route.query.id).then((res) => {
        dataForm.value = {
          ...dataForm.value,
          ...res,
        };
        res.charts.forEach((item) => {
          visualizes.push({
            ...item,
            moved: false,
            static: false,
          });
        });
        dataForm.value.visualizes = visualizes;
      });
    };

    // 保存
    const save = async () => {
      const res = await dataFormRef.value.validate();
      if (res) {
        return false;
      }

      putItemChart(dataForm.value).then((res) => {
        Message.success("保存成功");
        router.push({ path: module.value.mainPath });
      });
    };

    // 切换标签列表
    const curTab = ref("1");
    const tabClick = (val) => {
      curTab.value = val;
    };

    // 清空图表
    const deldeteChartItem = (id) => {
      Modal.confirm({
        title: `${id ? "清除" : "清空"}图表`,
        content: `${
          id ? "真的要清除当前的图表吗?" : "真的要清空当前仪表板中的所有图表吗?"
        }`,
        onOk: async () => {
          if (id) {
            const index = dataForm.value.visualizes.findIndex((item) => {
              return id === item.i;
            });
            dataForm.value.visualizes.splice(index, 1);
          } else {
            dataForm.value.visualizes = [];
          }
        },
      });
    };

    // 新增图表
    const addEditChartItemRef = ref(null);
    const onAddEditChartItem = () => {
      addEditChartItemRef.value.isEditVisible = true;
    };

    // 新增数据
    const changeEditItem = (data) => {
      data.forEach((item) => {
        dataForm.value.visualizes.push({
          ...item,
          moved: false,
          static: false,
          w: 6,
          h: 6,
          x: 0,
          y:
            dataForm.value.visualizes[dataForm.value.visualizes.length - 1].y +
            6,
        });
      });
    };

    // 修改数据
    const changeFilterControls = (data) => {
      dataForm.value.filterControls = data;
    };

    const setup = {
      module,
      save,
      dataForm,
      bindData,
      dataFormRef,
      curTab,
      tabClick,
      id,
      deldeteChartItem,
      addEditChartItemRef,
      onAddEditChartItem,
      changeEditItem,
      changeFilterControls,
    };
    provide("edit", setup);
    return setup;
  },
};
</script>
<style lang="less" scoped>
.dashboard-edit {
  padding: 20px;
}
.vue-grid-layout {
  width: 100%;
  margin: -10px 0 0 0;
}
.vue-grid-item:not(.vue-grid-placeholder) {
  background-color: var(--color-bg-1);
  border: 1px solid #999999;
  padding: 16px 16px;
  border: 1px solid var(--color-neutral-3);
  border-radius: var(--border-radius-small);
}
.vue-grid-item .resizing {
  opacity: 0.9;
}
.vue-grid-item .static {
  background: #fff;
}
.vue-grid-item .text {
  font-size: 24px;
  text-align: center;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  height: 100%;
  width: 100%;
}
.vue-grid-item .no-drag {
  height: 100%;
  width: 100%;
}
.vue-grid-item .minMax {
  font-size: 12px;
}
.vue-grid-item .add {
  cursor: pointer;
}
.vue-draggable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 0;
  left: 0;
  background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='10' height='10'><circle cx='5' cy='5' r='5' fill='#999999'/></svg>")
    no-repeat;
  background-position: bottom right;
  padding: 0 8px 8px 0;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  cursor: pointer;
}
.btn-list {
  position: absolute;
  left: 0;
  top: 0;
  .item-btn {
    margin-right: 10px;
  }
}
.btn-remove {
  position: absolute;
  right: 5px;
  top: 5px;
  color: #999999;
  cursor: pointer;
}
</style>
