import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import CustomizationNode from "./node.vue";
import pannel from "./pannel.vue";
import help from "./help.vue";

const nodeData = {
  type: "customization",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<CustomizationNode />`,
      components: {
        CustomizationNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("CustomizationNode", nodeData.node, true);
};

const ExtendData = {
  type: "customization",
  name: "定制功能",
  shape: "CustomizationNode",
  iconClass: "icon-flow-stop",
  color: "#ffffff",
  themebg: "#ff6d69",
  registerNode,
  help,
  pannel,
  skippable: false,
};


export default ExtendData;
