<template>
  <div class="data-table">

    <a-table ref="table" class="data-table" :data="dataList" :pagination="false">
      <template #columns>
        <a-table-column :title="tableColumns[0]" data-index="key">
          <template #cell="{ record }">
            <a-input v-if="!isNumber(keyType)" v-model="record.key" class="item-input" />
            <a-input-number v-if="isNumber(keyType)" v-model="record.key" class="item-input" />
          </template>
        </a-table-column>
        <a-table-column :title="tableColumns[1]" data-index="value">
          <template #cell="{ record }">
            <a-input v-model="record.value" class="item-input" />
          </template>
        </a-table-column>
        <a-table-column :width="50">
          <template #title>
            <a-button @click="handleAdd">{{t('global.button.add')}}</a-button>
          </template>
          <template #cell="{ rowIndex }">
            <a-button class="del-btn" @click="handleDelete(rowIndex)">删除</a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref,getCurrentInstance } from "vue";
import { Notification } from "@arco-design/web-vue";
import { isNumber } from "@/utils/filter";

const {
      proxy: { t }
    } = getCurrentInstance()
const props = defineProps({
  title: String,
  columns: Array,
  map: Object,
  keyType: String,
  repetition: String,
});
const dataList = ref([]);
const tableColumns = props.columns ? props.columns : ["key", "value"];

const handleAdd = () => {
  dataList.value.push({ key: "", value: "" });
};
const handleDelete = (index) => {
  dataList.value.splice(index, 1);
};
const validate = () => {
  if (!props.repetition) {
    return true;
  }
  // 校验是否有名称为空的条码
  for (let index = 0; index < dataList.value.length; index += 1) {
    if (!dataList.value[index].key){
      Notification.warning({
          title: `${props.title}的${props.columns[0]}不能为空`,
        });
      return false;
    }
  }
  // 校验是否有重复条目
  for (let index = 0; index < dataList.value.length; index += 1) {
    if (dataList.value[index].key === dataList.value[index + 1].key) {
      return false;
    }
  }
  return true;
};
</script>

<style scoped lang="less">
.data-table{
  width: 100%;
}
.item {
  margin-top: 10px;
  .item-input {
    width: 36%;
  }
  .del-btn {
    margin-left: 10px;
  }
}
</style>
