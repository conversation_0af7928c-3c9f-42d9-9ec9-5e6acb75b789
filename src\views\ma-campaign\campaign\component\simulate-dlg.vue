<template>
  <a-modal v-model:visible="visible" @before-ok="handleOk">
    <template #title>流程模拟</template>
    <a-form ref="dataFormRef" :model="entity">
      <a-form-item label="启用定时器" field="scheduleEnable">
        <a-switch v-model="entity.scheduleEnable" type="round">
          <template #checked>开</template>
          <template #unchecked>关</template>
        </a-switch>
      </a-form-item>
      <a-form-item label="发送沟通消息" field="sendMessage">
        <a-switch v-model="entity.sendMessage" type="round">
          <template #checked>开</template>
          <template #unchecked>关</template>
        </a-switch>
      </a-form-item>
      <!-- <a-form-item label="单步调试" field="stepOver">
        <a-switch v-model="entity.stepOver" type="round">
          <template #checked>开</template>
          <template #unchecked>关</template>
        </a-switch>
      </a-form-item> -->
      <a-form-item v-if="simAudienceNodes.length > 0" label="测试人群" field="audienceId">
        <div class="audience-list">
        <template v-for="node of simAudienceNodes" :key="node.nodeId">
          <div class="audience-item">
            <div>{{ node.nodeName }}</div>
            <div class="audience-select">
              <a-select v-model="node.audienceId" :allow-clear="true" placeholder="请选择人群" allow-search>
                <a-option v-for="item of audiences" :key="item.id" :label="item.name" :value="item.id"></a-option>
              </a-select>
            </div>
          </div>
        </template>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { modalCommit } from "@/utils/modal";
import { findAudienceList } from "@/api/audience";
import { simulateCampaign } from "@/api/campaign";

const visible = ref(false);
const entity = ref({
  budgetEnable: false,
  stepOver: false,
  scheduleEnable: false,
  sendMessage: false,
});
const dataFormRef = ref(null);
const audiences = ref([]);
const callback = ref(null);
const flowId = ref(null);
const campaignType = ref(null);
const simAudienceNodes = ref([]);

const handleOk = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    const _audienceIds = {}
    simAudienceNodes.value.forEach(it => {
      _audienceIds[it.nodeId] = it.audienceId
    })
    const request = {
      campaignId: flowId.value,
      setting: {
        breakPoints: [],
        budgetEnable: entity.value.budgetEnable,
        engineType: "DEBUG",
        scheduleEnable: entity.value.scheduleEnable,
        sendMessage: entity.value.sendMessage,
        stepOver: entity.value.stepOver,
        audienceIds: _audienceIds
      },
      triggerType: "DEBUG",
    };

    const inst = await simulateCampaign(request);
    callback.value(inst.id);
  });
};

const show = async (id, type, audienceNodes) => {
  visible.value = true;
  const params = { fields: "name", expression: "usageType eq TEST" };
  flowId.value = id;
  campaignType.value = type;

  simAudienceNodes.value.splice(0, simAudienceNodes.value.length)
  audienceNodes.forEach(it => {
    simAudienceNodes.value.push({"nodeId": it.id, "nodeName": it.data._name, "audienceId": null});
  });
  audiences.value = await findAudienceList(params);
  return new Promise((resolve, reject) => {
    callback.value = resolve;
  });

};

defineExpose({
  show,
});
</script>

<style  lang="less" scoped>
.audience-list{
  display: block;
  .audience-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .audience-select {
      width: 300px;
      padding-left: 10px;
    }
  }
}
</style>
