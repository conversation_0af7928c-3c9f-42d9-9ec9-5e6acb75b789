<template>
  <div class="easyflow-pannel-start">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity">

      <!-- 人群 -->
      <a-form-item v-if="!entity.multiple" :label="t('campaign.pannel.selectAudience')" class="form-item-select"
        :disabled="!editEnable" :rules="[{ required: true, message: '不能为空' }]">
        <a-select v-model="entity.audienceId" :allow-clear="true" :placeholder="t('campaign.pannel.selectAudience')"
          allow-search @change="onChangeAudience(entity.audienceId)">
          <a-option v-for="item of audiences" :key="item.id" :label="item.label" :value="item.id">
            [{{ item.name }}] <span class="light-text"> {{ item.id }} </span>
          </a-option>
        </a-select>

        <slot v-if="!entity.snapshotId" name="extra">
          <div class="estimate-number">
            <span>{{ t('campaign.pannel.estimatedNumber') }}<i>{{ audienceEstimate !== "" ? audienceEstimate : "--" }}</i>人</span>
          </div>
        </slot>
      </a-form-item>
      <a-form-item v-if="!entity.multiple" :label="t('campaign.pannel.audienceSnapshot')" class="form-item-select"
        :disabled="!editEnable">
        <a-select v-model="entity.snapshotId" :allow-clear="true" :placeholder="t('campaign.pannel.audienceSnapshot')"
          @change="onChangeSnapshot(entity.snapshotId)">
          <a-option v-for="item of snapshotList" :key="item.value" :value="item.value">{{ item.label }}</a-option>
        </a-select>
        <slot v-if="!!entity.snapshotId" name="extra">
          <div class="estimate-number">
            <span>{{ t('campaign.pannel.estimatedNumber') }}<i>{{ snapshotEstimate !== "" ? snapshotEstimate : "--" }}</i>人</span>
          </div>
        </slot>
      </a-form-item>

      <a-form-item :label="t('campaign.pannel.marketingFrequency')" :disabled="!editEnable">
        <a-select v-model="entity.schedule.type" class="easyflow-select" :options="frequencyOptions"
          :placeholder="t('campaign.pannel.marketingFrequency')">
          <template #option="{ data }">
            <div class="easyflow-option">
              <span class="name">{{ data?.label }}</span>
              <i class="desc">{{ data.raw?.note }}</i>
            </div>
          </template>
        </a-select>
      </a-form-item>

      <a-form-item :label="t('campaign.pannel.triggerTime')">

        <template v-if="entity.schedule.type == 'DATE'">
          <a-space>
            <a-date-picker v-model="entity.schedule.date" :show-time="true" value-format="YYYY-MM-DDTHH:mm:ss.000ZZ"
              class="form-date" :disabled="!editEnable" /> {{ t('campaign.pannel.startCampaign') }}
          </a-space>
        </template>

        <template v-if="entity.schedule.type == 'CRON'">
          <div>
            <a-range-picker v-model="timeRange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="!editEnable"
              @change="handleRangeTime" />
            <CronSelect v-model:cron="entity.schedule.cron" :disabled="!editEnable" />
          </div>
        </template>
      </a-form-item>
      <a-form-item :label="t('campaign.pannel.audienceDeduplication')" :disabled="!editEnable">
        <a-space>
          <a-switch v-model="entity.unique" type="round" />
          <a-tree-select v-if="entity.unique" v-model="entity.uniqueFields" :multiple="true" :allow-search="true" :data="dataModelFields"
            allow-clear :field-names="treeFieldStruct" :placeholder="t('campaign.reminder.field')" />
        </a-space>
      </a-form-item>
    </a-form>
    <a-form v-if="flow.entity.status == 'RUNNING'" class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity">
      <a-form-item :label="t('campaign.pannel.appendAudience')">
        <a-button type="primary" @click="appendCustomer">{{ t('campaign.pannel.appendAudience') }}</a-button>
      </a-form-item>
    </a-form>
    <AppendAudienceDlg ref="appendAudienceRef" :flow-id="flow.flowId" :flow-name="flow.entity.name" instance-id=""
      :task-id="props.node.id" model-id="Customer" />
  </div>
</template>

<script setup>
import { ref, onMounted, inject, getCurrentInstance } from "vue";
import { frequencyOptions } from "@/constant/easyflow";
import moment from "moment";
import { findCustomerModel } from "@/api/system";
import { findAudienceList, countAudience, getCrowdSnapshot } from "@/api/audience";
import { formatFields } from "@/utils/field"
import { filters } from "@/utils/filter";
import { treeFieldStruct } from "@/constant/common"
import CronSelect from "@/components/ma/cron-select/index.vue";
import AppendAudienceDlg from "@/components/modal-dlg/append-audience-dlg.vue";

const {
  proxy: { t }
} = getCurrentInstance();

const props = defineProps(["node", "easyflow"]);
const timeRange = ref(null);
const eventTimeRange = ref(null);
const audienceEstimate = ref(0); // 预估人数
const snapshotEstimate = ref(0); // 预估人数
const pannelInject = inject("pannel");
const flow = inject("flow");
const { editEnable } = pannelInject;

const entity = ref({
  multiple: false,
  audienceId: null, // 人群Id
  unique: true,     // 是否允许重复
  schedule: {
    date: null, // 定时时间
    cron: null, // cron表达式
    type: "DATE", // 定时类型：NONE,DELAY,DATE,CRON
    endTime: null, // 定时结束时间
    startTime: null, // 定时开始时间
  },
});

const audiences = ref(null); // 人群列表
const snapshotList = ref(null); // 人群快照
const appendAudienceRef = ref(null);
const dataModelFields = ref([]);

const handleRangeTime = () => {
  [entity.value.schedule.startTime, entity.value.schedule.endTime] = timeRange.value;
};

/**
 * 查询快照列表
 */
const getSnapshotList = async (audienceId) => {
  snapshotList.value = []
  let params = {}
  if (audienceId) {
    params = { expression: `audienceId eq ${audienceId}` }
  }
  await getCrowdSnapshot(params).then(res => {
    res.content.forEach(item => {
      snapshotList.value.push({ value: item.id, num: item.num, label: moment(item.createDate).format('YYYY-MM-DD HH:mm:ss') })
    })
  })
}

const onChangeAudience = async (audienceId) => {
  // 查询前判断是否还存在分组
  const item = audiences.value.find((x) => {
    return x.id === audienceId;
  });
  if (!item) {
    entity.value.audienceId = "";
    entity.value.audienceName = "";
    audienceEstimate.value = "";
    return false;
  }
  audienceEstimate.value = await countAudience(audienceId);
  entity.value.audienceName = item.name;
  entity.value.snapshotId = "";
  getSnapshotList(audienceId)
};

const onChangeSnapshot = async (snapshotId) => {
  if (snapshotId) {
    snapshotEstimate.value = filters(snapshotList.value, snapshotId, "num", "value");
    entity.value.snapshotName = filters(snapshotList.value, snapshotId, "label", "value");
  }
};

const handleSearchAudience = async (value) => {
  const params = { fields: "name,usageType", expression: "usageType ne TEST" };
  if (value) {
    params.expression = `AND name like ${value}`;
  }
  audiences.value = await findAudienceList(params);
  audiences.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
};

const save = () => {
  return entity.value;
};

const appendCustomer = async () => {
  appendAudienceRef.value.show();
};

defineExpose({
  save,
});

onMounted(async () => {
  Object.assign(entity.value, props.node.data);
  timeRange.value = [
    entity.value.schedule.startTime,
    entity.value.schedule.endTime,
  ];
  eventTimeRange.value = [
    entity.value.eventStartTime,
    entity.value.eventEndTime,
  ];
  const dataModel = await findCustomerModel();
  dataModelFields.value = formatFields(dataModel.fields, { path: "Customer" });
  await handleSearchAudience();
  if (entity.value.audienceId) {
    await getSnapshotList(entity.value.audienceId);
    audienceEstimate.value = await countAudience(entity.value.audienceId);
  }
  if (entity.value.snapshotId) {
    onChangeSnapshot(entity.value.snapshotId);
  }
});

</script>

<style lang="less" scoped>
.easyflow-pannel-start {
  .easyradio-group {
    width: 100%;

    .easyradio {
      width: 50%;
    }
  }

  .repeat-div {
    margin-top: 10px;
    justify-content: space-between;
    display: flex;
  }

  .form-date {
    width: 200px;
    margin: 0 10px;
  }

  .repeat-select {
    margin-right: 20px;
    width: 120px;
  }

  .form-file {
    width: 100%;
    height: 90px;
    display: flex;
    cursor: pointer;
    font-size: 12px;
    color: #999999;
    align-items: center;
    justify-content: center;
    border: 1px dashed #dddddd;

    &:hover {
      opacity: 0.8;
      border: 1px dashed #999999;
    }
  }

  .between {
    display: flex;
    justify-content: space-between;
  }
}

.between-option {
  display: flex;
  justify-content: space-between;
}

.usage-type {
  color: lightseagreen;
}

.form-item-select {
  position: relative;

  .estimate-number {
    position: absolute;
    top: 14px;
    right: 0;
    zoom: 0.8;
    padding: 0 5px;
    background-color: #3370ff;
    color: #ffffff;
  }
}
</style>
