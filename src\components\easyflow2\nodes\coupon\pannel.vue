<template>
  <div class="easyflow-pannel-coupon">
    <a-form class="easyflow-pannel-form pannel-form" layout="vertical" :model="entity" :disabled="!editEnable">
      <a-form-item label="优惠券模板" class="form-item-select">
        <a-select v-model="entity.capabilityId" class="easyflow-select" placeholder="请选择优惠券模板" :loading="loading"
          :filter-option="false" allow-search @change="showContent(entity.capabilityId)">
          <a-option v-for="item of benefits" :key="item.id" :value="item.id">
            [{{ item.name }}] <span class="light-text"> {{ item.id }} </span>
          </a-option>
        </a-select>
      </a-form-item>
      <!-- <a-form-item label="模板示例">
        <span class="coupon-content">
          {{ content }}
        </span>
      </a-form-item> -->

      <!-- <a-form-item label="预算设置" :content-flex="false">
        <a-checkbox class="budget" v-model="entity.budgetSetting.enabled" value="1" @change="toogleBudeget"
          >参与计算费用 ( 单价: <span> {{ unitBudget }}</span
          >元 )</a-checkbox
        >
        <div v-if="entity.budgetSetting.enabled">
          <div class="flex-line">
            <a-select
              class="easyflow-select"
              v-model="entity.budgetSetting.budgetItemId"
              placeholder="请选择预算条目"
              @search="handleSearchBudget"
              :loading="loading"
              :filter-option="false"
            >
              <a-option
                v-for="item of budgets"
                :key="item.id"
                :value="item.id"
                >{{ item.name }}</a-option
              >
            </a-select>
            <a-button
              class="btn"
              type="outline"
              @click="addBudgetItemRef.show()"
              >新增</a-button
            >
          </div>
        </div>
        <template v-slot:extra>
          <div class="tip">预算不足时停止发送</div>
        </template>
</a-form-item> -->
    </a-form>

    <AddBudgetItemDlg ref="addBudgetItemRef" :budget-id="budgetId" :bind-data="handleSearchBudget" />
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from "vue";
import { findBenefitList, findBenefitItem } from "@/api/benefit";
import { findBudgetItemList } from "@/api/budget";
import AddBudgetItemDlg from "@/components/modal-dlg/add-budget-item-dlg.vue";

const props = defineProps(["node"]);
const { node } = props;
const flowIjt = inject("flow");
const budgetId = ref(flowIjt.flowId);
const pannelInject = inject("pannel");
const { editEnable } = pannelInject;

const entity = ref({
  capabilityId: null,
  budgetSetting: {
    enabled: false,
    budgetId: budgetId.value
  },
  frequencyLimit: true
});
const content = ref("");
const benefits = ref([]);
const loading = ref(false);
const budgets = ref([]);
const unitBudget = ref(0);
const addBudgetItemRef = ref(null);

const handleSearchBenefit = async (name) => {
  loading.value = true;
  const params = { fields: "name,content,budgetSetting", expression: "" };
  params.expression = "type eq coupon AND status eq ENABLED";
  if (name) {
    params.expression += ` AND name like ${name}`;
  }
  benefits.value = await findBenefitList(params);
  benefits.value.forEach((it) => { it.label = `[${it.name}] ${it.id}` })
  loading.value = false;
};

const showContent = async (capabilityId) => {
  const benefit = await findBenefitItem(capabilityId);
  content.value = benefit.content;
  unitBudget.value = benefit.budget;
  entity.value.budgetSetting.budgetValue = benefit.budget;
};

const handleSearchBudget = async (name) => {
  loading.value = true;
  const params = {
    fields: "name",
    expression: `budgetId eq ${budgetId.value}`
  };
  if (name) {
    params.expression += ` AND name like ${name}`;
  }
  budgets.value = await findBudgetItemList(params);
  loading.value = false;
};

const toogleBudeget = () => {
  if (entity.value.budgetSetting.enabled) {
    entity.value.budgetSetting.budgetId = budgetId.value;
  }
};

const save = () => {
  return entity.value;
};

defineExpose({
  save
});
onMounted(() => {
  Object.assign(entity.value, node.data);
  handleSearchBenefit();
  // showContent(entity.value.capabilityId);
  // handleSearchBudget();
});
</script>

<style lang="less" scoped>
.easyflow-pannel-coupon {
  .coupon-content {
    color: #b3b3b3;
  }

  .budget {
    color: rgb(var(--primary-6));

    .tip {
      color: #b3b3b3;
    }
  }
}
</style>
