export default {
  "menu.campaign": "Campaign",
  "menu.campaign.campaign": "Campaign Manage",
  "menu.campaign.kpi": "KPI",
  "menu.campaign.abtest": "A/B Test",
  "menu.campaign.budget": "Budget",
  "menu.campaign.history": "History",
  campaign: {
    title: "Marketing Campaign",
    name: "Campaign Name",

    button: {
      search: "Search",
      reset: "Reset",
      create: "Create",
    },

    flow: {
      title: "Campaign Flow",
      campaign: "Flow Campaign",
      taskCommit: "Submit for Approval",
      taskRecord: "Operation Record",
    },

    task: {
      title: "Campaign Task",
      package: "Audience Package Campaign",
      name: "Name",
      description: "Description",
      campaignPeriod: "Campaign Period",
      campaignCategory: "Campaign Category",
      campaignTags: "Campaign Tags",
      selectAudiencePackage: "Select Audience Package",
      estimatedPopulation: "Estimated Population",
      executionAction: "Execution Action",
      diversionStrategy: "Diversion Strategy",
      noDiversion: "No Diversion",
      percentageDiversion: "Percentage Diversion",
      operationTime:"Operation time",
      operationType:"Operation type",
      operationUser:"Operation user",
      operationInfo:"Operation information",
    },

    pannel: {
      selectAudience:" Select Audience",
      audienceSnapshot:"Audience Snapshot",
      estimatedNumber:"Estimated Population",
      marketingFrequency:"Marketing Frequency",
      triggerTime:"Trigger Time",
      startCampaign:"Start Campaign",
      audienceDeduplication:"Audience Deduplication",
      appendAudience:"Append Audience",
      nodeDescription:"Node Description",
      remarkInfo:"Remark Information",
      reachContent:"Reach Content",
      reachField:"Reach Field",
      frequencyLimit:"Frequency Limit",
      deduplication:"Deduplication",
      selectDeduplication: "Select Deduplication Field",
    },

    report: {
      title: "Campaign Report",
      cumulativeReachCount: "Cumulative Reach Count",
      cumulativeReachPeople: "Cumulative Reach People",
      reachChannel: "Reach Channel",
      reachCount: "Reach Count",
      todayReachCount: "Today Reach Count",
      todayReachSuccessRate: "Today Reach Success Rate",
      cumulativeReachSuccessCount: "Cumulative Reach Success Count",
      cumulativeReachSuccessRate: "Cumulative Reach Success Rate",
      channelName: "Channel Name",
      channel: "Channel",
      operationTime: "Operation Time",
      campaignId: "Campaign ID",
      campaignName: "Campaign Name",
      campaignCategory: "Campaign Category",
      canvasId: "Canvas ID",
      canvasName: "Canvas Name",
      executionStatus: "Execution Status",
      customerId: "Customer ID",
      customerIdentityField: "Customer Identity Field",
      reachField: "Reach Field",
      reachFieldValue: "Reach Field Value",
      reachContent: "Reach Content",
      reachData: "Reach Data",
      reachStatus: "Reach Status",
      processInstanceId: "Process Instance ID",
      canvasNodeId: "Canvas Node ID",
      reachTemplateId: "Reach Template ID",
      reachTemplateName: "Reach Template Name",
      reachContentId: "Reach Content ID",
      reachContentName: "Reach Content Name",
      acceptanceStatus: "Acceptance Status",
      completionStatus: "Completion Status",
      errorStatus: "Error Status",
      nodeType: "Node Type",
      isTemplateFrequencyLimit: "Template Frequency Limit",
      isChannelFrequencyLimit: "Channel Frequency Limit",
      isGlobalFrequencyLimit: "Global Frequency Limit",
      message: "Message",
    },


    budget: {
      budgetManagement: "Budget Management",
      budgetSettings: "Budget Settings",
      entryName: "Entry Name",
      plan: "Plan",
      used: "Used",
      remaining: "Balance",
      warningValue: "Warning Value",
      action: "Action",
      addBudget: "Add Budget",
    },

    canvas: {
      title: "Campaign Canvas",
      code: "Code",
      name: "Name",
      category: "Category",
      status: "Status",
      type: "Campaign Type",
      saveAsTitle: "Save Canvas as File",
      remark: "Remark",
      addTitle: "Add Canvas",
      canvasId:"Canvas ID",
      canvasName:"Canvas name",
      campaignCode:"Activity code",
      timePeriod:"Time period",
      tag:"Tag",
    },
    
    popup: {
      create_title: "Create Campaign",
      edit_title: "Edit Campaign",
      code: "Campaign Code",
      name: "Campaign Name",
      dataRole: "Data Role",
      description: "Description",
      save_title: "Save Marketing Campaign"
    },

    operation: {
      view: "View",
      edit: "Edit",
      delete: "Delete",
      increaseBudget: "Increase Budget",
    },

    column: {
      code: "Campaign Code",
      name: "Campaign Name",
      remark: "Remarks",
      campagin:"Campagin",
      canvasId: "Canvas ID",
      canvasName: "Canvas Name",
      category: "Category",
      type: "Type",
      tags: "Tags",
      status: "Status",
      description: "Description",
      startTime: "Start Time",
      endTime: "End Time",
      action: "Actions",
    },
    reminder: {
      input_activity_name: "Enter the Campaign name",
      data_irretrievable_warning: "Data cannot be recovered after deletion. Are you sure you want to proceed?",
      delete_campaign: "Delete Campaign",
      input_activity_code: "Enter the campaign code",
      input_canvas_id: "Enter the Canvas ID",
      input_canvas_name: "Enter the Canvas Name",
      input_data_role: "Enter the data role",
      input_description: "Enter the campaign description information",
      input_canvas_remark: "Enter the Canvas Remarks",
      activity_code_format: "Campaign codes can only contain uppercase and lowercase letters, numbers, and underscores.",
      inputCanvasCode: "Enter the canvas code",
      inputCanvasName: "Enter the canvas name",
      selectCanvasCategory: "Select the canvas category",
      selectCanvasStatus: "Select the canvas status",
      selectCampaignType: "Select the campaign type",
      name: "Enter the name",
      field: "Select the Field",
      description: "Enter the description",
      campaignPeriod: "Campaign period cannot be empty.",
      campaignCategory: "Select the campaign category",
      campaignTags: "Enter campaign tags, press Enter to add.",
      selectAudiencePackage: "Select the audience package",
      selectAudience: "Select the audience",
      audienceSnapshot: "Select the audience snapshot",
      marketingFrequency: "Select the marketing frequency",
      estimatedPopulation: "Enter the estimated population",
      executionAction: "Select the execution action",
      notNull: "Cannot be empty",
      inputAmount: "Please enter the amount",
      channelName: "Channel Name",
      reachField: "Reach Field",
      reachFieldValue: "Reach Field Value",
      reachStatus: "Reach Status",
      canvasNodeId: "Canvas Node ID",
    }
  }
};
