<template>
  <div class="container">
    <div v-if="module.breadcrumb || module.entityName" class="main-title">
      <slot name="breadcrumb">
        <template v-if="module.breadcrumb">
          <a-breadcrumb>
            <template v-for="item in module.breadcrumb" :key="item.name">
              <a-breadcrumb-item v-if="item.path">
                <router-link :to="item.path">
                  <span style="font-size: 16px; font-weight: normal">
                    {{ item.name }}
                  </span>
                </router-link>
              </a-breadcrumb-item>
              <a-breadcrumb-item v-else>
                <span style="font-size: 16px; font-weight: bold">
                  {{ item.name }}
                </span>
              </a-breadcrumb-item>
            </template>
          </a-breadcrumb>
        </template>
        <template v-else>
          {{ module.entityName }}
        </template>
      </slot>
    </div>
    <!-- 主体占位符 -->
    <div class="general-card">
      <slot name="top"></slot>
      <a-row v-if="filter.length > 0" class="filter-row">
        <a-col :flex="1">
          <slot name="filter">
            <a-form class="filter-form" :model="formModel" auto-label-width label-align="right">
              <a-row :gutter="16">
                <template v-for="(filterItem, filterIndex) in filter" :key="filterIndex">
                  <a-col v-if="filterIndex < openFilterNumber" :span="8">
                    <a-form-item :field="filterItem.field" :label="filterItem.label">
                      <component :is="filterItem.component" v-model="formModel[filterItem.field]"
                        :options="filterItem.dataSource" :allow-clear="filterItem.allowClear"
                        :allow-search="filterItem.allowSearch" :placeholder="filterItem.placeholder"
                        :field-names="filterItem.fieldNames">
                      </component>
                    </a-form-item>
                  </a-col>
                </template>
              </a-row>
            </a-form>
          </slot>
        </a-col>
        <a-col :flex="'86px'" style="text-align: right; padding-left: 20px">
          <a-space :size="18">
            <slot name="search">
              <a-button type="primary" @click="search(1)">
                <template #icon>
                  <icon-search />
                </template>
                {{t('global.button.query')}}
              </a-button>
              <a-button @click="reset">
                <template #icon>
                  <icon-refresh />
                </template>
                {{t('global.button.reset')}}
              </a-button>
              <a-button v-if="filter.length > 3" type="text" @click="onOpenFilter">
                {{ openFilterNumber === 3 ? t('global.button.expand') : t('global.button.retract') }}
              </a-button>
            </slot>
          </a-space>
        </a-col>
      </a-row>

      <a-row v-if="module.showBtn" style="margin-bottom: 16px">
        <a-col :span="16">
          <a-space>
            <slot name="action">
              <a-button type="primary" @click="create">
                <template #icon>
                  <icon-plus />
                </template>
                {{t('global.button.create')}}
              </a-button>
            </slot>
          </a-space>
        </a-col>
        <a-col v-if="module.showCard" :span="8" style="text-align: right">
          <slot name="context">
            <a-radio-group v-model="viewComponent" type="button">
              <a-radio value="table">   {{t('global.button.list')}}</a-radio>
              <a-radio value="card">   {{t('global.button.card')}}</a-radio>
            </a-radio-group>
          </slot>
        </a-col>
      </a-row>

      <slot v-if="viewComponent === 'table'" name="main"> </slot>
      <slot v-if="viewComponent === 'card'" name="card"> </slot>
      <a-pagination v-if="main?.pagination?.value?.showPageSize" :total="main.pagination.value.total" size="small"
        :current="main.pagination.value.page" :page-size="main.pagination.value.size" show-total show-jumper
        show-page-size @change="changePage" @page-size-change="changeSizePage" />
    </div>
  </div>
</template>

<script>
import { ref, inject, onMounted,getCurrentInstance } from "vue";
import { useRouter, onBeforeRouteLeave } from "vue-router";

export default {
  name: "MainTemplate",
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const router = useRouter();
    const main = inject("main");
    const filter = main.filter?.value || [];
    const module = {
      entityName: null, // 不显示面包屑时显示的内容
      editPath: null, // 编辑页路径
      entityIdField: null, // 实体主键
      breadcrumb: null, // 面包屑
      backPath: null, // 后退路径
      showBtn: true, // 显示按钮
      showCard: false, // 显示卡片切换
      ...main.module.value
    };
    const viewComponent = ref(module?.defaultView || "table");

    const entity = main.entity?.value || {};

    const generateFormModel = () => {
      const tFilter = {};
      main.filter?.value?.forEach((f) => (tFilter[f.field] = f.value));
      return tFilter;
    };

    const formModel = ref(generateFormModel());

    // 重置查询要求必须定义并传入bindData API
    const reset = async () => {
      formModel.value = generateFormModel();
      await main.bindData();
    };

    // 过滤查询要求必须定义并传入bindData API
    const search = main.search
      ? main.search
      : async (page) => {
        if (!main.bindData) {
          return;
        }

        if (page && main?.pagination) {
          main.pagination.value.page = page;
        }

        const expression = [];

        filter.forEach((f) => {
          if (formModel.value[f.field]) {
            if (f.comment) {
              expression.push(`${f.field} ${f.operate} '${formModel.value[f.field]}'`);
            } else {
              expression.push(`${f.field} ${f.operate} ${formModel.value[f.field]}`);
            }
          }
        });

        if (expression.length > 1) {
          return main.bindData(expression.join(" AND "));
        }
        if (expression.length === 1) {
          return main.bindData(expression.toString());
        }
        return main.bindData();
      };
    const detail = main.detail
      ? main.detail
      : () => {
        const query = {};
        query[module.entityIdField] = row[module.entityIdField];
        router.push({ path: module.editPath, query });
      };
    const create = main.create
      ? main.create
      : () => {
        router.push({ path: module.createPath });
      };

    // 定义高级筛选
    const openFilterNumber = ref(3);
    const onOpenFilter = () => {
      openFilterNumber.value = openFilterNumber.value === 3 ? filter.length : 3;
    };

    // 切换页数
    const changePage = (e) => {
      main.pagination.value.page = e;
      search();
    };
    // 切换条数
    const changeSizePage = (e) => {
      main.pagination.value.page = 1;
      main.pagination.value.size = e;
      search();
    };

    onMounted(async () => {
      if (main.bindData) {
        await main.bindData();
      }
    });

    // 路由监听
    onBeforeRouteLeave((to, from, next) => {
      if (module.breadcrumb) {
        const breadcrumb = JSON.parse(
          JSON.stringify(main.module.value.breadcrumb)
        );
        breadcrumb[main.module.value.breadcrumb.length - 1].path =
          from.fullPath;
        localStorage.setItem("breadcrumb", JSON.stringify(breadcrumb));
      }
      next();
    });

    return {
      t,
      main,
      reset,
      search,
      filter,
      module,
      detail,
      create,
      formModel,
      viewComponent,
      openFilterNumber,
      onOpenFilter,
      changePage,
      changeSizePage
    };
  }
};
</script>

<style scoped lang="less">
.filter-row {
  border-bottom: 1px solid var(--color-neutral-3);
  margin-bottom: 15px;
}

.main-title {
  background-color: #ffffff;
  margin-top: 20px;
  margin-left: 20px;
  margin-right: 20px;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: bold;
  height: 60px;
  display: flex;
  align-items: center;
}

.general-card {
  margin-left: 20px;
  margin-right: 20px;
  background-color: #ffffff;
  padding: 20px;
  min-height: calc(100vh - 160px);
}

:deep(.arco-pagination-jumper-input) {
  width: 40px;
}
</style>
