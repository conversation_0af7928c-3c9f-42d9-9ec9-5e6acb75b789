import _top from "./shape/top/index.vue";
import _left from "./shape/left/index.vue";
import _right from "./shape/right/index.vue";
import _bottom from "./shape/bottom/index.vue";

export const port_attr = {
  circle: {
    r: 4,
    magnet: true,
    stroke: '#31d0c6',
    strokeWidth: 1,
    fill: '#fff',
  },
}

export const getPorts = (top?: boolean, right?: boolean, bottom?: boolean, left?: boolean) => {

  let result: any = {
    groups: {
      top: {},
      left: {},
      bottom: {},
      right: {},
    },
    items: []
  };
  if (top) {
    result.groups.top = { position: 'top', attrs: port_attr }
    result.items.push({ id: 'in-top-1', group: 'top' })
  }
  if (left) {
    result.groups.left = { position: 'left', attrs: port_attr }
    result.items.push({ id: 'in-top-2', group: 'left' })
  }
  if (bottom) {
    result.groups.bottom = { position: 'bottom', attrs: port_attr }
    result.items.push({ id: 'in-top-3', group: 'bottom' })
  }
  if (right) {
    result.groups.right = { position: 'right', attrs: port_attr }
    result.items.push({ id: 'in-top-4', group: 'right' })
  }
  return result;
}

export const top = _top
export const left = _left
export const right = _right
export const bottom = _bottom
export const rect = {
  x: 0,
  y: 0,
  width: 232,
  height: 104
}
