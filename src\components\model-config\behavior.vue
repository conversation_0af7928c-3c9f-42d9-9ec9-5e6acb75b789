<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <div class="model-config">
    <div class="field-list">
      <div class="field-search-bar">
        <a-input-search v-model="searchKey" class="field-search" />
        <!-- :disabled="!dataModel.own && dataModel.id" -->
        <a-button class="add-btn" type="primary" disabled @click="addRootNode">
          <template #icon>
            <icon-plus />
          </template>
        </a-button>
      </div>
      <div style="height: calc(100vh - 50px); overflow: auto">
        <field-tree
          ref="fieldTreeRef"
          :tree-data="treeData"
          :select-node="selectNode"
          :add-node="addChildNode"
          :remove-node="removeChildNode"
          @selectTreeNode="selectTreeNode"
        />
      </div>
    </div>
    <field-config
      v-if="curField"
      ref="fieldFormRef"
      :field="curField"
      :blur-change="blurChange"
      :change-name="changeName"
      :editable="true"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { Message, Notification } from "@arco-design/web-vue";
import fieldTree from "@/components/model-config/field-tree.vue";
import fieldConfig from "@/components/model-config/field-config.vue";
import { getFieldByPath } from "@/utils/field";

const props = defineProps({
  isRemove: {
    type: Boolean,
    default: false
  },
  dataModel: {
    type: Object,
    default() {
      return {
        fields: [],
        identifyFieldMap: {},
        purposeFields: {},
        pkName: "",
        own: false
      };
    }
  }
});

const emit = defineEmits(["update:dataModel"]);
// 模板内容解析
const model = ref(props.dataModel);

// 默认字段结构
const defaultField = () => {
  return {
    name: "",
    aliasName: "",
    type: null,
    description: "",
    options: {},
    array: false,
    optional: false,
    fields: [],
    path: "",
    defaultValue: "",
    piiData: false
  };
};

const searchKey = ref(""); // 字段搜索关键字

const curField = ref(model.value.fields[0]);

const cont = ref(0);
const fieldFormRef = ref(null);
const fieldTreeRef = ref(null);

function searchData(keyword) {
  const loop = (data) => {
    const result = [];
    data.forEach((item) => {
      if (item.aliasName.toLowerCase().indexOf(keyword.toLowerCase()) > -1) {
        result.push({ ...item });
      } else if (item.fields) {
        const filterData = loop(item.fields);
        if (filterData.length) {
          result.push({
            ...item,
            fields: filterData
          });
        }
      }
    });
    return result;
  };

  return loop(model.value.fields);
}

const treeData = computed(() => {
  if (!searchKey.value) return model.value.fields;
  return searchData(searchKey.value);
});

const selectTreeNode = (val) => {
  // if (val !== undefined) {
  curField.value = val;
  // }
};
const isStringEmpty = (str) => {
  if (str && str.length > 0) {
    return false;
  }
  return true;
};

const checkField = () => {
  if (curField.value == null) {
    return true;
  }
  if (
    isStringEmpty(curField.value.name) ||
    isStringEmpty(curField.value.aliasName) ||
    isStringEmpty(curField.value.type)
  ) {
    Notification.error("您存在未编辑的新增必填字段！");
    return false;
  }
  return true;
};

const selectNode = async (key, node) => {
  if (checkField()) {
    // eslint-disable-next-line prefer-destructuring
    curField.value = node.selectedNodes[0];
  }
};

// 判断是否重复编码
const repeatCode = (fields, path) => {
  const parent = path.substr(0, path.lastIndexOf("."));
  let brotherFields;
  if (parent) {
    brotherFields = getFieldByPath(fields, parent).fields;
  } else {
    brotherFields = fields;
  }
  const { length } = brotherFields;
  const fieldSet = new Set(brotherFields.map((it) => it.name));
  return length !== fieldSet.size;
};

const blurChange = (item) => {
  cont.value = 0;
  if (repeatCode(treeData.value, item.path)) {
    Message.warning("该编码已存在，不能重复！");
    item.name = "";
  }
};

const addRootNode = async () => {
  if (checkField()) {
    model.value.fields.push({
      ...defaultField(),
      isAdd: true
    });
    curField.value = model.value.fields[model.value.fields.length - 1];
    curField.value.path = `${curField.value.path} `;
    fieldTreeRef.value.selectTreeNode(curField.value);
  }
};

const addChildNode = async (nodeDate) => {
  if (checkField()) {
    if (!nodeDate.fields) {
      nodeDate.fields = [];
    }
    nodeDate.fields.push({
      ...defaultField(),
      path: `${nodeDate.path}.`,
      isAdd: true
    });
    curField.value = nodeDate.fields[nodeDate.fields.length - 1];
    fieldTreeRef.value.expandNode(nodeDate);
    fieldTreeRef.value.selectTreeNode(curField.value);
  }
};

// 删除回调
const removeItem = (data, delItem) => {
  const index = data.findIndex((item) => {
    return item.path === delItem.path;
  });

  if (index >= 0) {
    // 判断删除的是否是当前编辑的
    if (curField.value?.path === delItem.path) {
      curField.value = null;
    }
    data.splice(index, 1);
  } else {
    data.forEach((item) => {
      removeItem(item.fields, delItem);
    });
  }
};

const removeChildNode = (nodeDate) => {
  removeItem(model.value.fields, nodeDate);
};

const changeName = (nodeDate) => {
  if (nodeDate.path) {
    const temp = nodeDate.path?.split(".");
    let tempStr;
    for (let i = 0; i < temp.length; i += 1) {
      if (i === 0) {
        if (temp.length === 1) {
          tempStr = nodeDate.name;
        } else {
          tempStr = temp[i];
        }
      } else if (i + 1 === temp.length) {
        tempStr = `${tempStr}.${nodeDate.name}`;
      } else {
        tempStr = `${tempStr}.${temp[i]}`;
      }
    }
    nodeDate.path = tempStr;
  } else {
    nodeDate.path = nodeDate.name;
  }
};

const validate = () => {
  return !checkField();
};

watch(model.value, (data) => {
  emit("update:dataModel", data);
});

defineExpose({
  validate
});
</script>

<style scoped lang="less">
.model-config {
  display: flex;

  .field-list {
    display: flex;
    flex-direction: column;
    width: 280px;

    .field-search-bar {
      display: flex;

      .field-search {
        margin-bottom: 8px;
        max-width: 240px;
      }

      .add-btn {
        margin-left: 10px;
      }
    }
  }

  .field-fieldData {
    flex: 1;
    padding-left: 30px;
    max-width: 450px;
  }
}

::v-deep(.arco-tree-node-title-text) {
  height: 22px;
}

.tag-key {
  color: rgb(var(--arcoblue-6));
  background-color: rgb(var(--arcoblue-1));
  border: 1px solid transparent;
  padding: 0 5px;
  font-size: 10px;
  zoom: 0.8;
  position: absolute;
  right: 60px;
}
</style>
