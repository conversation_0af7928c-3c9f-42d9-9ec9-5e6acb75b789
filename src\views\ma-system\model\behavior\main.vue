<template>
  <module main>
    <template #action>
      <a-button v-permission="['ma_menu.system.behavior-model.sync']" type="primary" :loading="syncing" @click="syncFromDWH"> {{t('global.button.sync')}} </a-button>
      <a-button v-permission="['ma_menu.system.behavior-model.create']" type="primary" @click="create">
        <template #icon>
          <icon-plus />
        </template>
        {{ t('global.button.create')}}
      </a-button>
    </template>
    <template #main>
      <a-table
        ref="table"
        row-key="id"
        :bordered="false"
        :data="dataSource"
        :pagination="false"
      >
        <template #columns>
          <a-table-column :title="t('systemSetting.behaviorModel.modelName')" data-index="aliasName" />
          <a-table-column :title="t('systemSetting.behaviorModel.primaryKey')" data-index="pkName" />
          <a-table-column :title="t('global.button.operation')" :align="'center'">
            <template #cell="{ record }">
              <a-button v-permission="['ma_menu.system.behavior-model.view']" type="text" size="small" @click="detail(record)"
                >  {{ t('global.button.view')}}</a-button
              >
              <a-button
              v-permission="['ma_menu.system.behavior-model.delete']"
                :disabled="!record.operability"
                type="text"
                size="small"
                @click="deleteData(record.id)"
                > {{ t('global.button.delete')}}</a-button
              >
            </template>
          </a-table-column>
        </template>
      </a-table>
    </template>
  </module>
</template>

<script>
import { ref, provide, computed ,getCurrentInstance} from "vue";
import { useRouter } from "vue-router";
import {
  findBehaviorModelPage,
  getReferenceList,
  deleteBehaviorModel
} from "@/api/system";
import { syncBehaviorModelsFromDWH } from "@/api/behavior";
import { Modal, Notification } from "@arco-design/web-vue";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const router = useRouter();
    // 模块设置
    const module = ref({
      entityName: t('systemSetting.basic.behaviorModel'),
      entityIdField: "id",
      breadcrumb: [
        {
          name:  t('menu.system'),
          path: "/system/setting"
        },
        {
          name:  t('systemSetting.basic.behaviorModel')
        }
      ],
      showBtn: true,
      editPath: "/system/behavior-model/edit",
      createPath: "/system/behavior-model/edit"
    });

    // 分页设置
    const pagination = ref({
      page: 1,
      size: 10,
      total: 0,
      showPageSize: true
    });
    const syncing = ref(false);

    // 过滤设置
    const filter = ref([
      {
        field: "aliasName",
        label: t('systemSetting.behaviorModel.modelName'),
        component: "a-input",
        operate: "like",
        placeholder: t('systemSetting.behaviorModel.enterModelName'),
        comment: true,
        value: ""
      }
    ]);

    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = computed(() => entity.value.content || []);

    // 查询API
    const bindData = async (expression) => {
      entity.value = await findBehaviorModelPage(
        {
          ...pagination.value,
          page: pagination.value.page - 1
        },
        {
          expression,
          fields: "aliasName,pkName,createdTime,operability,description"
        }
      );
      pagination.value.total = entity.value.totalElements;
    };

    const create = () => {
        router.push({ path: module.value.createPath });
      };

    // 详情跳转
    const detail = (row) => {
      const query = {};
      query[module.value.entityIdField] = row[module.value.entityIdField];
      router.push({ path: module.value.editPath, query });
    };
    // 同步行为模型
    const syncFromDWH = async () => {
      try {
        syncing.value = true;
        await syncBehaviorModelsFromDWH();
        await bindData();
        Notification.info({ title: t('global.tips.success.sync') });
      } catch (e) {
        console.error(e);
      }
      syncing.value = false;
    };

    // 删除
    const deleteData = async (id) => {
      const references = await getReferenceList(id);
      if (references?.totalElements > 0) {
        Notification.error({
          title:  t('systemSetting.behaviorModel.deleteError'),
          content: `${ t('systemSetting.behaviorModel.changeBehavioEvent')}[${references.content[0]}]${ t('systemSetting.behaviorModel.changeBehavioEventSuffix')}`
        });
      }
      Modal.confirm({
        title: t('systemSetting.behaviorModel.deleteModel'),
        content:  t('systemSetting.behaviorModel.deleteConfirm'),
        onOk: async () => {
          await deleteBehaviorModel(id);
          if (dataSource.value.length === 1 && pagination.value.page > 1) {
            pagination.value.page -= 1;
          }
          await bindData();
        }
      });
    };

    const setup = {
      t,
      module,
      filter,
      entity,
      create,
      detail,
      bindData,
      syncing,
      deleteData,
      dataSource,
      pagination,
      syncFromDWH
    };
    provide("main", setup);
    return setup;
  }
};
</script>
