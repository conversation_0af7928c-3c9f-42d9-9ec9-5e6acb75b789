/**
*by:<EMAIL> on 2022/8/11 0010
*/
<template>
  <div class="AddEditChart">
    <module main>
      <template v-slot:filter></template>
      <template v-slot:search></template>
      <template v-slot:context></template>
      <template v-slot:main>
        <a-button type="primary"
                  @click="isEditVisible = true"
                  size="small" class="add-btn">
          <template #icon>
            <icon-plus />
          </template>
          新增
        </a-button>
        <a-table
            ref="table"
            :bordered="false"
            :data="dataSource"
            :pagination="false">
          <template #columns>
            <a-table-column title="显示名称" data-index="name"/>
            <a-table-column title="控件类型" data-index="type">
              <template #cell="{ record }">
                <i class="iconfont" :class="[`icon-chart-${record.type.toLowerCase()}`]"></i>{{sourceFilter(record.type)}}
              </template>
            </a-table-column>
            <a-table-column title="过滤编码" data-index="code">
              <template #cell="{ record }">
                <span style="color:#344ae7;">
                  {{ record.dataInputSetting.code || record.dataPickerSetting.code || record.datePickerSetting.code }}
                </span>
              </template>
            </a-table-column>
            <a-table-column title="默认值" data-index="name">
              <template #cell="{ record }">
                <span v-if="record.dataInputSetting.code">{{record.dataInputSetting.value}}</span>
                <span v-else-if="record.dataPickerSetting.code">{{record.dataPickerSetting.value}}</span>
                <span v-else-if="record.datePickerSetting.code">{{record.datePickerSetting.valueDisplayName}}</span>
              </template>
            </a-table-column>
            <a-table-column title="操作" align="center" width="90">
              <template #cell="{ record }">
                <a-button @click="deleteData(record.name)" type="text" size="small">删除</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
    <a-modal v-model:visible="isEditVisible"
             v-if="isEditVisible"
             title-align="start"
             @before-ok="handleOk" width="900px">
      <template #title>添加新的全局过滤条件</template>
      <a-form ref="dataFormRef" :model="dataForm" :style="{width: '840px'}" @submit="handleSubmit">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item field="name" label="显示名称"
                         :rules="[{required:true,message:'不能为空'}]"
                         :validate-trigger="['change','input']">
              <a-input v-model="dataForm.name" placeholder="请输入显示名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="type" label="控件类型" >
              <a-select v-model="dataForm.type" type="button">
                <a-option value="DATA_INPUT">输入框</a-option>
                <a-option value="DATA_PICKER">下拉框</a-option>
                <a-option value="DATE_PICKER">日期选择</a-option>
                <a-option value="DATA_CASCADE">联级选择器</a-option>
                <a-option value="HIDDEN">隐藏</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="code" label="过滤编码">
              <a-input v-if="dataForm.type === 'DATA_INPUT' || dataForm.type === 'HIDDEN'" v-model="dataForm.dataInputSetting.code" placeholder="请输入过滤编码" />
              <a-input v-if="dataForm.type === 'DATA_PICKER' || dataForm.type === 'DATA_CASCADE'" v-model="dataForm.dataPickerSetting.code" placeholder="请输入过滤编码" />
              <a-input v-if="dataForm.type === 'DATE_PICKER'" v-model="dataForm.datePickerSetting.code" placeholder="请输入过滤编码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="value" label="默认值">
              <a-input v-if="dataForm.type === 'DATA_INPUT' || dataForm.type === 'HIDDEN'" v-model="dataForm.dataInputSetting.value" placeholder="请输入默认值" />
              <a-input v-if="dataForm.type === 'DATA_PICKER' || dataForm.type === 'DATA_CASCADE'" v-model="dataForm.dataPickerSetting.value" placeholder="请输入默认值" />
              <a-input v-if="dataForm.type === 'DATE_PICKER'" v-model="dataForm.datePickerSetting.valueDisplayName" placeholder="请输入默认值" />
            </a-form-item>
          </a-col>
          <a-col :span="12"></a-col>
          <a-col :span="12"></a-col>
          <a-col :span="12"></a-col>
          <a-col :span="12"></a-col>
          <a-col :span="12"></a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import {computed, defineComponent, provide, ref} from 'vue';
import { useBussinessUnitStore } from "@/store";
import { modalCommit } from "@/utils/modal";
import {Modal, Notification} from "@arco-design/web-vue";

export default defineComponent({
  name: 'AddEditChart',
  props: {
    dataList: {
      type: Array,
      default: []
    }
  },
  emits: ['change'],
  setup(props, { emit }) {
    const isEditVisible = ref(false)
    const userTc = useBussinessUnitStore();
    const buCode = userTc.currentBussinessUnit?.code;
    const tenantId = userTc.currentBussinessUnit?.tenantId;

    // 模块设置
    const module = ref({
      entityIdField: "id",
      showCard: false,
      showBtn: false
    });

    // 分页设置
    const pagination = ref({});

    // 过滤设置
    const filter = ref([]);


    // 数据设置
    const entity = ref({});

    // 列表数据
    const dataSource = computed(() => props.dataList || []);

    // 查询API
    const bindData = async () => {};

    // 确定选择
    const handleOk = async (done) => {
      modalCommit(dataFormRef, done, async () => {
        dataSource.value.push(dataForm.value)
        emit('change', dataSource.value)
      })

    }

    // 筛选
    const sourceFilter = (v) => {
      switch (v) {
        case "DATA_INPUT":
          return "输入框";
        case "DATA_PICKER":
          return "下拉框";
        case "DATE_PICKER":
          return "日期选择";
        case "DATA_CASCADE":
          return "联级选择器";
        case "HIDDEN":
          return "隐藏";
        default:
          return "--";
      }
    }

    // 删除
    const deleteData = (name) => {
      const index = dataSource.value.findIndex(item => {
        return item.name === name
      })
      Modal.confirm({
        title: "移除过滤条件",
        content: '真的要移除所选择的过滤条件吗？',
        onOk: () => {
          dataSource.value.splice(index, 1)
          emit('change', dataSource.value)
        },
      })
    }

    // 选择数据
    const dataFormRef = ref(null)
    const dataForm = ref({
      dataInputSetting: {code: "", value: ""},
      dataPickerSetting: {
        code: "",
        options: {type: "DIMENSION_TABLE", dimensionTableId: "", bdmTableId: null, dict: {}},
        bdmTableId: null,
        dict: {},
        dimensionTableId: "",
        type: "DIMENSION_TABLE",
        value: "",
        valueDisplayName: "",
      },
      datePickerSetting: {
        code: "time",
        timeRange: {
          from: "2022-08-05T10:45:37.148+0800",
          to: "2022-08-05T10:45:37.148+0800"
        },
        type: "LAST_10_DAYS",
        valueDisplayName: "最近10天"
      },
      description: null,
      name: "",
      type: "DATA_INPUT",
    })

    const setup = {
      module,
      filter,
      entity,
      bindData,
      dataSource,
      pagination,
      isEditVisible,
      handleOk,
      sourceFilter,
      deleteData,
      dataForm,
      dataFormRef
    };
    provide("main", setup);
    return setup;
  },
});
</script>
<style lang="less" scoped>
.AddEditChart {
}
:deep(.general-card) {
  min-height: auto;
  margin: 0;
  padding: 0;
}
.iconfont {
  margin-right: 5px;
  color: #999999;
}
.add-btn{
  position: absolute;
  top: 0;
}
</style>
