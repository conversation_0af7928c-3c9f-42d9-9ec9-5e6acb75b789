export default {
  "menu.report": "沟通报表",
  'menu.report.report': '沟通报表',
  report:{
    communicationReport: "沟通报表",
    cumulativeReachCount: "累计触达次数",
    cumulativeReachPeople: "累计触达人数",
    reachChannel: "触达渠道",
    reachCount: "触达次数",
    todayReachCount: "今日触达次数",
    todayReachSuccessCount: "今日成功触达次数",
    todayReachSuccessRate: "今日触达成功率",
    cumulativeReachSuccessCount: "累计成功触达次数",
    cumulativeReachSuccessRate: "累计触达成功率",
    channel: "渠道",
    operationTime: "操作时间",
    campaignId: "活动编号",
    campaignName: "活动名称",
    campaignCategory: "活动分类",
    canvasId: "画布编号",
    canvasName: "画布名称",
    executionStatus: "执行状态",
    customerId: "客户ID",
    customerIdentityField: "客户身份字段",
    reachField: "触达字段",
    reachFieldValue: "触达字段值",
    reachContent: "触达内容",
    reachData: "触达数据",
    reachStatus: "触达状态",
    processInstanceId: "流程实例ID",
    canvasNodeId: "画布节点ID",
    reachTemplateId: "触达模板ID",
    reachTemplateName: "触达模板名称",
    reachContentId: "触达内容ID",
    reachContentName: "触达内容名称",
    acceptanceStatus: "接受状态",
    completionStatus: "完成状态",
    errorStatus: "出错状态",
    nodeType: "节点类型",
    isTemplateFrequencyLimit: "是否模版频次限制",
    isChannelFrequencyLimit: "是否渠道频次限制",
    isGlobalFrequencyLimit: "是否全局频次限制",
    message: "消息",
    reminder: {
      channel: "请选择渠道",
      operationTime: "请选择操作时间",
      campaignId: "请输入活动编号",
      campaignName: "请选择活动名称",
      campaignCategory: "请选择活动分类",
      canvasId: "请输入画布编号",
      canvasName: "请选择画布名称",
      executionStatus: "请选择执行状态",
      customerId: "请输入客户ID",
      customerIdentityField: "请选择客户身份字段",
      reachField: "请选择触达字段",
      reachFieldValue: "请输入触达字段值",
      reachContent: "请输入触达内容",
      reachData: "请选择触达数据",
      reachStatus: "请选择触达状态",
      processInstanceId: "流程实例ID",
      canvasNodeId: "请输入画布节点ID",
      reachTemplateId: "请输入触达模板ID",
      reachTemplateName: "请输入触达模板名称",
      reachContentId: "请输入触达内容ID",
      reachContentName: "请输入触达内容名称",
      acceptanceStatus: "查看接受状态",
      completionStatus: "查看完成状态",
      errorStatus: "查看出错状态",
      nodeType: "请选择节点类型",
      isTemplateFrequencyLimit: "请输入模板频次限制",
      isChannelFrequencyLimit: "请输入渠道频次限制",
      isGlobalFrequencyLimit: "请输入全局频次限制",
    }
},
};
