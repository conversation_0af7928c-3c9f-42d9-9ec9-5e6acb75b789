<template>
  <template v-if="visible">
    <a-modal v-model:visible="visible" :title="t('systemSetting.behaviorEvent.sendEvent')" @cancel="visible = false"
      @before-ok="send">
      <a-form ref="dataFormRef" :model="dataForm">
        <a-form-item field="code" :label="t('systemSetting.behaviorEvent.activity')" label-col-flex="70px">
          <a-input v-model="dataForm.code" :placeholder="t('systemSetting.behaviorEvent.activityCode')" />
        </a-form-item>
      </a-form>
    </a-modal>
  </template>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";

const {
      proxy: { t }
    } = getCurrentInstance()
const visible = ref(false);
const dataForm = ref({});
const dataFormRef = ref({});

const send = async (done) => {
};

const show = () => {
  visible.value = true;
};

defineExpose({ show });
</script>

<style lang="less" scoped></style>
