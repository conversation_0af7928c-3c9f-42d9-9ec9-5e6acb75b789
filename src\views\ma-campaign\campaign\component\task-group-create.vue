<template>
  <div class="task-group-create">
    <span>
      <div :class="{
        actived: actived,
      }" class="group create-group">
        <div class="create-group-title" v-if="!actived" @click="handleCreateGroup">
          <span class="iconfont icon-create"></span>
          <span>创建分组</span>
        </div>

        <div v-if="actived" class="create-group-body">
          <div class="create-grou-item">
            <a-input placeholder="分组名称" v-model="createGroup.name"></a-input>
          </div>
          <div class="create-grou-item">
            <a-textarea auto-size type="textarea" placeholder="请输入分组描述信息" v-model="createGroup.summary"></a-textarea>
          </div>
          <div class="create-grou-item">
            <a-button type="primary" @click="saveGroup">创建</a-button>
            <a-button type="outline" @click="actived = false">取消</a-button>
          </div>
        </div>
      </div>
    </span>
  </div>
</template>

<script>
import {
  saveGroup,
} from "@/api/group";
export default {
  props: {
    groupChange: {
      type: Function,
      default: null,
    },
  },
  setup() {
    const initGroup = () => {
      return {
        name: null,
        summary: null,
      }
    }

    return { initGroup };
  },
  data() {
    return {
      actived: false,
      createGroup: {
        name: null,
        summary: null,
      },
    };
  },
  methods: {
    handleCreateGroup() {
      this.actived = true;
    },
    async saveGroup() {
      await saveGroup(this.createGroup);
      await this.groupChange();
      this.createGroup = this.initGroup()
      this.actived = false;
    },
  },
};
</script>

<style lang="less" scoped>
.create-group {
  width: 260px;
  height: 35px;
  font-size: 14px;
  cursor: pointer;
  color: #999999;
  box-sizing: border-box;

  &.actived {
    height: 200px;
    border: 1px dashed #ffffff;
  }

  .create-group-title {
    height: 35px;
    display: flex;
    padding: 0 5px;
    color: #aaaaaa;
    align-items: center;
    border-radius: 4px 4px 0 0;
    background: rgba(0, 0, 0, 0.02);

    &:hover {
      color: #000000;
    }
  }

  .create-group-body {
    border: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
    padding: 5px;

    .create-grou-item {
      margin-bottom: 10px;

      >button {
        margin-right: 4px;
      }
    }
  }
}
</style>
