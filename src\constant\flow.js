import i18n from "../locale";

export const flowNodeStatus = [
    {
        // text: "接收",
        text: i18n.global.t("global.flow.receive"),
        value: "INCOME",
    },
    {
        // text: "成功",
        text: i18n.global.t("global.flow.success"),
        value: "OUTGOING",
    },
    {
        // text: "失败",
        text: i18n.global.t("global.flow.failure"),
        value: "ERROR",
    },
];

export const joinTypes = [
    {
        // text: "身份字段关联",
        text: i18n.global.t("global.flow.identityFieldAssociation"),
        value: "IDENTITY",
    },
    // {
    // text: "查询条件关联", // text: i18n.global.t("global.flow.queryConditionAssociation"),
    //     value: "QUERY",
    // },
]
