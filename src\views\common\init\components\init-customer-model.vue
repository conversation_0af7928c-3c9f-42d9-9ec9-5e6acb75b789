<template>
  <a-alert type="warning"> 数据仓库配置 </a-alert>
  <a-form ref="formRef" :model="formData">
    <a-form-item field="type" label="数据仓库类型" :rules="[{ required: true, message: '请选择数据仓库类型' }]">
      <a-select v-model="formData.type" type="round">
        <a-option value="lianwei_cdp"> CDP </a-option>
      </a-select>
    </a-form-item>
    <template v-if="formData.type == 'lianwei_cdp'">
      <a-form-item field="collectionId" label="数据集" :rules="[{ required: true, message: '请输入数据集Id' }]">
        <a-select v-model="formData.collectionId" type="round" @change="changeCollection()">
          <a-option v-for="item of dataDwhList" :key="item.collectionId" :value="item.id">{{ item.name }} - {{ item.aliasName }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="customerModelId" label="客户模型" :rules="[{ required: true, message: '请输入客户模型Id' }]">
        <a-select v-model="formData.customerModelId" type="round" @change="changeModel()">
          <a-option v-for="item of dataDwhModelList" :key="item.id" :value="item.id">{{ item.name }} - {{ item.aliasName }}</a-option>
        </a-select>
      </a-form-item>
    </template>
    <!-- <a-form-item field="own" label="自建数据模型">
      <a-switch v-model="formData.own" type="round">
        <template #checked>是</template>
        <template #unchecked>否</template>
      </a-switch>
    </a-form-item>
    <a-form-item field="customerModel" label="客户模型">
      <a-textarea v-model="customerModel" placeholder="请输入客户模型" />
    </a-form-item> -->
  </a-form>
</template>

<script setup>
import { ref,onMounted } from "vue";
import { dwhList, dwhModelList } from "@/api/marketing_center";

const props = defineProps(["setting"]);
const formRef = ref(null);

const formData = ref(props.setting);
const customerModel = ref("");
const dataDwhList = ref([]);
const dataDwhModelList = ref([]);

const changeCollection = async () => {
  if (formData.value.collectionId) {
    const dwh = dataDwhList.value.find((it) => it.id === formData.value.collectionId);
    formData.value.collectionName = dwh.name;
    formData.value.customerModelId = null;
    formData.value.customerModelName = null
    dataDwhModelList.value = await dwhModelList(formData.value.collectionId);
  }
};

const changeModel = async () => {
  if (formData.value.customerModelId) {
    const dwhModel = dataDwhModelList.value.find((it) => it.id === formData.value.customerModelId);
    formData.value.customerModelName = dwhModel.name;
  }
};

const commit = async (entity) => {
  if (await formRef.value.validate()) {
    return false;
  }
  entity.value.setting.dataWareHouseSetting = formData.value;
  if (formData.value.own) {
    entity.value.setting.dataWareHouseSetting.customerModel = JSON.parse(customerModel.value);
  } else {
    entity.value.setting.dataWareHouseSetting.customerModel = { own: false };
  }
  entity.value.setting.dataWareHouseSetting.customerModel.dwhType = formData.value.type;
  entity.value.setting.dataWareHouseSetting.customerModel.id = formData.value.customerModelId;
  return true;
};

onMounted(async () => {
  formData.value.own = false;
  dataDwhList.value = await dwhList();
  if (formData.value.collectionId) {
    dataDwhModelList.value = await dwhModelList(formData.value.collectionId);
  }
});

defineExpose({
  commit
})
</script>

<style lang="less" scoped>
.arco-alert {
  margin: 10px;
}
</style>
