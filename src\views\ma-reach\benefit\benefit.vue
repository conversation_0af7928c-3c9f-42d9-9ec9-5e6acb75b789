<template>
  <module edit>
    <template #main>
      <div class="template-body">
        <a-form ref="formRef" layout="vertical" class="general-form" :model="entity">
          <a-space direction="vertical" :size="16">
            <a-card class="general-card">
              <a-row :gutter="80">
                <a-col :span="24">
                  <a-form-item label="模板名称" field="entity.name">
                    <a-input v-model="entity.name" placeholder="请输入模板名称">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="渠道" field="entity.channelType">
                    <div class="m-t-p m-t-sl">
                      <a-select v-model="entity.channelId" placeholder="请选择渠道" popup-container=".m-t-sl">
                        <a-option v-for="item in channelList" :value="item.id" :label="item.name" />
                      </a-select>
                    </div>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="选择权益" field="entity.templateCode">
                    <a-select v-model="entity.templateId" placeholder="请选择权益">
                      <a-option v-for="item in templateList" :value="item.id" :label="item.name" />
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="权益单价" field="entity.budget">
                    <a-input-number v-model="entity.budget" placeholder="请输入权益单价" :min="0">
                    </a-input-number>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="描述信息" field="entity.summary">
                    <a-input v-model="entity.summary" placeholder="请输入描述信息">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="状态" field="entity.status">
                    <a-switch v-model="entity.status" type="round">
                      <template #checked>
                        启用
                      </template>
                      <template #unchecked>
                        禁用
                      </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-card>
          </a-space>
        </a-form>

      </div>
    </template>

    <template #action>
      <a-space>
        <a-button v-if="quit" type="outline" @click="quit"> 返回</a-button>
        <a-button v-if="save" type="primary" @click="save">{{ isEdit ? "保存" : "提交" }}
        </a-button>
        <a-button v-if="isEdit" type="primary" @click="simulateSend"> 模拟发送</a-button>
      </a-space>
    </template>
  </module>
</template>

<script>
import { ref, provide } from "vue";
import { useRouter, useRoute } from "vue-router";
import { findBenefitItem, saveBenefitInfo, findChannelList } from "@/api/benefit";
import { Message } from "@arco-design/web-vue";
import { findCustomerModel } from "../../../api/system";

export default {
  setup() {
    const route = useRoute();
    const router = useRouter();
    const module = ref({
      entityIdField: "id",
      mainPath: "/reach/benefit",
      breadcrumb: [
        {
          name: "营销云权益",
          path: "/reach/benefit"
        },
        {
          name: "编辑权益模板"
        }
      ],
    });

    let queryValue = route.query.id;
    let isEdit = !!queryValue;

    const entity = ref({
      status: false,
      contentTemplateUrlMapping: {},
      type: 'benefit',
      purpose: "benefit",
      templateMappings: null
    });
    const channelList = ref([]);
    const templateList = ref([]);

    const simulateDlg = ref(null)

    const bindData = async () => {
      if (isEdit) {
        entity.value = await findBenefitItem(queryValue)
      }
      channelList.value = await findChannelList({ expression: `type eq benefit` })
      if (!entity.value.channelId && channelList.value.length == 1) {
        entity.value.channelId = channelList.value[0].id
      }
    };
    const quit = async () => {
      await router.push({ path: module.value.mainPath });
    }
    const formData = ref({});
    const save = async () => {
      const res = await saveBenefitInfo(entity.value);
      queryValue = res.id
      isEdit = !!queryValue;
      Message.success('提交成功！');
      await quit()
    };


    const simulateSend = async () => {
      simulateDlg.value.handleClick(entity.value.id)
    }

    // 获取字段数据
    const userIdData = ref({})
    const initFields = (data, parent) => {
      if (!Array.isArray(data)) return;
      data.forEach((i) => {
        if (parent) {
          i.path = `${parent.path}.${i.name}`;
        } else {
          i.path = i.name;
        }
        i.name = `${i.name}      「${i.aliasName}」`

        if (i.fields && i.fields.length > 0) {
          return initFields(i.fields, i);
        }
      });
    };

    findCustomerModel('/customer-model/customer').then((data) => {
      if (data) {
        userIdData.value = data;
        initFields(userIdData.value.fields, "");
      }
    })

    const setup = {
      module,
      isEdit,
      entity,
      channelList,
      formData,
      bindData,
      save,
      quit,
      simulateSend,
      templateList,
      simulateDlg,
      userIdData
    };
    provide("edit", setup);
    return setup;
  },
};
</script>

<style lang="less" scoped>
.template-body {
  display: flex;
  background-color: #ffffff;
}

::v-deep(.general-card) {
  min-height: calc(100vh - 160px);
}

.m-t-p {
  position: relative;
  width: 100%;
}
</style>
