import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import RestrictNode from "./node.vue";
import RestrictPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "restrict",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<RestrictNode />`,
      components: {
        RestrictNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("RestrictNode", nodeData.node, true);
};

const Restrict = {
  type: "restrict",
  name: "限流",
  shape: "RestrictNode",
  iconClass: "icon-timing",
  color: "#105a63",
  themebg: "#f9f5ff",
  registerNode,
  help: Help,
  pannel: RestrictPannel,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Restrict;
