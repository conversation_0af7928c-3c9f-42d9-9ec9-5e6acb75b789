import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findAnalysisPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/analysis`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function findAnalysisList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/analysis/list`, {
    params,
  });
}

export function getAnalysis(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/analysis/${id}`);
}

export function saveAnalysis(info?: any) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/analysis`, info);
}

export function deleteAnalysis(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/analysis/${id}`);
}

export function findMetricPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/metric`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function findMetricList(params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/metric/list`, {
    params,
  });
}

export function getMetric(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/metric/${id}`);
}

export function createMetric(info?: any) {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/metric`, info);
}

export function modifyMetric(info?: any) {
  return axios.put(`/api/ma-manage/${tenantId}/${buCode}/metric`, info);
}

export function deleteMetric(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/metric/${id}`);
}

export function findAbtestPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/abtest`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function getAbtest(id: string) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/abtest/${id}`);
}

export function deleteAbtest(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/abtest/${id}`);
}

export function getChangeList(params?: any) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/audience-analysis-template/list`,
    {
      params,
    }
  );
}

export function getAnalysisAudience(
  type?: any,
  useType?: any,
  audienceId?: any
) {
  return axios
    .get(
      `/api/ma-manage/${tenantId}/${buCode}/audience-analysis-template/type/${type}/${useType}/${audienceId}`
    )
    .then((res) => {
      return axios.post(
        `/api/ma-manage/${tenantId}/${buCode}/visualize/render`,
        res
      );
    });
}

export function chartRender(request: any) {
  return axios.post(
    `/api/ma-manage/${tenantId}/${buCode}/charts/render`,
    request
  );
}
