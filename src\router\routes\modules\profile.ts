export default {
  path: "profile",
  name: "profile",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.dashboard",
    requiresAuth: true,
    icon: "icon-dashboard",
    order: 10,
    hideInMenu: true,
    parentMenu:true,
  },
  children: [
    {
      path: "profile",
      name: "profile",
      component: () => import("@/views/common/user-info/index.vue"),
      meta: {
        hideInMenu: true,
        locale: "menu.dashboard.workplace",
        requiresAuth: true,
        roles: ["*"],
      },
    },
  ],
};
