<template>
  <template v-if="showGroupVisible">
    <a-modal v-model:visible="showGroupVisible" title="AI生成" :hide-cancel="true" @cancel="cancel" @ok="cancel">
      <a-form ref="dataFormRef" :model="dataForm">
        <a-form-item field="item" label="items" :rules="[{ required: true, message: '请输入item' }]" label-col-flex="70px">
          <a-input v-model="dataForm.item" placeholder="items" />
        </a-form-item>
        <a-form-item v-for="(attr, index) of attrs" required :key="index" :label="`Attr-${index+1}`"
       >
          <a-input v-model="attr.value" placeholder="please enter your post..." />
          <a-button v-if="index === 0" @click="handleAdd(index)" :style="{ marginLeft: '10px' }"> 
            <template #icon>
              <icon-plus />
            </template>
          </a-button>
          <a-button v-else @click="handleDelete(index)" :style="{ marginLeft: '10px' }"> 
            <template #icon>
              <icon-minus />
            </template>
          </a-button>
        </a-form-item>
        <a-button style="margin-bottom: 10px;" @click="generate">生 成</a-button>
        <a-form-item v-if="isGenerate" field="name" label="生成结果" label-col-flex="70px">
          <a-textarea style="height: 100px;" v-model="result" placeholder="attr" />
        </a-form-item>
      </a-form>
    </a-modal>
  </template>
</template>

<script setup>
import { ref } from "vue";
import { aiGenerateInfo } from "@/api/flow";
import { modalCommit } from "@/utils/modal";

const showGroupVisible = ref(false);
const isGenerate = ref(false);
const result = ref('');
const dataForm = ref({
  item: '',
  attr: '',
  style: "",
  base: "",
  temperature: 0.7
});
const attrs = ref([{ value: '' }])
const dataFormRef = ref({});

const generate = async () => {
  const attr = []
  attrs.value.map(item => {
    if (item.value) {
      attr.push(item.value)
    }
  })
  dataForm.value.attr = attr.join(',')
  // console.log(dataForm.value)
  const res = await aiGenerateInfo(dataForm.value)
  result.value = res.msg
  isGenerate.value = true
}

const show = () => {
  showGroupVisible.value = true;
  isGenerate.value = false
};

const handleAdd = () => {
  attrs.value.push({
      value: ''
    })
  };
const handleDelete = (index) => {
  attrs.value.splice(index, 1)
}
const cancel = () => {
  dataForm.value = {
    item: '',
    attr: [{ value: '' }],
    style: "",
    base: "",
    temperature: 0.7
  }
  attrs.value = [{ value: '' }]
  isGenerate.value = false
}
defineExpose({ show });
</script>

<style lang="less" scoped></style>
