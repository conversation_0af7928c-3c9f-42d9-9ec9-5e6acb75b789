export default {
  path: "system",
  name: "system",
  component: () => import("../base.vue"),
  meta: {
    locale: "menu.system",
    requiresAuth: true,
    icon: "icon-settings",
    order: 100,
    parentMenu: true,
  },
  children: [
    {
      path: "setting",
      name: "setting",
      component: () => import("@/views/ma-system/setting/main.vue"),
      meta: {
        type: 'menu',
        locale: "menu.system",
        requiresAuth: true,
        roles: ["ma_menu.system"]
      }
    },
    {
      path: "customer-model",
      name: "CustomerModel",
      component: () => import("@/views/ma-system/model/customer/edit.vue"),
      meta: {
        locale: "customer-model",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "behavior-model",
      name: "BehaviorModel",
      component: () => import("@/views/ma-system/model/behavior/main.vue"),
      meta: {
        locale: "behavior-model",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "behavior-model/edit",
      name: "BehaviorModelEdit",
      component: () => import("@/views/ma-system/model/behavior/edit.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "behavior-event",
      name: "BehaviorEvent",
      component: () => import("@/views/ma-system/event/main.vue"),
      meta: {
        locale: "behavior-model",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "behavior-event/edit",
      name: "BehaviorEventEdit",
      component: () => import("@/views/ma-system/event/edit.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "system-budget",
      name: "SystemBudget",
      component: () => import("@/views/ma-system/budget/main.vue"),
      meta: {
        locale: "SystemBudget",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "campaign-category",
      name: "CampaignCategory",
      component: () => import("@/views/ma-system/category/main.vue"),
      meta: {
        locale: "CampaignCategory",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "center",
      name: "Center",
      component: () => import("@/views/ma-system/center/edit.vue"),
      meta: {
        locale: "center",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "node-config",
      name: "FlowNodeList",
      component: () => import("@/views/ma-system/node/main.vue"),
      meta: {
        locale: "FlowNode",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "node-config/edit",
      name: "FlowNodeEdit",
      component: () => import("@/views/ma-system/node/edit.vue"),
      meta: {
        locale: "FlowNode",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "metric",
      name: "Metric",
      component: () => import("@/views/ma-system/metric/main.vue"),
      meta: {
        locale: "metric",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "metric/edit",
      name: "MetricEdit",
      component: () => import("@/views/ma-system/metric/edit.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "reach",
      name: "reachChannelList",
      component: () => import("@/views/ma-system/channel/reach.vue"),
      meta: {
        locale: "触达渠道管理",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "reach/sms",
      name: "smsChannelEdit",
      component: () => import("@/views/ma-system/channel/component/sms.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "reach/wechat",
      name: "wechatChannelEdit",
      component: () => import("@/views/ma-system/channel/component/wechat.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "reach/email",
      name: "emailChannelEdit",
      component: () => import("@/views/ma-system/channel/component/email.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "benefit",
      name: "benefitChannelList",
      component: () => import("@/views/ma-system/channel/benefit.vue"),
      meta: {
        locale: "权益渠道管理",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "benefit/coupon",
      name: "couponChannelEdit",
      component: () => import("@/views/ma-system/channel/component/coupon.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "benefit/points",
      name: "pointsChannelEdit",
      component: () => import("@/views/ma-system/channel/component/points.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    // {
    //   path: "benefit/benefit",
    //   name: "benefitChannelEdit",
    //   component: () => import("@/views/ma-system/channel/component/benefit.vue"),
    //   meta: {
    //     hideInMenu: true,
    //     requiresAuth: true,
    //     roles: ["*"],
    //   },
    // },
    {
      path: "csvSetting",
      name: "csvSetting",
      component: () => import("@/views/ma-system/file/csv/csvSetting.vue"),
      meta: {
        locale: "csv文件设置",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "channel-limited",
      name: "channelLimited",
      component: () => import("@/views/ma-system/channel/limited.vue"),
      meta: {
        locale: "沟通限制",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "rfm",
      name: "rfm",
      component: () => import("@/views/ma-system/analysis/rfm/main.vue"),
      meta: {
        locale: "rfm",
        requiresAuth: true,
        hideInMenu: true,
        roles: ["*"]
      }
    },
    {
      path: "/system/rfm/edit",
      name: "rfmEdit",
      component: () => import("@/views/ma-system/analysis/rfm/edit.vue"),
      meta: {
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "node-category",
      name: "nodeCategoryList",
      component: () => import("@/views/ma-system/channel/node-category.vue"),
      meta: {
        locale: "触点分类管理(能力类型)",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "silence-rule",
      name: "silenceRuleList",
      component: () => import("@/views/ma-system/silence-rule/main.vue"),
      meta: {
        locale: "勿扰规则",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    },
    {
      path: "all-channel-limit",
      name: "allChannelLimit",
      component: () =>
        import("@/views/ma-system/channel/all-Channel-limit.vue"),
      meta: {
        locale: "全渠道限制",
        hideInMenu: true,
        requiresAuth: true,
        roles: ["*"]
      }
    }
  ]
};
