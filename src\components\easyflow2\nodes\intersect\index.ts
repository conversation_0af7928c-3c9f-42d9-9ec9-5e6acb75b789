import { Graph } from "@antv/x6";
import { getPorts } from "../../components/node";
import IntersectNode from "./node.vue";
import IntersectPannel from "./pannel.vue";

const nodeData = {
  type: "intersect",
  node: {
    inherit: "vue-shape",
    x: 0,
    y: 0,
    width: 232,
    height: 104,
    component: {
      template: `<IntersectNode />`,
      components: {
        IntersectNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("IntersectNode", nodeData.node, true);
};

const Intersect = {
  type: "intersect",
  name: "交集",
  shape: "IntersectNode",
  iconClass: "icon-cross",
  registerNode: registerNode,
  pannel: IntersectPannel,
  skippable: true,
};

export default Intersect;
