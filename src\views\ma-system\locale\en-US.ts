export default {
  "menu.system": "System",
  "menu.behavior": "Behavior",
  "menu.system.customer": "Customer Model",
  "menu.system.behavior": "Behavior Model",

  systemSetting: {
    title: {
      basicSettings: "Basic Settings",
      customerModel: "Customer Model",
      touchpointManagement: "Touchpoint Management",
    },
    basic: {
      basicSettings: "Basic Settings",
      customerModel: "Customer Model",
      behaviorModel: "Behavior Model",
      behaviorEvent: "Behavior Event",
      activityCategory: "Campaign Category",
      channelLimitation: "Channel Limitation",
      marketingCenterConfig: "Marketing Center",
    },
    category: {
      deleteCategory: "Delete Campaign Category",
    },
    touchpoint: {
      communicationChannel: "Communicate Channels",
      benefitChannel: "Benefit Channels",
      canvasNodeManagement: "Canvas Node",
      touchpointCategoryManagement: "Touchpoint Category",
      silenceRule: "Silence Rule",
    },
    basicSettings: {
      customerFieldSetting: "customerFieldSetting",
      primaryKey: "primaryKey",
      id: "id",
      name: "name",
      type: "type",
      isNull: "isNull",
      isArray: "isArray",
      identification: "identification",
      showingByList: "showingByList",
      useForQuery: "useForQuery",
      fieldUsage: "fieldUsage",
      defaultValue: "defaultValue",
      synchronousText: "synchronousText",
      saveText: "saveText",
      someFieldIsNull: "someFieldIsNull",
      fieldExisted: "fieldExisted",
      idTips: "idTips",
      notAllowNull: "notAllowNull",
      enterId: "enterId",
      enterName: "enterName",
      selectType: "selectType",
      yes: "Y",
      no: "N",
      string: "string",
      integer: "integer",
      long: "long",
      double: "double",
      date: "date",
      boolean: "boolean",
      nested: "nested",
      selectUseForQuery: "selectUseForQuery",
      eq: "eq",
      like: "like",
      lt: "lt",
      gt: "gt",
      selectFieldUsage: "selectFieldUsage",
      enterDefaultValue: "enterDefaultValue",
      realName: "realName",
      nickname: "nickname",
      gender: "gender",
      mobile: "mobile",
      wechat: "wechat",
      mail: "mail",
      address: "address",
      birthday: "birthday",
      register_campaign: "register_campaign",
      age: "age",
      register_time: "register_time",
    },

    behaviorModel: {
      behaviorModelSetting: "behaviorModelSetting",
      modelName: "modelName",
      primaryKey: "primaryKey",
      dataSync: "dataSync",
      enterModelName: "enterModelName",
      deleteError: "deleteError",
      deleteModel: "deleteModel",
      deleteConfirm: "deleteConfirm",
      changeBehavioEvent: "changeBehavioEvent",
      changeBehavioEventSuffix: "changeBehavioEventSuffix`",
      notAllowNull: "notAllowNull",
      modelNameLimit: "modelNameLimit",
      modelAliasName: "modelAliasName",
      enterMmodelAliasName: "enterMmodelAliasName",
      behaviorIdentifyField: "behaviorIdentifyField",
      fieldName: "fieldName",
      enterFieldName: "enterFieldName",
      showName: "showName",
      enterShowName: "enterShowName",
      fieldType: "fieldType",
      selectFieldType: "selectFieldType",
      fieldDesc: "fieldDesc",
      enterFieldDesc: "enterFieldDesc",
      privacyData: "privacyData",
      fieldNameTips: "fieldNameTips",
      options: "options",
      value: "value",
      description: "description",
      extras: "extras",
      key: "key",
    },

    behaviorEvent: {
      editbehaviorEvent: "editbehaviorEvent",
      id: "id",
      name: "name",
      modelId: "modelId",
      campaignId: "campaignId",
      status: "status",
      summary: "summary",
      createdTime: "createdTime",
      sendEvent: "sendEvent",
      activity: "activity",
      activityCode: "activityCode",
      listenerId: "listenerId",
      listenerType: "listenerType",
      listenerFlowId: "listenerFlowId",
      listenerStartTime: "listenerStartTime",
      listenerEndTime: "listenerEndTime",
    },

    activityCategory: {
      id: "id",
      enterId: "enterId",
      name: "name",
      enterName: "enterName",
      summary: "summary",
    },

    channelLimitation: {
      communicateLimit: "communicateLimit",
      name: "name",
      year: "year",
      month: "month",
      day: "day",
      allChannel: "allChannel",
    },

    marketingCenterConfig: {
      ossSetting: "ossSetting",
      type: "type",
      noDataSource: "noDataSource",
      endpoint: "endpoint",
      enterEndpoint: "enterEndpoint",
      accessKey: "accessKey",
      enterAccessKey: "enterAccessKey",
      secretKey: "secretKey",
      enterSecretKey: "enterSecretKey",
      bucketName: "bucketName",
      enterBucketName: "enterBucketName",
      basePath: "basePath",
      flowSetting: "flowSetting",
      flowTemplateSyncPublishId: "flowTemplateSyncPublishId",
      selectFlowTemplateSyncPublishId: "selectFlowTemplateSyncPublishId",
      flowTemplateSyncStartId: "flowTemplateSyncStartId",
      selectFlowTemplateSyncStartId: "selectFlowTemplateSyncStartId",
      customSetting: "customSetting",
      campaignEditableStatus: "campaignEditableStatus",
      manualCampaignCode: "manualCampaignCode",
      aiEnable: "aiEnable",
      DRAFT: "DRAFT",
      COMMITTED: "COMMITTED",
      REJECTED: "REJECTED",
      PAUSED: "PAUSED",
    },

    communicateChannel: {
      communicateManage: "Communicate Manage",
      channelName: "ChannelName",
      channelType: "ChannelType",
      createTime: "CreateTime",
      remark: "Remark",
      sms: "sms",
      email: "email",
      smsManage: "Sms Manage",
      simplifiedSms: "Simplified Sms",
      aliYunSms: "aliYunSms",
      messageType: "MessageType",
      userMobileField: "userMobileField",
      smsContent: {
        QSRQDMC: "Please enter the channel name",
        duanXinLeiXing: "Sms Type",
        QXZDXLX: "Please select SMS type",
        QXZYHSJHZD: "Please select the user's phone number field",
        YHSJHZD: "User phone number field",
        YHZGQDXDWYBSZD:
          "The unique identification field of the user under this channel",
        qingXuanZeZiDuan: "Please select field",
        shenFenZiDuanQianZhui: "Identity field prefix",
        yongYu: "Used for CDP identity field logic",
        QSRSFZDQZ: "Please enter the prefix of the identity field",
        QXZSFKCDZD: "Please select if the fields are reachable",
        YHJSGQDXXBJZD: "The user accepts the message tag field of this channel",
        lengthLimit: "length limit",
        QXZYYBJHYSFJSGQDFSXXDZD:
          "Please select the field to mark whether the member accepts the message sent through this channel",
        beiZhu: "Remark",
        QSRBZXX: "Please enter the remarks",
        ALYDXPZ: "Alibaba Cloud SMS configuration",
        fuWuDiZhi: "Service Address(endpoint)",
        QSRFWDZ: "Please enter the service address",
        duanXinQianMing: "SMS Signature",
        QSRDXQM: "Please enter your SMS signature",
        lianJie: "connection ID(accessKeyId)",
        qingShuRuLianJie: "Please enter the connection",
        lianJieMiYao: "connection keyID(accessKeySecret)",
        QSRLJMY: "Please enter the connection key",
        duanXinQuDao: "SMS channel",
        huoQuZiDuanShuJu: "Retrieve field data",
      },
      limitSetting: {
        gouTongXianZhiPeiZhi: "Communication restriction configuration",
        wuRaoKaiGuan: "Do not disturb the switch",
        kai: "Open",
        guan: "Close",
        wuRaoChuLiLeiXing: "Do not disturb processing type",
        QXZWRCLLX: "Please select the Do Not Disturb processing type",
        kaiShiGouTongShiJian: "Start communication time",
        jieShuGouTongShiJian: "End communication time",
        gouTongPinCiXianZhi: "Communication frequency limit",
        tianJia: "Add",
        QSRZQTS: "Please enter the number of days in the cycle",
        tian: "day",
        QSRZQCS: "Please enter the number of cycles",
        ci: "freq",
        shanChu: "Delete",
        yanShiFaSong: "Delay sending",
        buFaSong: "Do not send",
      },
    },

    benefitChannel: {
      points: "Points",
      coupon: "Coupon",
      benefitManagement: "benefitManagement",
      channelName: "Channel Name",
      channelType: "channelType",
      createTime: "createTime",
      remark: "summary",
      enterChannelName: "Please enter the channel name",
      enterChannelType: "Please enter the channel type",
      summary: "summary",
      enterSummary: "Please enter description information",
      couponBenefitManagement: "couponBenefitManagement",
      pointsenefitManagement: "pointsenefitManagement",
      couponChannel: "Coupon Channel",
      pointsChannel: "Points Channel",
    },

    canvasNodeManagement: {
      canvasNodeManagement: "CanvasNode Management",
      huoDongFenLei: "campaign classification",
      bianHao: "code",
      tuBiao: "icon",
      jieDianMingCheng: "node name",
      leiXing: "type",
      xiTong: "system",
      kuoZhan: "extend",
      fenLei: "classification",
      zhuangTai: "status",
      paiXu: "sort",
      beiZhu: "remark",
      QSRFLMC: "Please enter the category name",
      qingXuanZeZhuangTai: "Please select the status",
      shanChu: "Delete",
      chuDianBianMa: "Contact coding",
      QSRCDBM: "Please enter the contact code",
      chuDianMingCheng: "Contact name",
      QSRCDMC: "Please enter the contact point name",
      keYong: "available",
      tingYong: "Deactivate",
      chuDianShuoMing: "Contact Description",
      QSRCDSM: "Please enter the contact point description",
      xuanZeFenLei: "Category",
      qingShuRuFenLei: "Please enter category",
      qingXuanZeFenLei: "Please select category",
      qingXuanZeTuBiao: "Please select icon",
      xuanZeTuBiao: "select icon",
      beiJingSe: "background color",
      qingShuRuBeiJingSe: "Please enter background color",
      zhuTiSe: "Theme color",
      qingShuRuZhuTiSe: "Please enter the theme color",
      AZCXDDDSXPX: "Sort in ascending order",
      qingShuRuPaiXuZhi: "Please enter the sorting value",
      chuDianPeiZhi: "Contact configuration",
      sheZhiFenLei: "Set classification",
      QXZSZFL: "Please select the category for setting",
      muBanLeiXing: "Template type",
      QSRMBLX: "Please enter the template type",
      weiXinMuBanXiaoXi: "WeChat Template Message",
      weiXinKeFuXiaoXi: "WeChat customer service message Flow",
      qiWeiMuBanXiaoXi: "Enterprise WeChat Template Message",
      duanXin: "SMS",
      caiXin: "MMS",
      youJian: "Email",
      chuDaZiDuan: "Touch Field",
      YHZGQDXDCDZD: "User's reach fields under this channel",
      RGZHDZSZLCDZD: "If the reach field is set in the activity",
      ZYXSYHDZDCDZD: "Prioritize using the touchable fields in the activity",
      qingXuanZeZiDuan: "Please select a field",
      shenFenZiDuanQianZhui:
        "Identity field prefix MA assembly touchdown content",
      HCDZDYQZCSFBSZD:
        "Together with the contact field, it forms the identity identification field. For example, if the contact field for the phone number is MB | 130 *********, please fill in MB here",
      BRSJHCDZDS: "",
      zeZheLiTianXie: "",
      zhuangPeiNeiRong: "Assembly content",
      shiFouZai: "Is it there",
      zhuangPeiChuDaNeiRong: "",
      shi: "Y",
      fou: "N",
      chuanDiKeHuShuJu: "Transmitting customer data",
      SFCDKHSJD: "Whether to transmit customer data to",
      chuanDiNeiRongShuJu: "Transmitting content data",
      SFCDNRSJD: "Whether to transmit content data to FLOW",
      dengDaiFanKuiJieGuo: "Waiting for feedback results",
      SFDDFKJGCZXHXLC:
        "Do you wait for feedback results before executing subsequent processes",
      guanLianLiuCheng: "Related processes",
      zai: "The process defined and deployed in Flow",
      ZDYQBSDLC: "",
      qingXuanZe: "Please select the Flow process Flow process start node",
      liuCheng: "",
      liuChengQiDian: "Starting point of the process",
      liuChengKaiShiJieDian: "",
      piCiShuLiang: "batch quantity",
      DQQMZPCSLS:
        "When the request meets the batch quantity, submit the batch to FLOW",
      QSRPCSL: "Please enter the batch quantity",
      piCiShuaXinShiJian: "Batch refresh time",
      DDDPCSXSJRBMZPCSL:
        "When the batch refresh time still does not meet the batch quantity, it will be directly submitted to FLOW",
      zeZhiJieTiJiaoGei: "",
      QSRPCSXSJ: "Please enter the batch refresh time",
      miao: "Sec",
      pinCiXianZhi: "Frequency limit",
      DQQDSFXYPCXZ: "Does the current channel require frequency restrictions",
      GTXZSBZD: "Communication restriction identification field",
      YHZGQDXXZPCDSBZD:
        "Identification field for users to limit frequency under this channel",
      meiNianCiShu: "Annual frequency",
      YHZGQDXMNXZCDCS: "Users are limited to reaching this channel annually",
      meiYueCiShu: "Monthly frequency",
      YHZGQDXMYXZCDCS:
        "Users are limited to monthly reach through this channel",
      meiTianCiShu: "Daily frequency",
      YHZGQDXMTXZCDCS: "Users are limited to daily reach through this channel",
      xiTongSheZhi: "System settings",
      huaBuJieDianGuanLi: "Canvas Node Management",
      bianJiHuaBuJieDian: "Edit canvas nodes",
      tuBiaoXuanZe: "Icon selection",
      BMBKWZW: "Encoding cannot be in Chinese",
    },

    touchpointCategoryManagement: {
      touchpointCategoryManagement: "Touchpoint Category",
      id: "id",
      name: "name",
      summary: "summary",
      createdTime: "createdTime",
      enterId: "Please enter the code",
      enterName: "Please enter the category name",
      enterSummary: "Please enter a note",
      addCategory: "Add Category",
      editCategory: "Edit Category",
    },

    silenceManagement: {
      name: "name",
      bianMa: "Code",
      mingCheng: "Name",
      beiZhu: "Remarks",
      caoZuo: "Operation",
      wuRaoGuiZe: "Don't disturb the rules",
      xinJian: "New",
      bianJi: "Editor",
      xiTongSheZhi: "System Settings",
      huoDongFenLei: "Activity Classification",
      wuRaoShiDuan: "Time Period",
      QSRHDBZXX: "Please enter activity comment information",
      bianJiWuRaoGuiZe: "Editor's Don't Disturb Rules",
      xinZengWuRaoGuiZe: "Add Do Not Disturb Rule",
      qingShuRuBianMa: "Please enter the code",
      QSRFLMC: "Please enter classification name",
      xinZeng: "New",
      shanChu: "Delete",
    },
  },
};
