import { Graph } from "@antv/x6";

import Start from "./start";
import Filter from "./filter";
import End from "./end";
import Outbound from "./outbound";
import Timer from "./timer";
import Sms from "./sms";
import Email from "./email";
import Wechat from "./wechat";
import Benefit from "./benefit";
import Points from "./points";
import Coupon from "./coupon";
import Wecom from "./wecom";

import AbTest from "./abtest";
import AbTesting from "./abdiverter";
import Intersect from "./intersect";
import Union from "./union";
import Exclusion from "./exclusion";
import Unique from "./unique";
import Sort from "./sort";
import Tag from "./tag";
import Flow from "./flow";
import Event from "./event";
import FlowContent from "./flow-content";
import DynamicReach from "./dynamic-reach";

import EventReceive from "./event-receive";
import AudienceReceive from "./audience";

import ExtendData from "./extend-data";
import Customization from "./customization";
import Restrict from "./restrict";

export interface Node {
  config: object;
  register: Function;
}

export const StencilGroup = [
  {
    title: "基础流程图",
    name: "group1",
  },
  {
    title: "系统设计图",
    name: "group2",
    graphHeight: 250,
    layoutOptions: {
      rowHeight: 70,
    },
  },
];

export const NodeGroups = [
  {
    category: "营销目标",
    color: "#4493f3",
    themebg: "#eef7ff",
    nodes: [EventReceive, AudienceReceive],
  },
  {
    category: "营销触达",
    color: "#4493f3",
    themebg: "#eef7ff",
    nodes: [DynamicReach, Sms, Wechat, Email, Benefit, Points, Coupon, Wecom],
  },
  {
    category: "营销功能",
    color: "#4493f3",
    themebg: "#eef7ff",
    nodes: [Tag, Flow, ExtendData, Customization],
  },
  {
    category: "Flow",
    color: "#4493f3",
    themebg: "#eef7ff",
    nodes: [FlowContent],
  },
  {
    category: "逻辑操作",
    color: "#ffa347",
    themebg: "#fef6e8",
    nodes: [
      Intersect,
      Union,
      Exclusion,
      Filter,
      Unique,
      AbTest,
      AbTesting,
      Sort,
    ],
  },
  // {
  //   category: "定时延时",
  //   color: "#ad7fff",
  //   themebg: "#f5f0ff",
  //   nodes: [
  //   ]
  // },
  {
    category: "流程控制",
    color: "#ffa347",
    themebg: "#fef6e8",
    nodes: [
      Start,
      Timer,
      Restrict,
      // Event,
      End,
      // Outbound
    ],
  },
];

export const Nodes: any = NodeGroups.flatMap((it) => it.nodes);

export const getPannel = (type: string) => {
  for (let index = 0; index < Nodes.length; index += 1) {
    const node = Nodes[index];
    if (node.type === type) {
      return node.pannel;
    }
  }
};

export const getHelp = (type: string) => {
  for (let index = 0; index < Nodes.length; index += 1) {
    const node = Nodes[index];
    if (node.type === type) {
      return node.help;
    }
  }
};
