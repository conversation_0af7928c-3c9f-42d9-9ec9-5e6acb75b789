<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { getContent } from "@/api/flow-content";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  props: ['nodeItem'],
  setup(props) {
    const getBrief = async (data) => {
      const brief = [];

      return brief;
    };
    const item = props?.nodeItem || JSON.parse(localStorage.getItem('nodeItem'))
    const config = {
      title: item?.name || "flow",
      summary: item?.name || "动态触达节点",
      iconClass: item?.icon || "icon-start",
      nodeClass: "easyflow-node-event-receive",
      headerColor: item?.themeColor || "#39bcc5",
      headerBgColor: item?.themeColor || "#39bcc5",
      background: item?.background || "#f2fcfa",
      getBrief,
    };

    provide("node", config);
    return config;
  },
  data() {
    return {
      monitor: false,
    };
  },
};
</script>
