import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import { Notification } from "@arco-design/web-vue";
import { useBussinessUnitStore } from "@/store";
import { getToken, clearToken } from "@/utils/auth";

import i18n from "../locale";

export interface HttpResponse<T = unknown> {
  status: number;
  msg: string;
  code: number;
  data: T;
}

if (import.meta.env.VITE_API_BASE_URL) {
  axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;
}

axios.interceptors.response.use(
  (response: AxiosResponse<HttpResponse>) => {
    const res = response.data;
    return res;
  },
  (error) => {
    const { status } = error.response;
    // const current_user=window.easyweb.session.get('current_user')
    // let title = "系统提示";
    let title = i18n.global.t("global.tips.error.permission");
    let message = "";
    let hasError = false;

    if (status >= 400 && status < 500) {
      message = error.response.data.message;
      if (status === 401) {
        // title = "权限异常";
        title = i18n.global.t("global.tips.error.permissionException");
        // message = "登录超时或没有权限，请重新登录或申请权限。";
        message = i18n.global.t("global.tips.error.loginTimeoutOrNoPermission");
        // window.location.href =
        //   window.easyweb.loginPage || current_user.loginPage;
        window.location.replace(window.location.origin);
        clearToken();
      }
      if (status === 403) {
        // title = "资源异常";
        title = i18n.global.t("global.tips.error.resourceException");
      }
      if (status === 417) {
        // title = "系统异常";
        title = i18n.global.t("global.tips.error.systemException");
      }
      if (error.response.data.code === "1005") {
        const useBUStore = useBussinessUnitStore();
        useBUStore.setMarketingCenter(null);
        window.location.href = "#/init";
        return Promise.reject(error);
      }
    } else {
      // title = "系统异常";
      title = i18n.global.t("global.tips.error.systemException");
      // message = "网络连接错误或服务器正忙，请稍候重试...";
      message = i18n.global.t("global.tips.error.networkErrorOrServerBusy");
      hasError = true;
    }

    Notification.error({
      title,
      content: message,
    });
    return Promise.reject(error);
  }
);
