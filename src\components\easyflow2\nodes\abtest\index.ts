import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import AbTestNode from "./node.vue";
import AbTestPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "abtest",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<AbTestNode />`,
      components: {
        AbTestNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("AbTestNode", nodeData.node, true);
};

const AbTest = {
  type: "abtest",
  name: "分流器",
  shape: "AbTestNode",
  iconClass: "icon-ABtestshezhi",
  registerNode: registerNode,
  pannel: AbTestPannel,
  help: Help,
  skippable: false,
  auth: [
    "export_task_record"
  ]
};

export default AbTest;
