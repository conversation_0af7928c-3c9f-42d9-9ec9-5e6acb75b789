<template>
  <popover v-if="isDebug" position="left">
    <div v-if="isDebug" class="flow-node-record">
      <span class="iconfont icon-table"></span>
      <span class="record red">待执行：{{ record.pending }} </span>
      <span class="record">总数：{{ record.total }}</span>
    </div>
    <template #content>
      <div class="btn-group">
        <p><Button @click="viewData">查看数据</Button></p>
        <p>
          <Button
            v-if="tirggerEnabled && record.pending > 0"
            :loading="isLoading"
            @click="stepOverOne"
            >执行一条</Button
          >
        </p>
        <p>
          <Button
            v-if="tirggerEnabled && record.pending > 0"
            :loading="isLoading"
            @click="stepOverAll"
            >执行全部</Button
          >
        </p>
      </div>
    </template>
  </popover>
</template>

<script setup>
import { ref, inject, watch } from "vue";
import { Button, Popover } from "@arco-design/web-vue";
import EventBus from "@/utils/eventbus";

const node = inject("node");
const isDebug = node.isDebug;
const tirggerEnabled = ref(!node.info.notTirgger);
const record = ref(node.record);
const isLoading = ref(false);

// 监听执行
watch(
  () => record.value,
  (newVal, oldVal) => {
    if (newVal && newVal.pending > 0) {
      isLoading.value = false;
    }
  }
);

const viewData = async () => {
  EventBus.eventbus.emit("viewSimData", {
    taskId: node.nodeId.value,
  });
};
const stepOverOne = async () => {
  isLoading.value = true;
  EventBus.eventbus.emit("stepOver", {
    taskId: node.nodeId.value,
    all: false,
  });
};
const stepOverAll = async () => {
  isLoading.value = true;
  EventBus.eventbus.emit("stepOver", {
    taskId: node.nodeId.value,
    all: true,
  });
};
</script>

<style lang="less" scoped>
.flow-node-record {
  width: 100%;
  cursor: pointer;
  &:hover {
    > .iconfont {
      color: #666666;
    }
  }
  > .iconfont {
    color: #aaaaaa;
    transition: all ease 0.3s;
  }

  .btn-group {
    display: block;
  }
  .red {
    color: red;
  }
  .record {
    margin: 0 5px;
  }
}
</style>
