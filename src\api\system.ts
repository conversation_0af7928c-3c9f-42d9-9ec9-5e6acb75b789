import axios from "axios";
import { QueryInfo, Params } from "@/types/api";
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export function findCustomerModel() {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/marketing_center/customer-model`
  );
}

export function saveCustomerModel(info?: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/marketing_center/customer-model`;
  return axios.put(uri, info);
}

export function findBehaviorModelList(query?: QueryInfo) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-model/list`, {
    params: {
      ...query,
    },
  });
}

export function getCustomerModelList(id?: any) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/customer-model/${id}`);
}

export function getBehaviorModel(id: string) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/behavior-model/${id}`;
  return axios.get(uri);
}

export function getBehaviorModelByName(name: string) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/behavior-model/query`;
  return axios.get(uri, {
    params: {
      expression: `name eq ${name}`
    },
  });
}

export function findBehaviorModelPage(query?: QueryInfo, params?: Params) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/behavior-model`;
  return axios.get(uri, {
    params: {
      ...params,
      ...query,
    },
  });
}
export function deleteBehaviorModel(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/behavior-model/${id}`
  );
}

export function saveBehaviorModel(info?: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/behavior-model`;
  return info.id ? axios.put(uri, info) : axios.post(uri, info);
}

export function findBehaviorEventPage(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-event`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function findBehaviorEvent(id: string) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/behavior-event/${id}`;
  return axios.get(uri);
}

export function findBehaviorEventList() {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/behavior-event/list`);
}

export function createBehaviorEvent(info?: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/behavior-event`;
  return axios.post(uri, info);
}

export function modifyBehaviorEvent(info?: any) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/behavior-event`;
  return axios.put(uri, info);
}

export function deleteBehaviorEvent(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/behavior-event/${id}`
  );
}

export function syncCustomerModel() {
  return axios.put(
    `/api/ma-manage/${tenantId}/${buCode}/marketing_center/sync-customer-model`
  );
}

export function findAnalysisRFMModelList(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/rfm_model/data_page`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function getRfmPageList(params: any) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/rfm_model/data_page`,
    params
  );
}

export function getTargetSourceData(collectionId: any) {
  return axios.get(
    `/api/ma-manage/${tenantId}/${buCode}/cdp_operate/${collectionId}/model`
  );
}

export function setTargetSourceData(query: any) {
  return axios[query.id ? "put" : "post"](
    `/api/ma-manage/${tenantId}/${buCode}/rfm_model`,
    query
  );
}

export function getInfoData(id: any) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/rfm_model/${id}`);
}

export function deleteTargetSource(id: string) {
  return axios.delete(`/api/ma-manage/${tenantId}/${buCode}/rfm_model/${id}`);
}

export function setGroupBudgetData(query: any) {
  return axios[query.id ? "put" : "post"](
    `/api/ma-manage/${tenantId}/${buCode}/budget-group`,
    query
  );
}

export function deleteGroupBudget(id: string) {
  return axios.delete(
    `/api/ma-manage/${tenantId}/${buCode}/budget-group/${id}`
  );
}

export function getGroupBudgetList(query?: QueryInfo, params?: Params) {
  return axios.get(`/api/ma-manage/${tenantId}/${buCode}/budget-group`, {
    params: {
      ...params,
      ...query,
    },
  });
}

export function getReferenceList(modelId: string) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/behavior-event/quote/${modelId}`;
  return axios.get(uri);
}

export function getDashboard(annual: string) {
  const uri = `/api/ma-manage/${tenantId}/${buCode}/dashboard/${annual}`;
  return axios.get(uri);
}

export const tb = `${tenantId}_${buCode}`;
