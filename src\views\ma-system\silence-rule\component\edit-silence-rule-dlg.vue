<template>
  <template v-if="visible">
    <a-modal v-model:visible="visible" :title="isEdit ? $t('systemSetting.silenceManagement.bianJiWuRaoGuiZe') : $t('systemSetting.silenceManagement.xinZengWuRaoGuiZe')" @cancel="visible = false"
      @before-ok="saveEntity">
      <a-form ref="dataFormRef" :model="entity">
        <a-form-item field="id" :label="$t('systemSetting.silenceManagement.bianMa')" :disabled="isEdit" :rules="[{ required: true, message: t('systemSetting.silenceManagement.qingShuRuBianMa') }]"
          label-col-flex="70px">
          <a-input v-model="entity.id" :placeholder="$t('systemSetting.silenceManagement.bianMa')" />
        </a-form-item>
        <a-form-item field="name" :label="$t('systemSetting.silenceManagement.mingCheng')" :rules="[{ required: true, message: $t('systemSetting.silenceManagement.QSRFLMC') }]" label-col-flex="70px">
          <a-input v-model="entity.name" :placeholder="$t('systemSetting.silenceManagement.mingCheng')" />
        </a-form-item>
        <a-form-item :label="$t('systemSetting.silenceManagement.wuRaoShiDuan')" label-col-flex="70px">
          <div>
            <a-button type="outline" size="mini" @click="addTimeRange">{{$t('systemSetting.silenceManagement.xinZeng')}}</a-button>
            <div class="time-ranges">
              <template v-for="(range, index) in timeRanges" :key="index">
                <div class="time-range">
                  <a-time-picker v-model="timeRanges[index]" type="time-range" format="HH:mm:ss"
                    value-format="HH:mm:ss" />
                  <a-button class="time-range-del" type="outline" size="mini" @click="delTimeRange(index)">{{$t('systemSetting.silenceManagement.shanChu')}}</a-button>
                </div>
              </template>
            </div>
          </div>
        </a-form-item>
        <a-form-item field="summary" :label="$t('systemSetting.silenceManagement.beiZhu')" label-col-flex="70px">
          <a-textarea v-model="entity.summary" type="textarea" :placeholder="$t('systemSetting.silenceManagement.QSRHDBZXX')" />
        </a-form-item>
      </a-form>
    </a-modal>
  </template>
</template>

<script setup>
import { ref } from "vue";
import { modalCommit } from "@/utils/modal";
import { createSilence, modifySilence } from "@/api/silence";

const visible = ref(false);
const isEdit = ref(false);
const entity = ref({});
const dataFormRef = ref({});
let callback;
const timeRanges = ref([]);

const addTimeRange = () => {
  timeRanges.value.push([]);
}

const delTimeRange = (index) => {
  timeRanges.value.splice(index, 1);
}

const saveEntity = async (done) => {
  modalCommit(dataFormRef, done, async () => {
    entity.value.timeRanges = timeRanges.value.map((it) => { return { start: it[0], end: it[1] } });
    if (isEdit.value) {
      await modifySilence(entity.value);
    } else {
      await createSilence(entity.value);
    }
    callback();
  });
};

const initTimeRanges = () => {
  timeRanges.value = entity.value.timeRanges.map((it) => [it.start, it.end]);
};

const showCreate = () => {
  entity.value = { timeRanges: [] };
  initTimeRanges();
  isEdit.value = false;
  visible.value = true;
  return new Promise((resolve, reject) => {
    callback = resolve;
  });
};

const showEdit = (data) => {
  entity.value = data ? JSON.parse(JSON.stringify(data)) : {};
  initTimeRanges();
  isEdit.value = true;
  visible.value = true;
  return new Promise((resolve, reject) => {
    callback = resolve;
  });
};


defineExpose({ showCreate, showEdit });
</script>

<style lang="less" scoped>
.time-ranges {
  margin: 2px;

  .time-range {
    display: flex;
    margin-top: 5px;

    .time-range-del {
      margin-left: 5px;
    }
  }
}
</style>
