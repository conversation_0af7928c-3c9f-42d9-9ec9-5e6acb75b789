import moment from "moment";

export function formatDate(date: Date, fmt: string) {
  // 获取年份
  if (/(y+)/.test(fmt)) {
    // 把数字变成字符串
    const dateY = `${date.getFullYear()}`;
    // RegExp.$1 在判断中出现过，且是括号括起来的，所以 RegExp.$1 就是 "yyyy"
    fmt = fmt.replace(RegExp.$1, dateY.substr(4 - RegExp.$1.length));
  }

  // 获取其他
  const o: any = {
    "M+": date.getMonth() + 1,
    "d+": date.getDate(),
    "h+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds(),
  };
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = `${o[k]}`;
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? str : padLeftZero(str)
      );
    }
  }
  return fmt;
}

function padLeftZero(str: string) {
  return `00${str}`.substr(str.length);
}

export function currentDateToString() {
  return moment().format("yyyy-MM-dd HH:mm:ss");
}
export function dateToUTCString(date: Date) {
  return formatDate(date, "yyyy-MM-dd hh:mm:ss");
}

export function stringToDate(date: string) {

}

export function shortDatetime(date: string) {
  if (date && date.length > 19) return date.substring(0, 19).replace("T", " ");
}

export function shortDate(date: string) {
  if (date && date.length > 19) return date.substring(0, 10);
}

export function getDurationString(delay: any) {
  let duration = "P";
  if (delay.day > 0) {
    duration += `${delay.day}D`;
  }
  let time = "";
  if (delay.hour > 0) {
    time += `${delay.hour}H`;
  }
  if (delay.minute > 0) {
    time += `${delay.minute}M`;
  }
  if (delay.second > 0) {
    time += `${delay.second}S`;
  }
  if (time !== "") {
    duration += `T${time}`;
  }
  return duration;
}

export function getDelay(duration: string) {
  let tmp = duration.replace(/(P|T)/g, "");
  const delay = { day: 0, hour: 0, minute: 0, second: 0 };
  let index;
  if ((index = tmp.indexOf("D")) > 0) {
    delay.day = parseInt(tmp.substr(0, index));
    tmp = tmp.substr(index);
  }
  if ((index = tmp.indexOf("H")) > 0) {
    delay.hour = parseInt(tmp.substr(0, index));
    tmp = tmp.substr(index);
  }
  if ((index = tmp.indexOf("M")) > 0) {
    delay.minute = parseInt(tmp.substr(0, index));
    tmp = tmp.substr(index);
  }
  if ((index = tmp.indexOf("S")) > 0) {
    delay.second = parseInt(tmp.substr(0, index));
  }
  return delay;
}
