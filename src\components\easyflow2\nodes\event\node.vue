<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { shortDatetime } from "@/utils/date";
import { findBehaviorEventItem } from "@/api/behavior";
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (data.behaviorId) {
        const behavior = await findBehaviorEventItem(data.behaviorId);
        brief.push({ sort: 0, label: "事件", value: behavior.name });
      }
      brief.push({
        sort: 1,
        label: "开始时间",
        value: shortDatetime(data.eventStartTime),
      });
      brief.push({
        sort: 2,
        label: "结束时间",
        value: shortDatetime(data.eventEndTime),
      });
      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    let item = nodeApi.find(item => { return item.type === 'event' })
    const setup = {
      title: item.name || "事件",
      summary: item.name || "事件节点",
      iconClass: item.icon || "icon-event",
      nodeClass: "easyflow-node-event",
      headerColor: item.themeColor || "#fff000",
      headerBgColor: item.themeColor || "#ff6d69",
      background: item.background || "#fff8f8",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
