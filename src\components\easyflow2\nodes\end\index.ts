import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import EndNode from "./node.vue";
import EndPannel from "./pannel.vue";
import Help from "./help.vue";

const nodeData = {
  type: "end",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<EndNode />`,
      components: {
        EndNode,
      },
    },
    ports: getPorts(true, false, false, false),
  },
};
const registerNode = () => {
  Graph.registerNode("EndNode", nodeData.node, true);
};

const End = {
  type: "end",
  name: "结束",
  shape: "EndNode",
  iconClass: "icon-flow-stop",
  color: "#ffffff",
  themebg: "#ff6d69",
  registerNode: registerNode,
  help: Help,
  pannel: EndPannel,
  skippable: false,
  auth: [
    "export_task_chain_record"
  ]
};


export default End;
