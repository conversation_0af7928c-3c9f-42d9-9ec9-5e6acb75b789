<template>
  <div class="container">
    <div class="left-side">
      <div class="panel">
        <Banner v-model:vm="budgetAnnual" />
        <DataPanel :data-list="dataList" />
      </div>
      <!-- <a-row :gutter="24" style="margin-top: 20px; overflow: hidden">
        <a-col :span="12">
          <div class="panel panel-chart">
            <ChartItem :options="dataList.barOption" height="30vh"></ChartItem>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="panel panel-chart">
            <ChartItem :options="dataList.pieOption" height="30vh"></ChartItem>
          </div>
        </a-col>
      </a-row> -->
      <!-- <a-row :gutter="24" style="margin-top: 20px; overflow: hidden">
        <a-col :span="12">
          <MaBi :id="`${tb}_communication_of_execution_24`" ref="maBiRefLeft" :height="80" :is-get-data="false"
            :is-filter="false" />
        </a-col>
        <a-col :span="12">
          <MaBi :id="`${tb}_campaign_of_execution_30`" ref="maBiRefRight" :height="80" :is-get-data="false"
            :is-filter="false" />
        </a-col>
      </a-row> -->
    </div>
    <div class="right-side">
      <a-grid :cols="24" :row-gap="16">
        <a-grid-item :span="24">
          <div class="panel moduler-wrap">
            <QuickOperation />
            <RecentlyVisited />
          </div>
        </a-grid-item>
        <a-grid-item class="panel" :span="24">
          <div class="panel moduler-wrap">
            <guide />
          </div>
        </a-grid-item>
      </a-grid>
    </div>
  </div>
</template>

<script>
import { ref, watch } from "vue";
import ChartItem from "@/components/chart/index.vue";
import { getDashboard, tb } from "@/api/system";
import MaBi from "@/components/bi/chart/index.vue";
import BudgetList from "@/components/ma/budget-list/index.vue";
import Banner from "./components/banner.vue";
import DataPanel from "./components/data-panel.vue";
import QuickOperation from "./components/quick-operation.vue";
import Guide from "./components/guide.vue";

export default {
  name: "Workplace",
  components: {
    Banner,
    DataPanel,
    QuickOperation,
    Guide
    // ChartItem,
    // MaBi,
    // BudgetList
  },
  setup() {
    const budgetAnnual = ref("");
    const dataList = ref({});
    // watch(
    //   () => budgetAnnual.value,
    //   (newVal) => {
    //     // getBiData();
    //     getDashboard(newVal).then((res) => {
    //       dataList.value = res;

    //       // 柱状图
    //       const barOption = {
    //         title: {
    //           text: dataList.value.bar.name
    //         },
    //         tooltip: {
    //           trigger: "axis",
    //           axisPointer: {
    //             type: "shadow"
    //           }
    //         },
    //         legend: {},
    //         grid: {
    //           left: "3%",
    //           right: "4%",
    //           bottom: "3%",
    //           containLabel: true
    //         },
    //         xAxis: [
    //           {
    //             type: "category",
    //             data: dataList.value.bar.xAxis
    //           }
    //         ],
    //         yAxis: [
    //           {
    //             type: "value"
    //           }
    //         ],
    //         series: [
    //           {
    //             name: dataList.value.bar.series[0].name,
    //             type: "bar",
    //             stack: "Ad",
    //             emphasis: {
    //               focus: "series"
    //             },
    //             data: dataList.value.bar.series[0].data
    //           },
    //           {
    //             name: dataList.value.bar.series[1].name,
    //             type: "bar",
    //             stack: "Ad",
    //             emphasis: {
    //               focus: "series"
    //             },
    //             data: dataList.value.bar.series[1].data
    //           },
    //           {
    //             name: dataList.value.bar.series[2].name,
    //             type: "bar",
    //             stack: "Ad",
    //             emphasis: {
    //               focus: "series"
    //             },
    //             data: dataList.value.bar.series[2].data
    //           }
    //         ]
    //       };
    //       // 饼状图
    //       const pieOption = {
    //         title: {
    //           text: dataList.value.pie.name
    //         },
    //         tooltip: {
    //           trigger: "item"
    //         },
    //         legend: {
    //           top: "0%",
    //           left: "center"
    //         },
    //         series: [
    //           {
    //             name: dataList.value.pie.name,
    //             type: "pie",
    //             radius: ["40%", "70%"],
    //             emphasis: {
    //               focus: "self"
    //             },
    //             label: {
    //               formatter: "{b}: {@2012} ({d}%)"
    //             },
    //             data: dataList.value.pie.data
    //           }
    //         ]
    //       };
    //       dataList.value = {
    //         ...dataList.value,
    //         barOption,
    //         pieOption
    //       };
    //     });
    //   }
    // );

    // 获取BI 数据 暂时没有用处
    const maBiRefLeft = ref("");
    const maBiRefRight = ref("");
    const getBiData = () => {
      maBiRefLeft.value.getItemReport(8);
      maBiRefRight.value.getItemReport(8);
    };
    return {
      budgetAnnual,
      dataList,
      maBiRefLeft,
      maBiRefRight,
      tb,
      getBiData
    };
  }
};
</script>

<style lang="less" scoped>
.container {
  background-color: var(--color-fill-2);
  padding: 16px 20px;
  display: flex;
}

.left-side {
  flex: 1;
}

.right-side {
  width: 280px;
  margin-left: 16px;
}

.panel {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: auto;
}

:deep(.panel-border) {
  margin-bottom: 0;
  border-bottom: 1px solid rgb(var(--gray-2));
}

.moduler-wrap {
  border-radius: 4px;
  background-color: var(--color-bg-2);

  :deep(.text) {
    font-size: 12px;
    text-align: center;
    color: rgb(var(--gray-8));
  }

  :deep(.wrapper) {
    margin-bottom: 8px;
    text-align: center;
    cursor: pointer;

    &:last-child {
      .text {
        margin-bottom: 0;
      }
    }

    &:hover {
      .icon {
        color: rgb(var(--arcoblue-6));
        background-color: #e8f3ff;
      }

      .text {
        color: rgb(var(--arcoblue-6));
      }
    }
  }

  :deep(.icon) {
    display: inline-block;
    width: 32px;
    height: 32px;
    margin-bottom: 4px;
    color: rgb(var(--dark-gray-1));
    line-height: 32px;
    font-size: 16px;
    text-align: center;
    background-color: rgb(var(--gray-1));
    border-radius: 4px;
  }
}

.panel-chart {
  padding: 20px 0 10px 10px;
  overflow: hidden;
}

// responsive
.mobile {
  .container {
    display: block;
  }

  .right-side {
    // display: none;
    width: 100%;
    margin-left: 0;
    margin-top: 16px;
  }
}

:deep(.bi-chart) {
  padding: 0 !important;
}
</style>
