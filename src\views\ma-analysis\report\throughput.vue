<template>
  <module view>
    <template #main>
      <a-form class="filter-form" :model="formModel" auto-label-width label-align="right">
        <a-row :gutter="16">
          <a-col :span="4">
            <a-form-item :label="t('analysis.timeUnit')">
              <a-select v-model="entity.interval" @change="onChangeInterval">
                <a-option value="1d">{{t('analysis.days')}}</a-option>
                <a-option value="1h">{{t('analysis.hours')}}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item :label="t('analysis.timeRange')">
              <a-range-picker v-model="timeRange" show-time value-format="YYYY-MM-DDTHH:mm:ss.000ZZ" />
            </a-form-item>
          </a-col>
          <a-col :flex="'86px'" style="text-align: right; padding-left: 20px">
            <a-space :size="18">
              <a-button type="primary" @click="search"><template #icon><icon-search /></template>{{t('global.button.query')}}</a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
      <a-row class="chart-list" :gutter="16">
        <a-col :span="8">
          <Chart height="310px" class="chart" :option="chartOption1" />
        </a-col>
        <a-col :span="8">
          <Chart height="310px" class="chart" :option="chartOption2" />
        </a-col>
        <a-col :span="8">
          <Chart height="310px" class="chart" :option="chartOption3" />
        </a-col>
        <a-col :span="8">
          <Chart height="310px" class="chart" :option="chartOption4" />
        </a-col>
        <a-col :span="8">
          <Chart height="310px" class="chart" :option="chartOption5" />
        </a-col>
      </a-row>
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance } from 'vue'
import { useRoute } from "vue-router";
import { chartRender } from "@/api/analysis"
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;
export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance()
    const route = useRoute();
    const module = ref({
      entityIdField: "id",
      breadcrumb: [
        {
          // name: "报表分析",
          name: t('analysis.statementAnalysis'),
          path: "/analysis/main",
        },
        {
          name: route.query.title,
        },
      ],
      mainPath: "/analysis/main",
    });
    const interval = ref("1d");
    const timeRange = ref(null);
    const rangeFormat = ref("YYYY-MM-DD");
    const chartOption1 = ref({});
    const chartOption2 = ref({});
    const chartOption3 = ref({});
    const chartOption4 = ref({});
    const chartOption5 = ref({});
    const formModel = ref({});

    const entity = ref({
      interval: "1h",
      dateFormat: "yyyy-MM-dd HH:mm"
    });

    const getDate = (past) => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - past);
      return pastDate;
    };

    const getLegend = (parsedData) => {
      if (parsedData.bars) {
        return Object.keys(parsedData.bars);
      } if (parsedData.lines) {
        return Object.keys(parsedData.lines);
      }
      return [];

    }

    const parse = (title, parsedData) => {
      // ECharts图表数据格式
      const option = {
        title: [
          {
            text: title,
            left: 'center'
          }
        ],
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          bottom: 10,
          data: getLegend(parsedData)
        },
        xAxis: {
          type: 'category',
          data: parsedData.xAxis
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };

      if (parsedData.lines) {
        Object.keys(parsedData.lines).forEach(name => {
          option.series.push({
            data: parsedData.lines[name],
            type: 'line',
            name
          })
        });
      }
      if (parsedData.bars) {
        Object.keys(parsedData.bars).forEach(name => {
          option.series.push({
            data: parsedData.bars[name],
            type: 'bar',
            name
          })
        });
      }
      return option;
    }

    const onChangeInterval = () => {
      switch (entity.value.interval) {
        case '1d':
          entity.value.dateFormat = 'yyyy-MM-dd';
          break;
        case '1h':
          entity.value.dateFormat = 'yyyy-MM-dd hh:mm';
          break;
        default:
      }
    }

    const render1 = async () => {
      const data = await chartRender({
        index: `ma_${tenantId}_${buCode}_fact_*`,
        query: "taskType eq flow_content",
        rangeQuery: "timestamp",
        startTime: timeRange.value[0],
        endTime: timeRange.value[1],
        interval: entity.value.interval,
        dateFormat: entity.value.dateFormat,
        bar: "taskType"
        // bar: "nodeConfigId"
      });
      // return parse('触达吞吐量', data);
      return parse(t('analysis.reachThroughput'), data);
    };

    const render2 = async () => {
      const data = await chartRender({
        index: `ma_${tenantId}_${buCode}_fact_*`,
        query: "taskType eq flow_content",
        rangeQuery: "timestamp",
        startTime: timeRange.value[0],
        endTime: timeRange.value[1],
        interval: entity.value.interval,
        dateFormat: entity.value.dateFormat,
        bar: "nodeConfigId"
        // bar: "nodeConfigId"
      });
      // return parse('触达渠道', data);
      return parse(t('analysis.reachChannel'), data);

    };

    const render3 = async () => {
      const data = await chartRender({
        index: `ma_${tenantId}_${buCode}_fact_*`,
        query: "taskType eq flow_content",
        rangeQuery: "timestamp",
        startTime: timeRange.value[0],
        endTime: timeRange.value[1],
        interval: entity.value.interval,
        dateFormat: entity.value.dateFormat,
        // line: "nodeConfigId",
        bar: "reachStatus"
      });
      // return parse('触达状态', data);
      return parse(t('analysis.reachStatus'), data);
    };

    const render4 = async () => {
      const data = await chartRender({
        index: `ma_${tenantId}_${buCode}_fact_*`,
        query: "taskType eq flow_content",
        rangeQuery: "timestamp",
        startTime: timeRange.value[0],
        endTime: timeRange.value[1],
        interval: entity.value.interval,
        dateFormat: entity.value.dateFormat,
        // line: "nodeConfigId",
        bar: "reachTemplate"
      });
      return parse('触达模板', data);
    };

    const render5 = async () => {
      const data = await chartRender({
        index: `ma_${tenantId}_${buCode}_fact_*`,
        query: "taskType eq flow_content",
        rangeQuery: "timestamp",
        startTime: timeRange.value[0],
        endTime: timeRange.value[1],
        interval: entity.value.interval,
        dateFormat: entity.value.dateFormat,
        // line: "nodeConfigId",
        bar: "groupId"
      });
      return parse('活动触达次数', data);
    };

    const search = async () => {
      chartOption1.value = await render1();
      chartOption2.value = await render2();
      chartOption3.value = await render3();
      chartOption4.value = await render4();
      chartOption5.value = await render5();
    };

    const bindData = async () => {
      timeRange.value = [getDate(1), getDate(0)];
      search();
    }
    const setup = {
      module,
      entity,
      bindData,
      formModel,
      chartOption1,
      chartOption2,
      chartOption3,
      chartOption4,
      chartOption5,
      interval,
      timeRange,
      rangeFormat,
      onChangeInterval,
      search
    };

    provide("view", setup);
    return setup;
  }
}
</script>

<style scoped lang="less">
.filter-form {
  margin: 15px;
}

.chart-list {
  width: 99%;
  margin: 10px;
}

.chart {
  margin: 15px;
}
</style>
