<template>
  <BasicNode />
</template>

<script>
import { provide } from "vue";
import { shortDatetime, shortDate } from "@/utils/date"
import mixin from "../../components/mixin/node";
import BasicNode from "../BasicNode.vue";

export default {
  components: {
    BasicNode,
  },
  mixins: [mixin],
  inject: {
    node: {
      default: null,
    },
  },
  setup() {
    const getBrief = async (data) => {
      const brief = [];
      if (!data.schedule) {
        return brief;
      }
      if (data.schedule.type == "DATE") {
        brief.push({ sort: 0, label: "执行时间", value: shortDatetime(data.schedule.date) });
      } else if (data.schedule.type == "CRON") {
        brief.push({
          sort: 0,
          label: "执行周期",
          value:
            `${shortDate(data.schedule.startTime)
            } 至 ${
            shortDate(data.schedule.startTime)}`,
        });
        brief.push({ sort: 0, label: "执行时间", value: data.schedule.cron });
      } else if (data.schedule.type == "DELAY" && data.schedule.duration) {
        let {duration} = data.schedule;
        duration = duration
          .replaceAll("P", "")
          .replaceAll("T", "")
          .replaceAll("D", "天")
          .replaceAll("H", "小时")
          .replaceAll("M", "分")
          .replaceAll("S", "秒");
        brief.push({ sort: 0, label: "延迟时间", value: duration });
      }

      return brief;
    };
    const nodeApi = JSON.parse(localStorage.getItem('nodeApi'))
    const item = nodeApi.find(item => { return item.type === 'timer' })
    const setup = {
      title: item.name || "定时",
      summary: item.name || "定时节点",
      iconClass: item.icon || "icon-timing",
      nodeClass: "easyflow-pannel-timer",
      headerColor: item.themeColor || "#105a63",
      headerBgColor: item.themeColor || "#9b69ff",
      background: item.background || "#f9f5ff",
      getBrief,
    };
    provide("node", setup);
    return setup;
  },
  data() {
    return {};
  },
};
</script>
