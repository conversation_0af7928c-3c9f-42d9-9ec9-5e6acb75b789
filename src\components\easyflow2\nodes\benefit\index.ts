import { Graph } from "@antv/x6";
import { getPorts, rect } from "../../components/node";
import BenefitNode from "./node.vue";
import BenefitPannel from "./pannel.vue";

const nodeData = {
  type: "benefit",
  node: {
    inherit: "vue-shape",
    ...rect,
    component: {
      template: `<BenefitNode />`,
      components: {
        BenefitNode,
      },
    },
    ports: getPorts(true, true, true, true),
  },
};
const registerNode = () => {
  Graph.registerNode("BenefitNode", nodeData.node, true);
};

const Benefit = {
  type: "benefit",
  name: "权益",
  shape: "BenefitNode",
  iconClass: "icon-license",
  color: "#ffffff",
  themebg: "#4594f3",
  registerNode: registerNode,
  pannel: BenefitPannel,
  skippable: true,
  auth: [
    "export_task_record"
  ]
};

export default Benefit;
